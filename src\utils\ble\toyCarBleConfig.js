/**
 * 玩具车蓝牙配置文件
 * 只包含基本的默认配置参数
 */

/**
 * 默认配置参数
 */
export const DEFAULT_CONFIG = {
  channel: '37',                    // 默认 BLE 信道
  address: 'CC CC CC CC CC',        // 默认设备地址 Mac地址
  deviceName: 'TEMP',               // 默认设备名称（与实际蓝牙设备名称一致）
  responseTimeout: 3000,            // 设备响应超时时间（毫秒）
}

/**
 * 档位速度映射
 */
export const GEAR_SPEED_MAP = {
  1: 0x1E,  // 1档 - 30%
  2: 0x32,  // 2档 - 50%
  3: 0x4B   // 3档 - 75%
}

/**
 * 获取当前配置
 */
export function getCurrentConfig() {
  try {
    return {
      channel: wx.getStorageSync('channel') || DEFAULT_CONFIG.channel,
      address: wx.getStorageSync('address') || DEFAULT_CONFIG.address,
      deviceName: wx.getStorageSync('deviceName') || DEFAULT_CONFIG.deviceName,
      responseTimeout: wx.getStorageSync('responseTimeout') || DEFAULT_CONFIG.responseTimeout,
    }
  } catch (error) {
    console.error('❌ 读取配置失败:', error)
    return DEFAULT_CONFIG
  }
}

/**
 * 保存配置到本地存储
 */
export function saveConfig(config) {
  try {
    Object.keys(config).forEach(key => {
      if (config[key] !== undefined) {
        wx.setStorageSync(key, config[key])
      }
    })
    console.log('✅ 配置保存成功:', config)
    return true
  } catch (error) {
    console.error('❌ 配置保存失败:', error)
    return false
  }
}

/**
 * 🎯 从API返回的订单信息中更新蓝牙配置
 * @param {Object} orderData - getUserOrderByNoAll API返回的订单数据
 */
export function updateConfigFromOrderData(orderData) {
  try {
    if (!orderData) {
      console.warn('⚠️ 订单数据为空，使用默认配置')
      return false
    }

    const newConfig = {}
    let hasChanges = false

    // 🎯 从订单数据中提取蓝牙配置
    if (orderData.bind_ble_device) {
      // 🎯 验证设备名称格式
      const deviceName = String(orderData.bind_ble_device).trim()
      if (deviceName) {
        newConfig.deviceName = deviceName
        hasChanges = true
        console.log('📱 更新设备名称:', deviceName)
      } else {
        console.warn('⚠️ 设备名称为空字符串，使用默认配置')
      }
    } else {
      console.log('ℹ️ 设备名称参数为空，使用默认配置')
    }

    if (orderData.bind_ble_mac) {
      // 🎯 验证和格式化MAC地址
      const macAddress = String(orderData.bind_ble_mac).trim()
      if (macAddress) {
        // 🎯 如果是UUID格式，直接使用；如果是MAC格式，保持原样
        newConfig.address = macAddress
        hasChanges = true
        console.log('🔗 更新设备地址:', macAddress)
      } else {
        console.warn('⚠️ 设备地址为空字符串，使用默认配置')
      }
    } else {
      console.log('ℹ️ 设备地址参数为空，使用默认配置')
    }

    // 🎯 添加其他可能的配置字段
    if (orderData.ble_channel) {
      const channel = String(orderData.ble_channel).trim()
      if (channel) {
        newConfig.channel = channel
        hasChanges = true
        console.log('📡 更新BLE信道:', channel)
      }
    }

    // 🎯 保存更新的配置
    if (hasChanges) {
      const success = saveConfig(newConfig)
      if (success) {
        console.log('✅ 蓝牙配置已从订单信息更新:', newConfig)

        // 🎯 触发配置更新事件，通知其他模块重新加载配置
        try {
          uni.$emit('ble-config-updated', newConfig)
          console.log('📢 已发送配置更新事件')
        } catch (eventError) {
          console.warn('⚠️ 发送配置更新事件失败:', eventError)
        }

        return true
      } else {
        console.error('❌ 蓝牙配置更新失败')
        return false
      }
    } else {
      console.log('ℹ️ 没有需要更新的配置项')
      return true
    }

  } catch (error) {
    console.error('❌ 更新蓝牙配置失败:', error)
    return false
  }
}

/**
 * 🎯 验证蓝牙配置是否有效
 * @param {Object} config - 要验证的配置对象
 * @returns {boolean} 配置是否有效
 */
export function validateConfig(config) {
  try {
    if (!config || typeof config !== 'object') {
      return false
    }

    // 验证设备名称
    if (config.deviceName && typeof config.deviceName !== 'string') {
      console.warn('⚠️ 设备名称格式无效')
      return false
    }

    // 验证设备地址
    if (config.address && typeof config.address !== 'string') {
      console.warn('⚠️ 设备地址格式无效')
      return false
    }

    // 验证信道
    if (config.channel && typeof config.channel !== 'string') {
      console.warn('⚠️ BLE信道格式无效')
      return false
    }

    return true
  } catch (error) {
    console.error('❌ 配置验证失败:', error)
    return false
  }
}
