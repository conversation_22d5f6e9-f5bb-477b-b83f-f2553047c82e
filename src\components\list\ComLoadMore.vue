<template>
  <u-loadmore
    :status="status[loadingType]"
    :icon="icon"
    :margin-top="marginTop"
    :margin-bottom="marginBottom"
    :load-text="loadText"
  ></u-loadmore>
</template>

<script>
export default {
  name: "ComLoadMore",
  props: {
    loadingType: {
      //上拉的状态：0-loading前；1-loading中；2-没有更多了
      type: Number || String,
      default: 0,
    },
  },
  data() {
    return {
      icon: true,
      iconType: "circle",
      marginTop: "20",
      marginBottom: 20,
      loadText: {
        loadmore: "轻轻上拉,加载更多",
        loading: "正在加载中,请喝杯茶",
        nomore: "我也是有底线的",
      },
      status: ["loadmore", "loading", "nomore"],
    };
  },
};
</script>

<style lang="scss" scoped>
</style>