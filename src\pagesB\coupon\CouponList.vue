<template>
    <view>
        <ComList :loadingType="loadingType">
            <view class="list">
                <view class="list-item" v-for="item in listData" :key="item.id">
                    <CouponListCard :info="item" />
                </view>
            </view>
        </ComList>
    </view>
</template>
<script>
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import { getCoupon } from "@/common/http/api"
import CouponListCard from "./CouponListCard.vue"
export default {
    components: { ComList, CouponListCard },
    mixins: [myPull()],
    data() {
        return {};
    },
    methods: {
        getList(page, done) {
            let data = {
                page,
                limit: 10,
                type: ''
            }
            getCoupon(data).then(res => {

                done(res.list)
            })
        }
    },
    onLoad() {
        this.refresh()
    },

}
</script>


<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>

<style scoped  lang='scss'>
.list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    &-item {
        width: 48%;
        flex-shrink: 0;
    }
}
</style>