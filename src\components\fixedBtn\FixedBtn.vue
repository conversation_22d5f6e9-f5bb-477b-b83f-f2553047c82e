<template>
    <view class='fixed'>
        <view class="box">
            <view class="total-box">
                <view>合计：￥</view>

                <view class="price">{{ price }}</view>
                <!-- <view>元</view> -->
            </view>
            <view class="btn">
                <BaseButton :text="btnTitle" width="200rpx" shape="circle" @onClick="click" />
            </view>
        </view>
        <SafeBlock />
    </view>
</template>
<script>
import BaseButton from '../base/BaseButton.vue';
import SafeBlock from '../list/SafeBlock.vue';
export default {
    name: 'FixedBtn',
    props: {
        price: { type: Number | String, default: 0 },
        btnTitle: { type: String, default: '去支付' },
    },
    components: { BaseButton, SafeBlock },

    data() {
        return {

        };
    },

    methods: {
        click() {
            this.$emit('click')
        }
    },
}
</script>


<style scoped  lang='scss'>
.fixed {

    background-color: #fff;

    .box {
        @include flexRowBetweenColCenter();
        padding: 10px 20px;

        .total-box {
            @include flexAllcenter();

            .price {
                color: red;
            }
        }


    }
}
</style>