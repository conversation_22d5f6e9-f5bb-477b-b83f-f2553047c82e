<template>
  <view class="content">
    <LoginPopup :isNeedAuth="isLogin" />
    <!-- #ifndef H5 -->
    <BaseNavbar title="个人中心" bgColor="#0781ce" color="#fff" />
    <!-- #endif -->
    <view class="top">
      <image class="topBg" src="../static/icon/ic_profile_top_bg.png" />
      <view class="titleBar">
        <view class="user-box">
          <view class="user-box-info">
            <view class="user-box-info-img">
              <!-- #ifndef MP-WEIXIN -->
              <image class="avatar" :src="vMemberInfo.headimgurl || '/pagesC/static/icon/ic_def_avatar.png'
                " @click="getUserProfile" />
              <!-- #endif -->
              <!-- #ifdef MP-WEIXIN -->
              <button class="avatar-wrapper avatar_btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                <image class="avatar" :src="avatarUrl || '/pagesC/static/icon/ic_def_avatar.png'"></image>
              </button>
              <!-- #endif -->
              <!-- <view class="bind-mobile">绑定手机号</view> -->

            </view>
            <view class="user-box-info-name">
              <!-- #ifndef MP-WEIXIN -->
              <view class="nickName" @click="getUserProfile">
                {{ vMemberInfo.user_nickname || "点击授权登录" }}</view>
              <!-- #endif -->
              <!-- #ifdef MP-WEIXIN -->
              <view class="vip_box">
                <image class="vip_img" src="../static/icon/vip.png"></image>
                <input type="nickname" class="nickName" @blur="blurname" v-model="username" placeholder="请输入昵称">
              </view>

              <!-- #endif -->
              <!-- <view class="bind-mobile">绑定手机号</view> -->
            </view>
          </view>
          <view class="user-box-set" @click="showTips" v-if="false">
            <view class="setting">
              <BaseIcon name="setting" color="#fff" />
            </view>
            <view class="bell">
              <BaseIcon name="bell" color="#fff" />
            </view>
          </view>
        </view>
        <view class="assets">
          <view class="assets-box" @click="go(`/pagesD/wallet/index`)">
            <view>{{ vMemberInfo.cash || "0.00" }}</view>
            <view>余额</view>
          </view>
          <view class="assets-line"></view>
          <view class="assets-box" @click="go(`/pagesB/userCoupon/index`, true)">
            <view>{{ vMemberInfo.coupons_num || 0 }}</view>
            <view>优惠券</view>
          </view>
          <view class="assets-line"></view>
          <view class="assets-box" @click="go(`/pagesD/scoreTask/ScoreRecord`, true)">
            <view>{{ vMemberInfo.point || "0" }}</view>
            <view>积分</view>
          </view>
        </view>
      </view>
    </view>
    <view class="menuList">
      <!-- #ifdef MP-WEIXIN -->
      <view class="member" @click="
        go(`/pagesD/wallet/myWallet`)
        ">
        <view class="member-left">
          <image class="member-icon" src="/pagesC/static/icon/member_icon.png" />
          <view class="equity">
            <view class="equity_1">会员可享多项权益</view>
            <view class="equity_2">{{
              vMemberInfo.is_paid_member === 1 ? "已开通会员" : "未开通会员"
            }}</view>
          </view>
        </view>
        <view class="member-right">{{ vMemberInfo.is_paid_member === 1 ? "已激活会员" : "立即激活" }}
        </view>
      </view>
      <!-- #endif -->
      <view class="card" v-for="item in cardList" :key="item.title" :style="{ height: item.cardHeight }">
        <view class="card-title">{{ item.title }}</view>
        <view class="card-list">
          <view class="card-list-item" v-for="items in item.list" :key="items.id" @click="onClickProfileMenuItem(items)">
            <view class="iconBox">
              <image class="icon" :style="items.style" :src="items.icon"></image>
            </view>
            <view class="card-list-item-title">{{ items.title }}</view>
          </view>
          <!-- <view style="flex: 20%"></view> -->
          <!-- <view style="flex: 20%" v-for="item in 3" :key="item"></view> -->
        </view>
      </view>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <CommonAd :ad="vAd.personalCenterBannerAd||''" type="banner" />
    <!-- #endif -->
    <view :style="{
      width: '425rpx',
      height: '98rpx',
      margin: '50rpx auto 0',
      boxSizing: 'border-box',
    }">
      <!-- <image
        src="/pagesC/static/icon/login-ad.png"
        :style="{ width: '100%', height: '100%' }"
      /> -->
    </view>

    <SafeBlock height="20" />
  </view>
</template>
<script>
import BaseIcon from "../../components/base/BaseIcon.vue";
import BaseNavbar from "../../components/base/BaseNavbar.vue";
import SafeBlock from "../../components/list/SafeBlock.vue";
import LoginPopup from "../../components/LoginPopup.vue";
import CommonAd from "../../components/WxAd/CommonAd.vue";
export default {
  components: { BaseNavbar, BaseIcon, SafeBlock, LoginPopup, CommonAd },
  data() {
    return {
      cardList: [
        {
          title: "订单中心",
          list: [
            {
              icon: require("../static/icon/ic_profile_order.png"),
              title: "我的订单",
              id: 101,
              url: `/pagesB/order/Order`,
              style: "width:42rpx;height:48rpx;",
            },
            // {
            //   icon: require("../static/icon/invest_order.png"),
            //   title: "认养记录",
            //   id: 102,
            //   url: `/pagesB/investOrder/index`,
            //   style: "width:48rpx;height:48rpx;",
            // },
            // {
            //   icon: require("../static/icon/ic_profile_shop.png"),
            //   title: "商城订单",
            //   id: 103,
            //   url: `/pagesB/marketOrder/index`,
            //   style: "width:48rpx;height:48rpx;",
            // },
            // {
            //   icon: require("../static/icon/ic_profile_ad.png"),
            //   title: "广告订单",
            //   id: 104,
            //   url: `/pagesB/advertiseOrder/index`,
            //   style: "width:48rpx;height:48rpx;",
            // },
          ],
        },
        {
          title: "我的服务",
          cardHeight: "350rpx",
          list: [
            /* #ifdef MP-WEIXIN */
            {
              icon: require("../static/icon/ic_profile_wallet.png"),
              title: "我的钱包",
              url: `/pagesD/wallet/index`,
              id: 201,
              style: "width:50rpx;height:46rpx;",
            },
            /* #endif */
            /* #ifdef MP-WEIXIN */
            {
              id: 202,
              icon: require("../static/icon/ic_profile_companion.png"),
              title: "成为合伙人",
              url: `/pagesB/partner/index`,
              style: "width:55rpx;height:46rpx;",
            },
            /* #endif */
            // {
            //   icon: require("../static/icon/ic_profile_score.png"),
            //   title: "积分任务",
            //   id: 203,
            //   url: `/pagesD/scoreTask/index`,
            //   style: "width:48rpx;height:48rpx;",
            // },
            // {
            //   icon: require("../static/icon/ic_profile_help.png"),
            //   title: "帮助中心",
            //   id: 204,
            //   url: `/pagesC/help/index`,
            //   style: "width:49rpx;height:44rpx;",
            // },
            {
              icon: require("../static/icon/ic_profile_about_us.png"),
              title: "关于我们",
              id: 205,
              url: `/pagesC/about/index`,
              style: "width:48rpx;height:48rpx;",
            },
          ],
        },
      ],
      isLogin: false,
      username: '',
      avatarUrl:''
    };
  },

  methods: {
    onChooseAvatar(e){
   
      if (e.detail.avatarUrl&&e.detail.avatarUrl!=this.vMemberInfo.headimgurl) {
        let userinfo = {
          avatarUrl: e.detail.avatarUrl,
          user_nickname:this.username
        }
        // console.log('微信头像', e)
        uni.$emit('updataUser', userinfo)
        this.avatarUrl = e.detail.avatarUrl
      }
    },
    blurname(e) {
      // console.log('微信昵称', e)
      if (e.detail.value&&e.detail.value!=this.vMemberInfo.user_nickname) {
        let userinfo = {
          user_nickname: e.detail.value,
          avatarUrl:this.vMemberInfo.headimgurl
        }
        uni.$emit('updataUser', userinfo)
        this.username = e.detail.value
      }
    },
    go(url, boolear) {
      if (boolear) {
        return this.showTips();
      }
      uni.navigateTo({ url });
      // this.showTips();
    },
    showTips() {
      uni.showToast({
        title: "即将上线,敬请期待~",
        icon: "none",
      });
    },
    getUserProfile() {
      // console.log("--->用户信息", this.vMemberInfo.headimgurl);
      //登录过我就返回
      /* #ifdef H5*/
      return uni.showToast({
        title: "请登录小程序~",
        icon: "none",
      });
      /* #endif */
      /* #ifndef  MP-WEIXIN*/
      if (this.vMemberInfo.headimgurl) return;

      this.isLogin = true;
      /* #endif */
    },
    onClickProfileMenuItem(item) {
      const { url } = item;
      uni.navigateTo({ url });
    },
  },
  // onLoad() {
  //   this.vMemberInfo.avatarUrl && this.getUserInfoHandle();
  //    this.getInfo()
  //   console.log('info', this.vMemberInfo)
  // },
  watch: {
    vMemberInfo: {
      handler(newValue, oldValue) {
        // 在这里执行当 vMemberInfo 变化时的逻辑
        // console.log('vMemberInfo 发生变化', newValue?.user_nickname, oldValue?.user_nickname);
        if(newValue?.user_nickname!=oldValue?.user_nickname){
          this.username = newValue?.user_nickname || '';
        }
        if(newValue?.headimgurl!=oldValue?.headimgurl){
          this.avatarUrl=newValue?.headimgurl
        }
        
       
      },
      deep: true, // 深度监听对象内部属性的变化
      immediate: true // 立即执行监听函数，确保在组件加载时能够正确初始化值
    }
  },
};
</script>


<style scoped  lang='scss'>
.vip_box {
  display: flex;
}
.vip_img {
  width: 35rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.content {
  .top {
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 380rpx;

    .topBg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
    }

    .titleBar {
      padding: 32rpx 28rpx;

      .user-box {
        display: flex;
        justify-content: space-between;

        &-info {
          display: flex;
          justify-content: center;
          align-content: center;
          .avatar_btn{
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            padding: 0;
            border-radius: 50%;
          }
          .avatar {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
          }

          &-name {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 20rpx;

            .nickName {
              font-size: $font-size-large !important;
              color: white;
            }

            .bind-mobile {
              text-align: center;
              border-radius: 18rpx;
              font-size: $font-size-base;
              margin-top: 20rpx;
              color: white;
              background-color: #cbcbcc;
            }
          }
        }

        &-set {
          display: flex;
          justify-content: center;
          margin-top: 16rpx;

          .setting,
          .bell {
            margin-right: 24rpx;
          }
        }
      }

      .assets {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        padding: 0 72rpx;
        margin-top: 28rpx;

        &-box {
          display: flex;
          flex-direction: column;
          align-items: center;

          >view {
            &:nth-child(1) {
              font-size: 40rpx;
            }

            &:nth-child(2) {
              font-size: $font-size-small;
              margin-top: 10rpx;
            }
          }
        }

        &-line {
          width: 2rpx;
          height: 30rpx;
          background-color: #fff;
        }
      }
    }
  }

  .menuList {
    padding: 0 30rpx;

    .member {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 132rpx;
      border-radius: 10rpx;
      padding: 34rpx;
      box-sizing: border-box;
      margin-top: -66rpx;
      background-color: #f9d1a0;

      &-left {
        display: flex;
        justify-content: center;
        align-items: center;

        .member-icon {
          width: 56rpx;
          height: 58rpx;
        }

        .equity {
          color: #94481c;
          margin-left: 30rpx;

          &_1 {
            font-size: $font-size-base;
          }

          &_2 {
            font-size: $font-size-xsmall;
            margin-top: 20rpx;
          }
        }
      }

      &-right {
        padding: 14rpx;
        color: #c47a46;
        font-size: $font-size-base;
        border-radius: 28rpx;
        background-color: #fff;
      }
    }

    .card {
      margin-top: 16rpx;
      padding: 34rpx 30rpx;
      border-radius: 10rpx;
      color: #333;
      box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
      background: #fff;

      &-title {
        font-weight: 700;
        font-size: $font-size-large;
      }

      &-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        &-item {
          // flex: 22%;
          width: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-top: 50rpx;

          &-title {
            color: #333333;
            font-size: $font-size-small;
          }
        }
      }
    }
  }

  .adContainer {
    margin-top: 30rpx;
  }
}
</style>