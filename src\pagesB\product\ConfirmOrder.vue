<template>
  <!--确认订单页面-->
  <view class="content">
    <view class="topInfo">
      <view class="pointPlaceInfo">
        <PointDetails :pointPlaceInfo="vPointInfo" :bBleConnected="bBleConnected" />
      </view>
    </view>

    <view class="tips">
      <view class="tips-tltle">温馨提示：</view>
      <view class="tips-list">
        <view>1、支付成功后请点击完成,否则可能失败</view>
        <view v-if="bleTypeHandle == globalCodes.orderGategory.CHARGE">
          2、如付款后未充电,请重新扫描二维码</view>
      </view>
    </view>

    <BasePopup :show.sync="isShowPopup">
      <PayWayPopup @selectPayWay="selectPayWay" :isShowBalanceType="isHaveCash" :balance="vMemberInfo.cash" />
    </BasePopup>
    <view class="fixed-btn">
      <FixedBtn @click="clickPay" :price="price || 0" :btnTitle="btnTitle" />
    </view>
  </view>
</template>
<script>
import FixedBtn from "@/components/fixedBtn/FixedBtn.vue";
import PointDetails from "@/components/PointDetails.vue";
import BasePopup from "@/components/base/BasePopup.vue";
import PayWayPopup from "@/components/PayWayPopup.vue";
import { payOrder } from "@/utils/pay";
import { prepay, pointOrderOut, payBalance,getUserInfo } from "@/common/http/api";
import { bleMixin } from "@/mixins/bleMixin.js";
import { globalEvents } from "@/global/globalEvents";
import { globalCodes } from "@/global/globalCodes";
export default {
  components: { PointDetails, FixedBtn, BasePopup, PayWayPopup },
  data() {
    return {
      isShowPopup: true,
      btnTitle: "去支付",
      price: "0",
      order_amount: 0, // 要支付的订单金额
      bBleConnected: false, //蓝牙是否连接
      bleTypeHandle: globalCodes.orderGategory.CHARGE,
      charge_sn: "",
      getWay: globalCodes.getWay.PAY_MONEY,
      isHaveCash: false,
    };
  },
  mixins: [bleMixin],
  methods: {
    getInfo() {
      getUserInfo().then((res) => {
        uni.$u.vuex('vMemberInfo', res)
      });
    },
    clickPay() {
      if (this.vIsBleDevice && !this.bBleConnected) {
        return this.startBleConnect();
      }
      if (this.getWay == globalCodes.getWay.POINT_EXCHANGE) {
        this.onClickPointExchange();
      } else {
        /* #ifdef MP-TOUTIAO */
        //直接调用抖音收银台
        this.requestPay();
        /* #endif */
        /* #ifndef MP-TOUTIAO */
        this.isShowPopup = true;
        /* #endif */
      }
    },
    selectPayWay(e) {
      console.log();
      if (e?.id == 1) {
        console.log("钱包余额支付");
        if (this.balance < this.order_amount)
          return uni.showToast({
            title: "余额不足,请选择其他支付方式~",
            icon: "none",
          });
        this.requestPay(globalCodes.getWay.PAY_BALANCE);
      } else if (e?.id == 2) {
        console.log("开始调用支付");
        this.requestPay();
      }
    },
    doPayBalance() {
      const { orderInfo, curSelMeal } = this.vCreateOrderInfo;
      // console.log("订单信息 1 ", this.vCreateOrderInfo);
      // console.log("订单信息2 ", orderInfo);
      let data;

      if (this.bleTypeHandle === globalCodes.orderGategory.CHARGE) {
        data = {
          order_sn: orderInfo.charge_sn,
          amount: this.order_amount,
          order_type: globalCodes.orderTypeService.ORDER_CHARGE, // 充电订单
        };
      } else {
        data = {
          order_sn: orderInfo.order_sn,
          amount: this.order_amount,
          order_type: globalCodes.orderTypeService.ORDER_COMMON,  // 普通订单
        };
      }
      if (this.vCreateOrderInfo.orderInfo.vip_price * 1) {
        data['amount'] = this.vCreateOrderInfo.orderInfo.vip_price
      }
      payBalance(data).then((res) => {
        
       
        if (this.vCreateOrderInfo.orderInfo.vip_price * 1) {
          this.vMemberInfo.cash = (this.vMemberInfo.cash - this.vCreateOrderInfo.orderInfo.vip_price).toFixed(2);
        }else{
          this.vMemberInfo.cash = (this.vMemberInfo.cash - this.order_amount).toFixed(2);
        }
        if (this.vIsBleDevice) {
          if (this.bBleConnected) {
            this.onRecharge(curSelMeal.time);
          } else {
            console.log("连接蓝牙");
            this.isChargeFlag = true;
            this.startBleConnect();
          }
        } else {
          let url = `/pagesB/order/OrderDetails?order_sn=${orderInfo.order_sn}`;
          console.log("跳转", url);
          uni.redirectTo({
            url
          });
        }
      });
    },
    requestPay(payType = globalCodes.getWay.PAY_MONEY) {
      console.log("开始调用支付requestPay （）", payType);
      if (this.bleTypeHandle === globalCodes.orderGategory.CHARGE) {
        const { payInfo, curSelMeal, orderInfo } = this.vCreateOrderInfo;
        console.log("🚀 ~ payInfo", payInfo);
        this.charge_sn = orderInfo?.charge_sn;
        if (payType == globalCodes.getWay.PAY_BALANCE) { // 余额支付
          this.doPayBalance();
        } else {
          payOrder(
            this,
            payInfo,
            `/pagesB/order/OrderDetails?order_sn=${orderInfo?.order_sn}`,
            this.vIsBleDevice ? this.paySuccessCallback : null
          );
        }
      } else {
        let params = {
          order_sn: this.vCreateOrderInfo?.orderInfo?.order_sn,
          device_sn: this.vCreateOrderInfo?.pointPlaceInfo?.device_sn,
        };
        if (payType == globalCodes.getWay.PAY_BALANCE) { // 余额支付
          this.doPayBalance();
        } else {
          prepay(params).then((res) => {
            console.log("🚀 ~ res", res);
            let payInfo = res.data;
            //请求支付
            payOrder(
              this,
              payInfo,
              `/pagesB/order/OrderDetails?order_sn=${this.vCreateOrderInfo?.orderInfo?.order_sn}`,
              this.vIsBleDevice ? this.paySuccessCallback : null
            );
          });
        }

      }
    },
    //支付成功回调
    paySuccessCallback() {
      if (this.bleTypeHandle === "charge") {
        this.onRecharge(this.vCreateOrderInfo.curSelMeal.time);
      } else {
        this.openBleLock(false, this.vCreateOrderInfo?.orderInfo?.channel);
      }
    },
    //积分兑换
    //点击了确认兑换
    onClickPointExchange() {
      let params = {
        order_sn: this.vCreateOrderInfo.orderInfo.order_sn,
      };
      pointOrderOut(params).then((res) => {
        if (res?.prepay_info) {
          this.isShowSuccess("兑换成功", 0, () => {
            uni.redirectTo({
              url: `/pagesB/order/OrderDetails?order_sn=${this.vCreateOrderInfo?.orderInfo?.order_sn}`,
            });
          });
        } else {
          this.isShowErr("兑换失败~");
        }
      });
    },
  },
  onLoad(opt) {
    console.log("🚀 ~ opt", opt);
    //1是立即购买 2是积分兑换
    this.getInfo()
    this.getWay = opt?.getWay ?? globalCodes.getWay.PAY_MONEY;
    this.bleTypeHandle = this.vCreateOrderInfo?.bleTypeHandle;
    // console.log("🚀 ~ this.vCreateOrderInfo", this.vCreateOrderInfo);
    // console.log("🚀 ~ this.bleTypeHandle", this.bleTypeHandle);
    // console.log('---> 会员的信息  ', this.vMemberInfo);
    this.isHaveCash = this.vMemberInfo.is_paid_member==1;
    if (this.getWay == globalCodes.getWay.POINT_EXCHANGE) {
      this.btnTitle = "确认兑换";

      this.price =
        (this.vCreateOrderInfo.curSelMeal.exchange_integral ?? 0) + "积分";
      // console.log("🚀 ~  this.price", this.price);
    } else {
      this.btnTitle = "去支付";
      // console.log('---> 订单信息 ', this.vCreateOrderInfo);
      // console.log('---> 会员的余额  ', this.isHaveCash);

      if (this.bleTypeHandle === "charge") {
        this.order_amount = this.vCreateOrderInfo.orderInfo.amount;
        this.price = this.vCreateOrderInfo.orderInfo.amount + " 元";
      } else {
        this.order_amount = this.vCreateOrderInfo.orderInfo.order_price;
        this.price = this.vCreateOrderInfo.orderInfo.order_price + " 元";
      }
    }
    this.bBleConnected = getApp().globalData.connected;
    uni.$off(globalEvents.EVENT_BLE_CONNECT_CHANGED);
    uni.$on(globalEvents.EVENT_BLE_CONNECT_CHANGED, (e) => {
      console.log("home，收到蓝牙连接状态改变事件，是否连接：", e.connected);
      getApp().globalData.connected = e.connected;
      this.bBleConnected = e.connected;
      if (e.connected) {
        //微信支付
        this.requestWxPay();
      }
    });
  },
};
</script>


<style  lang='scss'>
page {
  background-color: $pageBgColor;
}

.content {
  position: relative;

  .topInfo {
    padding: 30rpx;
    background-color: white;

    wx-ad {}

    .pointPlaceInfo {
      margin-top: 50rpx;

      .name {
        font-size: $font-size-xlarge;
        color: $textBlack;
        font-weight: bold;
        margin-bottom: 40rpx;
      }

      .line {
        margin-bottom: 25rpx;
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .icon {
          width: 23rpx;
          height: 23rpx;
        }

        .label {
          color: #666666;
          font-size: $font-size-xsmall;
          margin-left: 10rpx;
        }
      }

      .deviceType {
        margin-top: 40rpx;
        margin-bottom: 30rpx;

        .imgType {
          width: 20rpx;
          height: 26rpx;
        }

        .textType {
          margin-left: 10rpx;
          font-size: $font-size-xsmall;
          color: $themeColor;
        }
      }
    }
  }

  .bottomBar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    padding: 20rpx 30rpx;

    .totalWrap {
      .label {
        font-size: $font-size-xsmall;
        color: $textDarkGray;
      }

      .value {
        margin-left: 20rpx;
        color: $mainRed;
        display: flex;
        align-items: flex-end;

        .money {
          font-size: 46rpx;
          font-weight: bold;
        }

        .unit {
          font-size: $font-size-xsmall;
          margin-left: 5rpx;
          margin-bottom: 5rpx;
        }
      }
    }

    .btnPay {
      margin-left: auto;

      main-button {}
    }
  }

  .safeArea {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 99;
  }
}

.tips {
  padding: 20rpx;

  &-tltle {
    color: #666;
    font-size: $font-size-middle;
    font-weight: 700;
  }

  &-list {
    color: #999;
    font-size: $font-size-base;

    >view {
      line-height: 1.8;
    }
  }
}

.point {
  padding: 30rpx;
  background-color: #fff;
}

.fixed-btn {
  @include fixedBottom();
}
</style>