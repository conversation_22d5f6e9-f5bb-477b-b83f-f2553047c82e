import moment from 'moment'

//格式化时间戳
export function formatDate(timestamp) {
    return moment(timestamp).format('YYYY-MM-DD')
}

export function formatTime(timestamp) {
    return moment(timestamp).format('HH:mm:ss')
}

export function formatTimeToMin(timestamp) {
    return moment(timestamp).format('HH:mm')
}

export function formatDateTime(timestamp) {
    return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

export function formatDateTime2(timestamp) {
    return moment(timestamp).format('YYYY/MM/DD HH:mm:ss')
}

//精确到分
export function formatDateTimeToMin(timestamp) {
    return moment(timestamp).format('YYYY-MM-DD HH:mm')
}

//月日时分-2月23日 16:00
export function formatDateTimeToMDHM(timestamp) {
    let arr = moment(timestamp).format('M-D-HH:mm').split('-')
    return `${arr[0]}月${arr[1]}日 ${arr[2]}`
}

//计算 2个时间得差值
export function formatDiff(now, oldNow, status, duration) {
    //console.log("🚀 ~ status", status)

    // console.log(formatDateTime(now));
    // console.log(formatDateTime(oldNow));
    now = moment(formatDateTime(now));
    oldNow = moment(formatDateTime(oldNow));
    let date = now.diff(oldNow, 'second')
    // console.log("date3", date);
    // console.log("🚀 ~ duration", duration)
    if (duration > date) {
        let remainTimeData = moment.duration(duration - date, 'seconds')
        // console.log("🚀 ~ duration - date", duration - date)

        let remainTime = moment({ h: remainTimeData.hours(), m: remainTimeData.minutes(), s: remainTimeData.seconds() }).format('HH:mm:ss')
        // console.log("🚀 ~ remainTime", remainTime)

        let time = moment.duration(date, 'seconds')
        let hours = time.hours()
        let minutes = time.minutes()
        let seconds = time.seconds()
        return [moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss'), remainTime, duration - date]
    } else {

        return false
    }

}