export const globalCodes = {
    //机器类型
    machineType: {
        MASK: 1,             //口罩机
        BAG: 2,              //环保袋
        RECHARGE: 3,         //充电宝
        RECHARGE_LINE: 4,    //充电线
        WIRELESS_CHARGING: 5, //无线充      
    },

    //机器类型对应名称
    machineTypeName: {
        MASK: '口罩机',
        BAG: '环保袋',
        RECHARGE: '充电宝',
        RECHARGE_LINE: '充电线',
        WIRELESS_CHARGING: '无线充',
        nameArr: ['', '口罩机', '环保袋', '充电宝', '充电线', '无线充']
    },

    //机器类型对应编码
    machineCode: {
        MASK: ['0xdd', '0xcc', '0xad'],
        BAG: ['0xa4', '0xd5', "0x11"],
        RECHARGE: ['0x10', '0x80'],
        WIRELESS_CHARGING: ['0x88', '0x89'],
    },

    //获取（袋子/口罩等）方式
    getWay: {
        PAY_MONEY: 1,        //直接支付
        POINT_EXCHANGE: 2,   //积分兑换
        FREE_GET_BY_AD: 3,   //通过观看广告免费领取
        PAY_BALANCE:4, // 余额支付
    },

    
    //订单状态
    orderStatus: {
        STATUS_FINISHED: 1,                //订单完成
        STATUS_EXCEPTION: 2,               //订单异常
        STATUS_CANCEL: 3,                  //取消订单
        STATUS_WAIT_FOR_PAY: 4,			//等待出货
        STATUS_TIME_OUT: 5, 				// 订单超时

        nameArr: ['', '订单完成', '订单异常', '取消订单', '等待支付', '订单超时']
    },

    //充电订单状态
    chargeOrderStatus: {
        STATUS_CHARGE_TO: 1,                //待开启
        STATUS_CHARGE_ING: 2,               //充电中
        STATUS_CHARGE_END: 3,               //充电结束
        nameArr: ['', '待开启', '正在充电中~', '充电结束~']
    },

    // 游戏订单状态 
    gameOrderStatus: {
        STATUS_GAME_TO: 1,                //待开启
        STATUS_GAME_ING: 2,               //游戏中
        STATUS_GAME_END: 3,               //游戏结束
        STATUS_GAME_ERR: 4,               //订单异常
        nameArr: ['', '待启动', '正常骑行中~', '骑行结束~','订单异常']
    },
    carOrderStatus: {
        STATUS_GAME_TO: 1,                //待开启
        STATUS_GAME_ING: 2,               //游戏中
        STATUS_GAME_END: 3,               //游戏结束
        STATUS_GAME_ERR: 4,               //订单异常
        nameArr: ['', '待启动', '正常驾驶中~', '驾驶结束~','订单异常']
    },
    cannonOrderStatus: {
        STATUS_GAME_TO: 1,                //待开启
        STATUS_GAME_ING: 2,               //游戏中
        STATUS_GAME_END: 3,               //游戏结束
        STATUS_GAME_ERR: 4,               //订单异常
        nameArr: ['', '待启动', '正常运行中，请长按开炮~', '运行结束~','订单异常']
    },
    // 新增水枪
    waterGunOrderStatus: {
        STATUS_GAME_TO: 1,                //待开启
        STATUS_GAME_ING: 2,               //游戏中
        STATUS_GAME_END: 3,               //游戏结束
        STATUS_GAME_ERR: 4,               //订单异常
        nameArr: ['', '待启动', '正常运行中~', '运行结束~','订单异常']
    },

    // 设备状态（用户）
    umModelStatus:{
        NOTHING : 0, // 待
        CHARGING:1, // 充电中
        GAMING:2, // 游戏中
    },

    // 订单类型
    orderGategory: {
        CHARGE:"charge",
        OPEN_DOOR:"openDoor",
        GAME_TIME:"gameTime",
    },

    // 服务器端订单区别   
    orderTypeService:{
        ORDER_CHARGE :1, // 充电订单
        ORDER_COMMON :2, // 普通订单
    },

    // 普通订单 再分类
    commonOrderType:{ 
        ORDER_COMMON_PAY :1, // 付费订单
        ORDER_COMMON_FREE: 2, // 免费订单
        ORDER_COMMON_RATE_BACK:3, // 返利订单
        ORDER_COMMON_RATE_DOUIN:4, // 抖音订单
        ORDER_COMMON_POINT:5, // 积分订单
    },

    // 订单类型单位
    orderUnitType:{
        UNIT_GAME:3, // 游戏订单单位
    }
}

//判断是否是蓝牙设备
export const isBleDevice = (deviceType) => {
    if (deviceType == globalCodes.machineType.RECHARGE_LINE || deviceType == globalCodes.machineType.WIRELESS_CHARGING)
        return true
    return false
}

//口罩机
export const isMaskMachine = (type) => {
    if (type && typeof (type) == 'string' && type.indexOf('0x') >= 0) {
        let arr = globalCodes.machineCode.MASK
        return arr.findIndex(item => item == type) >= 0
    } else {
        return type == globalCodes.machineType.MASK;
    }
}

//环保袋机
export const isBagMachine = (type) => {
    if (type && typeof (type) == 'string' && type.indexOf('0x') >= 0) {
        let arr = globalCodes.machineCode.BAG
        return arr.findIndex(item => item == type) >= 0
    } else {
        return type == globalCodes.machineType.BAG;
    }
}

//充电宝
export const isRechargeMachine = (type) => {
    if (type && typeof (type) == 'string' && type.indexOf('0x') >= 0) {
        let arr = globalCodes.machineCode.RECHARGE
        return arr.findIndex(item => item == type) >= 0
    } else {
        return type == globalCodes.machineType.RECHARGE;
    }
}

//无线充
export const isWirelessMachine = (type) => {
    if (type && typeof (type) == 'string' && type.indexOf('0x') >= 0) {
        let arr = globalCodes.machineCode.WIRELESS_CHARGING
        return arr.findIndex(item => item == type) >= 0
    } else {
        return type == globalCodes.machineType.WIRELESS_CHARGING;
    }
}