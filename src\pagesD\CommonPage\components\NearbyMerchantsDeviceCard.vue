<template>
    <view class="card" @click="change">
        <view class="content">
            <radio :checked="info.isCheck" class="radio" color="#0DACE0" />
            <view class="list">
                <view class="list-item">
                    <view class="title">设备编号：</view>
                    <view class="txt">{{ info.device_sn }}</view>
                </view>
                <view class="list-item">
                    <view class="title">价格：</view>
                    <view class="txt">{{ info.price }}</view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    name: "NearbyMerchantsDeviceCard",
    props: {
        info: { type: Object, default: {} },
    },
    data() {
        return {};
    },

    methods: {
        change() {
            this.$emit("selectItem");
        },
    },
    onLoad() { },
};
</script>

<style scoped lang="scss">
.card {
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    background-color: #fff;
}

.content {
    display: flex;
    align-items: center;

    .list {
        margin-left: 10rpx;

        &-item {
            display: flex;
            font-size: $font-size-base;
            line-height: 1.8;

            .title {
                color: #666;
            }

            .txt {
                color: #333;
            }
        }
    }

    .radio {
        flex-shrink: 0;
    }
}
</style>
