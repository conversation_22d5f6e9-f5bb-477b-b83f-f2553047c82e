<template>
  <view>
    <!-- #ifndef MP-TOUTIAO -->
    <u-navbar
      v-if="isShowSlot"
      :title="title"
      :placeholder="placeholder"
      :bgColor="bgColor"
      :titleStyle="{ color: color, fontWeight: bold ? 700 : 400 }"
      :safeAreaInsetTop="true"
      @rightClick="onRightClick"
      @leftClick="onLeftClick"
    >
      <view slot="left">
        <slot />
      </view>
      <view slot="center">
        <slot name="center">{{ title }}</slot>
      </view>
    </u-navbar>
    <u-navbar
      v-else
      :title="title"
      :placeholder="placeholder"
      :bgColor="bgColor"
      :leftIconColor="color"
      :safeAreaInsetTop="true"
      :leftIconSize="leftIconSize"
      :autoBack="autoBack"
      :titleStyle="{ color: color, fontWeight: bold ? 700 : 400 }"
      @rightClick="onRightClick"
      @leftClick="onLeftClick"
    />
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  name: "BaseNavbar",
  props: {
    title: {
      type: String,
      default: "",
    },
    isShowSlot: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: Boolean,
      default: true,
    },
    bgColor: {
      type: String,
      default: "#fff",
    },
    color: {
      type: String,
      default: "#333",
    },
    bold: {
      type: Boolean,
      default: false,
    },
    autoBack: {
      type: Boolean,
      default: true,
    },
    leftIconSize: {
      type: [String, Number],
      default: "40rpx",
    },
  },
  methods: {
    onLeftClick() {
      this.$emit("leftClick")
    },
    onRightClick() {
      this.$emit("rightClick")
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  font-weight: bold;
}
</style>
