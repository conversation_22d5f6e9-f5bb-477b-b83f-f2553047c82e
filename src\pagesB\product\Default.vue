<template>
    <view>
        <view class="top-Map">
            <view class="map-box">
                <view class="search-box">
                </view>
                <view class="swiper-box">
                    <swiper class="swiper_box" :interval="5000" autoplay :duration="1000" :circular="true">
                        <!-- #ifdef MP-WEIXIN  -->
                        <swiper-item class="swiper-item">
                            <!-- 插入广告组件 -->
                            <CommonAd :ad="vAd.swiperBannerAd || ''" type="custom" />
                        </swiper-item>
                        <!--  #endif -->
                        <!-- 广告轮播项 -->
                        <swiper-item class="swiper-item" v-for="(item, index) in adInfoList" :key="index">
                            <image :src="item.url" class="image"></image>
                        </swiper-item>
                    </swiper>
                </view>
                <view class="map">
                    <!-- #ifdef MP-WEIXIN -->
                    <map id="map" :longitude="longitude" :latitude="latitude" scale="18" :markers="markers"
                        :polyline="polyline" :polygons="polygons" show-location
                        style="width: 100%; height:  calc(20vh)">
                    </map>
                    <!--  #endif  -->
                    <!--   #ifdef MP-ALIPAY -->
                    <map id="map" :latitude="curLatitude" :longitude="curLongitude" scale="18" :markers="markers"
                        :polyline="polyline" :polygons="polygons" show-location
                        style="width: 100%; height:  calc(25vh)">
                    </map>
                    <!-- #endif  -->

                    <view class="home" @click="nivHome()">
                        <view class="home_icon">
                            <BaseIcon name="home-fill" size="30" color="#fff" />
                        </view>
                        <!-- <view>返回首页</view> -->
                    </view>
                </view>



                <view class="pre_top">
    
                    <view>
                        <span class="pre_top_right">设备编号：{{ device_sn || '' }}</span>
                    </view>


                </view>
                <block v-if="false">
                    <view class="pre_btn">

                        <view class="pre_btn_item">
                            <view class="pre_btn_item_line">
                                <image class="image" src="../static/点.png" />
                            </view>
                            <view class="pre_btn_item_left">
                                预付金
                            </view>
                            <view class="pre_btn_item_green">
                                ￥{{
                    deviceInfo.pre_pay_money || 0
                }}元
                            </view>
                            <view class="bubble-box">未缴纳</view>
                        </view>
                        <view class="pre_btn_item">
                            <view class="pre_btn_item_line">
                                <image class="image" src="../static/点.png" />
                            </view>
                            <view class="pre_btn_item_left">
                                起步时长
                            </view>
                            <view class="pre_btn_item_green">
                                {{ deviceInfo.game_time || 0 }}分钟
                            </view>
                        </view>
                        <view class="pre_btn_item">
                            <view class="pre_btn_item_line">
                                <image class="image" src="../static/点.png" />
                            </view>
                            <view class="pre_btn_item_left">
                                起步价
                            </view>
                            <view class="pre_btn_item_green">
                                {{
                    deviceInfo.good_price || 0
                }}元（含{{ deviceInfo.game_time || 0 }}分钟）
                            </view>
                        </view>
                        <view class="pre_btn_item">
                            <view class="pre_btn_item_line">
                                <image class="image" src="../static/点.png" />
                            </view>
                            <view class="pre_btn_item_left">
                                超起步价后
                            </view>

                            <span class="pre_btn_item_top">
                                <span>
                                    <view>
                                        <span class="pre_btn_item_pre">
                                            {{
                                            deviceInfo.pre_per_minute || 0
                                            }}元/1分钟
                                        </span>
                                        <span class="pre_btn_item_green">
                                            （超出起步价后）
                                        </span>
                                    </view>
                                    <view class="pre_btn_item_small">
                                        不足1分钟按1分钟算
                                    </view>
                                </span>

                            </span>
                        </view>

                        <view class="pre_btn_item">
                            <view class="pre_btn_item_line">
                                <image class="image" src="../static/点.png" />
                            </view>
                            <view class="pre_btn_item_left">
                                支付订单后
                            </view>
                            <view class="pre_btn_item_green">
                                不足{{ deviceInfo.game_time || 0 }}分钟按{{ deviceInfo.game_time||0}}分钟算
                            </view>
                        </view>

                    </view>
                    <view class="pre_msg">
                        <view class="pre_btn_icon">
                            <BaseIcon name="info-circle-fill" size="20" color="#ff852f" />
                        </view>
                        <view>
                            请在上述地图规定的p点内并连接电源归还设备
                        </view>

                    </view>
                    <view class="pre_buy">
                        <view class="pre_buy_flex">
                            <view class="pre_buy_pre">
                                ￥{{deviceInfo.pre_pay_money||0}}
                            </view>
                            <view class="pre_buy_btn" @click="buy">去支付</view>
                        </view>

                    </view>
                </block>
                <block>
                    <NoPasswordPayment></NoPasswordPayment>
                </block>
            </view>
        </view>
    </view>
</template>
<script>

import { locationMixin } from "@/mixins/locationMixin";
import BaseIcon from "@/components/base/BaseIcon.vue";
import NoPasswordPayment from "../components/NoPasswordPayment.vue";
export default {
    name: 'index',
    components: {
        BaseIcon,
        NoPasswordPayment
    },
    data() {
        return {
            polygons: [],
            polygonLabels: [], // 用于存储多边形中心的文字标注
            currentPolygonIndex: -1, // 当前正在创建的多边形索引
            isCreatingPolygon: false, // 用于判断是否在创建多边形
            isCreating: false,//是否可以创建多边形
            nearbyMachineList: [],
            longitude: 0,
            latitude: 0,
            searchQuery: "", // 搜索框的值
            polygonMarkers: [], // 用于存储多边形的标记点
            hotel_id: '',
            isP: false,
            isAddP: false,
            isAddMarker: false,
            mapCtx: null,
            orderInfo: {
                pay_money: '12'
            },


        }
    },
    mixins: [locationMixin],
    methods: {
        nivHome() {
            //跳转并清楚路由历史
            uni.reLaunch({
                url: '/pages/index/index',
            })
        },
        buy() {
            this.$emit('buy', this.deviceInfo)
        },

        //计算中心点
        calculateCenterPoint(coordinates) {
            if (coordinates.length === 0) {
                return null; // 或者你可以选择抛出一个错误  
            }

            let sumLat = 0;
            let sumLon = 0;

            // 遍历坐标数组，累加纬度和经度  
            coordinates.forEach(coord => {
                sumLat += coord.latitude * 1;
                sumLon += coord.longitude * 1;
            });

            // 计算平均值  
            const avgLat = sumLat / coordinates.length;
            const avgLon = sumLon / coordinates.length;

            // 返回中心点坐标  
            return { latitude: avgLat, longitude: avgLon };
        },

        findMarkerIndex(lat, lng) {
            const tolerance = 0.00005; // 公差范围，用于判断点击点是否接近现有标记
            return this.polygonMarkers.findIndex(marker => {
                return Math.abs(marker.latitude - lat) < tolerance && Math.abs(marker.longitude - lng) < tolerance;
            });
        },

        //初始化地图
        initMarkers() {

            this.mapCtx = uni.createMapContext("map"); // map为地图的id
        },
        //获取位置信息
        getLocPermission() {
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            this.initLocPermission(() => {
                this.getCurrentLocation(() => {
                    this.longitude = this.curLongitude
                    this.latitude = this.curLatitude
                    this.initMarkers();

                });
            });
            //#endif
            // #ifdef MP-ALIPAY
            uni.showModal({
                title: "温馨提示：",
                content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
                success: ({ confirm }) => {
                    if (confirm) {
                        this.getCurrentLocation(() => {
                            this.initMarkers();
                        });
                    }
                },
            });

            //#endif
            // #ifdef H5
            uni.showModal({
                title: "温馨提示：",
                content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
                success: ({ confirm }) => {
                    if (confirm) {
                        this.getCurrentLocation(() => {
                            this.initMarkers();
                        });
                    }
                },
            });
            //#endif
        },
        // //获取定位
        // async getLocation(hotel_id) {
        //     try {
        //         let data = {
        //             hotel_id: hotel_id
        //         }
        //         const res = await this.$u.api.getMyHotels(data);
        //         this.nearbyMachineList[0] = res.data.find(item => item.id == hotel_id)


        //         this.longitude = this.nearbyMachineList[0].lon
        //         this.latitude = this.nearbyMachineList[0].lat


        //         console.log('数据筛选', this.nearbyMachineList, this.longitude, this.latitude)

        //         if (this.longitude && this.latitude) {
        //             console.log('经纬度', this.longitude, this.latitude)
        //         } else {
        //             this.getLocPermission()
        //         }
        //     }
        //     catch (error) {
        //         console.log(error);
        //     }
        // },
        // //获取电子围栏
        // async getGeofence(hotel_id) {
        //     try {
        //         let data = {
        //             hotel_id: hotel_id
        //         }
        //         const res = await this.$u.api.getHotelGeofence(data);
        //         console.log('points', res)
        //         if (res.data.length > 0) {
        //             this.isAddMarker = true
        //         } else {
        //             this.isAddMarker = false

        //         }
        //         let arr = []
        //         //如果有进制添加围栏





        //         res.data.map(item => {
        //             let data = {
        //                 latitude: item.latitude,
        //                 longitude: item.longitude
        //             }
        //             arr.push(data)
        //             // this.polygons[0].points.push(data)
        //             // console.log('this.polygons',this.polygons)

        //         })

        //         let params = {
        //             id: this.currentPolygonIndex,
        //             fillColor: "#1791fc66",
        //             strokeColor: "#FFF",
        //             strokeWidth: 2,
        //             zIndex: 3
        //         };

        //         let newPolygon = Object.assign({ points: arr }, params);
        //         console.log('arr', arr)
        //         // 示例  
        //         const coordinates = arr;
        //         const centerPoint = this.calculateCenterPoint(coordinates);
        //         console.log('centerPoint', centerPoint)
        //         this.polygons[0] = newPolygon;
        //         // let markerArr = {
        //         //   id: 1,
        //         //   latitude: centerPoint.latitude,
        //         //   longitude: centerPoint.longitude,
        //         //   iconPath: this.icMarkerDefault,
        //         //   width: 40,
        //         //   height: 50,
        //         // }
        //         // console.log("🚀 ~ markerArr", markerArr)
        //         // this.markers.push(markerArr);
        //         // console.log('this.markers', this.markers)
        //         // // this.polygons[0].push(params)
        //         // console.log('数据筛选', res)

        //     }
        //     catch (error) {
        //         console.log('err', error);
        //     }
        // },
        // //获取p点
        // async getfence(hotel_id) {
        //     try {
        //         let data = {
        //             hotel_id: hotel_id
        //         }
        //         const res = await this.$u.api.getHotelPGeofence(data);
        //         console.log('points', res)
        //         if (res.data.length > 0) {
        //             this.isAddP = true

        //         } else {
        //             this.isAddP = false

        //         }
        //         let markerItem = {
        //             id: 10001,
        //             latitude: res.data[0].latitude * 1,
        //             longitude: res.data[0].longitude * 1,
        //             width: '24px',
        //             height: '34px',
        //             iconPath: '../static/img/icon/p.png',
        //             rotate: 0,
        //             alpha: 1,
        //             zIndex: 3,
        //         };

        //         // 检查是否已存在具有特定id的对象  
        //         const index = this.markers.findIndex(marker => marker.id === markerItem.id);

        //         // 如果不存在（index 为 -1），则添加新对象  
        //         if (index === -1) {
        //             this.markers.push(markerItem);
        //         } else {
        //             // 如果存在，则替换它  
        //             this.markers.splice(index, 1, markerItem);
        //         }
        //     }
        //     catch (error) {
        //         console.log(error);

        //     }



        // },
    },
    props: {
        deviceInfo: {
            type: Object,
            default: {},
        },
        device_sn: {
            type: String,
            default: ''
        },
        markers: {
            type: Array,
            default: [],
        },
        points: {
            type: Array,
            default: [],
        },
        adInfoList: {
            type: Array,
            default: [],
        },

    },
    //组件加载完成
    mounted() {
        if (this.points.length > 0) {
            this.polygons.push({
                points: [...this.points],
                fillColor: "#1791fc66",
                strokeColor: "#FFF",
                strokeWidth: 2,
                zIndex: 3
            });
        }
        console.log('polygons',this.polygons)
        // 初始化地图上下文
        this.mapCtx = uni.createMapContext("map", this);
        this.getLocPermission()
        // this.mapCtx = uni.createMapContext("map"); // map为地图的id
        // this.getLocPermission()

    }


}
</script>
<style lang="scss">
page {
    background-color: #fff;
}
</style>
<style scoped lang="scss">
.bubble-box {
    margin-left: 10rpx;
    border: 1rpx solid #ff852f;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    font-size: 23rpx;
    color: #ff852f;
    padding: 5rpx 10rpx;

}

.map {
    position: relative;

    .home {
        position: absolute;
        bottom: 30rpx;
        left: 30rpx;
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        background-color: #0eade2;
        color: white
    }
}

.swiper_box {
    padding: 0;
    /* #ifdef MP-WEIXIN  */
    height: 260rpx;
    /* #endif */
    /* #ifdef MP-ALIPAY */
    height: 250rpx;

    /* #endif */
    .swiper-item {
        // background-color:white;
        // background-color:red;
        height: auto;
    }

    ::v-deep .common {
        padding: 0 !important;
    }

    image {
        width: 100%;
        height: 100%;
    }
}



.pre_top {
    background-color: rgb(238, 236, 236);
    padding: 10rpx 20rpx;
    display: flex;
    justify-content: space-between;

    &_blod {
        font-weight: bold;
        font-size: 50rpx;
    }

    &_pre {
        color: rgb(255, 160, 35);
    }

    &_small {
        margin-left: 10rpx;
        font-size: 20rpx;
    }

    &_right {
        font-size: 45rpx;
    }

}

.pre_btn {
    padding: 0 10rpx 20rpx 20rpx;

    // height: 700rpx;
    // color: #ff852f;
    // position: absolute;
    // bottom: 150rpx;
    &_item {
        display: flex;
        margin: 40rpx 0;
        align-items: center;

        &_line {
            width: 25rpx;
            height: 25rpx;
            margin-right: 10rpx;
            display: flex;
            align-items: center;

            .image {
                width: 100%;
                height: 100%;
            }

        }

        &_green {
            font-size: 35rpx;
            color: #666;
        }

        &_top {
            vertical-align: top;
        }

        &_small {
            font-size: 38rpx;
            color: #666;
        }

        &_pre {
            color: #ff852f;
            font-size: 32rpx;
        }

        &_left {
            width: 200rpx;
            margin-right: 20rpx;
            font-size: 40rpx;
            font-weight: bold;
        }
    }

    &_icon {
        margin-right: 10rpx;
    }

}

.pre_msg {
    // position: absolute;
    // bottom: 150rpx;
    margin: 0 20rpx;
    display: flex;
    background-color: rgb(95, 95, 95);
    padding: 20rpx;
    color: #fff;
    font-size: 30rpx;

    &_icon {
        margin-right: 10rpx;
    }


}

.pre {
    // width: 100%;
    position: relative;
}

.pre_buy {
    width: 100%;
    box-sizing: border-box;
    padding: 10rpx 20rpx;
    background-color: #fff;

    &_flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    &_pre {
        color: #ff852f;
        font-size: 45rpx;
    }

    &_btn {
        width: 250rpx;
        padding: 20rpx;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
        border-radius: 15rpx;
        font-size: 40rpx;
        color: #fff;
        text-align: center;
    }

    &_add {
        border: 1rpx solid #ff852f;
    }

    position: relative;
    margin-top: 50rpx;

}
</style>