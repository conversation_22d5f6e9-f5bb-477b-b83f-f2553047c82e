<template>
  <!-- 商铺详情信息导航 -->
  <view class="content" @click="goDetais">
    <view class="card">
      <view class="top" @click="gotoConfirmOrderPage">
        <view class="placeName" v-if="pointPlaceInfo.hotelName">
          {{ pointPlaceInfo.hotelName }}</view>
        <view class="deviceInfo">
          <view class="line flexRowBetween">
            <view class="line-box">
              <view class="icon-box">
                <BaseIcon name="bookmark" />
              </view>
              <view class="label">
                设备编号：{{ pointPlaceInfo.device_sn }}
              </view>
            </view>
            <view class="see-details" v-if="isShowDetails"> 查看详情 </view>
          </view>
          <view class="line">
            <view class="icon-box">
              <BaseIcon name="clock" />
            </view>
            <view class="label">营业时间：09:00-23:00</view>
          </view>
          <view class="line" v-if="pointPlaceInfo.addressDetail">
            <view class="icon-box">
              <BaseIcon name="map" />
            </view>
            <view class="label">{{ pointPlaceInfo.addressDetail }}</view>
          </view>
          <view class="line">
            <view class="icon-box">
              <BaseIcon name="wifi" color="#0DACE0" />
            </view>
            <view class="label act">网络正常</view>
          </view>
          <view class="line" v-if="vIsBleDevice">
            <view class="icon-box flexRowAllCenter ble-icon">
              <image class="icon-img" :src="`/pagesB/static/icon/${bBleConnected ? 'ic_ble_connected' : 'ic_ble_not_connected'
                }.png`" />
            </view>
            <view class="label textMaxOneLine" :class="bBleConnected ? 'act' : ''">
              {{ bBleConnected ? "蓝牙已连接" : "蓝牙未连接" }}
            </view>
          </view>
        </view>
        <block>
          <view class="placeName" style="margin-top: 20rpx;">
            订单信息</view>
          <view class="deviceInfo">
            <view class="line flexRowBetween">
              <view class="line-box">
                <view class="icon-box">
                  <BaseIcon name="order" />
                </view>
                <view class="label">
                  订单编号：{{ vCreateOrderInfo.orderInfo.order_sn }}
                </view>
              </view>
            </view>
            <view class="line flexRowBetween">
              <view class="line-box">
                <view class="icon-box">
                  <BaseIcon name="rmb-circle" />
                </view>
                <view class="label">
                  订单价格：
                  <text style="color: red;">
                    {{ vCreateOrderInfo.orderInfo.order_price }}元
                  </text>
                  <text v-if="vCreateOrderInfo.orderInfo.vip_price*1">（会员价{{vCreateOrderInfo.orderInfo.vip_price}}元）</text>
                </view>
              </view>
            </view>
            <view class="line flexRowBetween">
              <view class="line-box">
                <view class="icon-box">
                  <BaseIcon name="clock" />
                </view>
                <view class="label">
                  游戏时间：{{ vCreateOrderInfo.curSelMeal.game_time }}（分钟）
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
      <view class="btnNav flexRowAllCenter" @click="onClickBtnNav" v-if="navIsShow">
        <BaseIcon name="map" sizi="26" color="#fff" />
        <view class="btnText">导航</view>
      </view>
    </view>
  </view>
</template>

<script>
import BaseIcon from "./base/BaseIcon.vue";

export default {
  components: { BaseIcon },
  props: {
    pointPlaceInfo: Object,
    navIsShow: {
      type: Boolean,
      default() {
        return false;
      },
    },
    bBleConnected: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isShowDetails: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      isIconStyle: "",
      isBleDeviceSn: false,
    };
  },

  mounted() {
    // this.isIconStyle = this.getSizeByType(this.pointPlaceInfo.deviceType);
  },
  methods: {
    onClickBtnNav() {
      uni.openLocation({
        latitude: parseFloat(this.pointPlaceInfo.lat),
        longitude: parseFloat(this.pointPlaceInfo.lon),
        name: this.pointPlaceInfo.adr_title,
        address: this.pointPlaceInfo.addressDetail,
        success: function (res) {
          console.log("导航返回值：", res);
        },
      });
    },
    gotoConfirmOrderPage() { },
    goDetais() {
      if (!this.isShowDetails) return false;
      uni.navigateTo({ url: `/pagesB/placeDetails/index` });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  .top {
    .placeName {
      font-size: $font-size-xlarge;
      color: $textBlack;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 36rpx;
      @include textMaxOneLine();
    }

    .deviceInfo {
      margin-top: auto;

      .line {
        margin-bottom: 25rpx;
        display: flex;
        align-items: center;
        overflow: hidden;

        &-box {
          @include flexAllcenter();
        }

        .see-details {
          color: #0dace0;
          font-size: 28rpx;
          font-weight: 700;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          @include textMaxOneLine();
          margin-left: 22rpx;
          font-size: $font-size-xsmall;
          color: #666666;
        }

        .act {
          color: $themeColor;
        }
      }
    }
  }

  .btnNav {
    color: #fff;
    height: 80rpx;
    background: linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2);
    border-radius: 40rpx;
    font-size: $font-size-large;
    text-align: center;
    margin-top: 60rpx;

    .nav {
      width: 27rpx;
      height: 27rpx;
    }

    .btnText {
      margin-left: 6rpx;
    }
  }
}

.ble-icon {
  width: 52rpx;
  height: 52rpx;
  padding: 6rpx;
  box-sizing: border-box;

  .icon-img {
    width: 100%;
    height: 100%;
  }
}
</style>
