<template>
    <!--我的汉币页面-->
    <view>
        <!-- #ifndef H5 -->
        <BaseNavbar :title="`我的${vHName}`" />
        <!-- #endif -->
        <view class="content">
            <view class="centerArea">
                <view class="t_left">
                    <view class="t_m_text">我的{{ vHName }}</view>
                    <view class="t_b_text">{{ vMemberInfo.cash || "0.00" }}</view>
                    <view class="t_m_text">包含赠送{{ vHName }} {{ "0.00" }}</view>
                </view>
                <view class="t_right" @click="gotoBeansDetailPage()">
                    <text>明细</text>
                    <text class="t_m">></text>
                </view>
            </view>
            <view class="buttomArea">
                <view class="but_title">
                    {{ vHName }}充值
                </view>
                <view class="center">
                    <view class="cent_itme" v-for="(item, i) in rechargeBeanList" :key="i"
                        @click="gotoMany(item.money)">
                        <view class="cent_top">
                            <view class="cent_title">
                                <text>
                                    {{ item.money }}
                                </text>
                                <text class="t_sm">
                                    {{ vHName }}
                                </text>

                            </view>
                            <view class="cent_btm">
                                购买数量
                            </view>
                        </view>
                        <view class="cent_btn">
                            <view class="btm_left">
                                <text>￥{{ item.money }}</text>
                                <text class="btm_sm">
                                    元
                                </text>

                            </view>
                            <view class="btn">
                                购买
                            </view>
                        </view>
                    </view>
                </view>
                <view class="bt_text">
                    <view class="bt_title">
                        {{ vHName }}说明：
                    </view>
                    <view>
                        <view class="line_h" v-for="(item, i) in tips" :key="i">
                            {{ item }}
                        </view>
                    </view>
                </view>
            </view>
            <LoginPopup />
        </view>
    </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue";
import LoginPopup from "@/components/LoginPopup.vue";
import BaseButton from "@/components/base/BaseButton.vue";
import BaseNavbar from '../../components/base/BaseNavbar.vue'
import { creatRechargeOrderAndPrepay, getPackage, updateUserInfo, getUserInfo } from '@/common/http/api'
export default {
    name: "index",
    components: { BaseIcon, LoginPopup, BaseButton, BaseNavbar },

    data() {
        return {
            cash: "0",
            id: [],
            vip_price: '',
            rechargeBeanList: [],//套餐列表
            tips: [],//提示
            orider: false,
            buyindex: false,
        };
    },
    computed: {
        payMany() {
            if (this.vip_price) {
                return this.vip_price * 1;
            } else {
                return '自定义'
            }
        },
    },
    methods: {
        getInfo() {
            getUserInfo().then((res) => {
                uni.$u.vuex('vMemberInfo', res)
            })
        },
        doGetPackage() {
            let data = {
                type: 'recharge',
            }
            getPackage(data).then((res) => {
                this.rechargeBeanList = res?.data || []
                this.tips = res?.tips?.split('|') || []
                console.log('tip', this.tips)
            })
        },
        gotoMany(pay) {
            let many = (pay * 1).toFixed(2)
            this.requestWxPay(many)
        },
        requestWxPay(pay) {
            let that = this
            let params = {
                amount: pay,
                device_sn: this.vDeviceSn, // 扫码的设备
            }
            creatRechargeOrderAndPrepay(params).then((res) => {
                console.log('🚀 ~ res', res)
                let payInfo = res.prepay_info
                //请求微信支付
                /* #ifdef MP-WEIXIN */
                uni.requestPayment({
                    /* #ifdef MP-WEIXIN */
                    provider: 'wxpay',
                    timeStamp: payInfo.timeStamp,
                    nonceStr: payInfo.nonceStr,
                    package: payInfo.package,
                    signType: payInfo.signType,
                    paySign: payInfo.paySign,
                    /* #endif */
                    /* #ifdef MP-ALIPAY */
                    provider: 'alipay',
                    orderInfo: payInfo.trade_no,
                    /* #endif */
                    success(res) {
                        console.log('支付成功', res)
                        /* #ifdef MP-ALIPAY */
                        if (res.resultCode != 9000)
                            return uni.showToast({
                                title: '支付失败',
                                icon: 'none',
                            })
                        /* #endif */
                        //跳转到订单列表页面
                        // this.isShowSuccess("支付成功", 0);
                        if (that.buyindex || that.orider) {
                            uni.navigateBack({
                                delta: 1 // 返回上一页，如果需要返回多页，可以调整此数字
                            });
                        } else {
                            uni.redirectTo({
                                url: `/pagesD/wallet/consumeList`,
                            })
                        }

                    },
                    fail(res) {
                        console.log('支付失败', res)
                        uni.showToast({
                            title: '支付失败~',
                            icon: 'none',
                        })
                    },
                    complete(res) {
                        console.log('支付complete', res)
                        //clearInterval(that.sti);
                        uni.$u.vuex('vMemberInfo', null) // 清空，重新获取用户信息，重新获取汉币
                        that.getInfo()

                    },
                })
                /* #endif */
                /* #ifdef MP-ALIPAY */
                my.tradePay({
                    orderInfo: payInfo.trade_no,
                    success: (res) => {
                        console.log('支付成功tradePay', res)
                        uni.redirectTo({
                            url: `/pagesD/wallet/consumeList`,
                        })
                    },
                    fail: (res) => {
                        console.log('支付失败', res)
                        uni.showToast({
                            title: '支付失败~',
                            icon: 'none',
                        })
                    },
                    complete: (res) => {
                        console.log('支付complete', res)
                        //clearInterval(that.sti);
                        uni.$u.vuex('vMemberInfo', null) // 清空，重新获取用户信息，重新获取汉币
                        that.getInfo()

                    },
                })
                /* #endif */
                /* #ifdef H5 */

                let ua = window.navigator.userAgent.toLowerCase()
                //判断是不是微信
                if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                    // 微信
                    console.log('微信浏览器')
                    WeixinJSBridge.invoke(
                        'getBrandWCPayRequest',
                        {
                            appId: this.vAppId, //公众号ID，由商户传入
                            timeStamp: payInfo.timeStamp, //时间戳，自1970年以来的秒数
                            nonceStr: payInfo.nonceStr, //随机串
                            package: payInfo.package,
                            signType: payInfo.signType, //微信签名方式：
                            paySign: payInfo.paySign, //微信签名
                        },
                        function (res) {
                            if (res.err_msg == 'get_brand_wcpay_request:ok') {
                                console.log('支付成功', res)
                                uni.redirectTo({
                                    url: `/pagesD/wallet/consumeList`,
                                })
                                // 使用以上方式判断前端返回,微信团队郑重提示：
                                //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
                            } else {
                                uni.showToast({
                                    title: '支付失败~',
                                    icon: 'none',
                                })
                            }
                        },
                    )
                }
                //判断是不是支付宝
                else if (ua.match(/AlipayClient/i) == 'alipayclient') {
                    //支付宝
                    // 3、调用支付宝api唤起支付功能
                    AlipayJSBridge.call("tradePay", {
                        // tradeNO: "2020111422001434251423057464"
                        tradeNO: payInfo.trade_no
                    }, function (data) {
                        console.log(data)
                        //log(JSON.stringify(data));
                        if (data.resultCode != 9000) {
                            //支付失败
                            uni.showToast({
                                title: '支付失败~',
                                icon: 'none',
                            })

                        } else {
                            if (that.buyindex || that.orider) {
                                uni.navigateBack({
                                    delta: 1 // 返回上一页，如果需要返回多页，可以调整此数字
                                });
                            } else {
                                uni.redirectTo({
                                    url: `/pagesD/wallet/consumeList`,
                                })
                            }
                        }


                    })
                }
                /* #endif */
            })
        },

        gotoAssetsDetailPage() {
            uni.navigateTo({
                url: `/pagesD/wallet/consumeList?type=1`,
            });
        },

        gotoBeansDetailPage() {
            uni.navigateTo({
                url: `/pagesD/wallet/consumeList?type=2`,
            });
        },

        gotoRecharge() {
            uni.navigateTo({
                url: `/pagesD/wallet/rechargeCenter`,
            });
        },

        onClickWithdraw() {
            uni.navigateTo({
                url: `/profilePages/walletWithdraw/index?cash=${this.cash}`,
            });
        },

        // doGetMemberInfo() {
        //   getMemberInfo().then((res) => {
        //     this.cash = res?.cash;
        //   });
        // },
    },
    onShow() {
        // this.doGetMemberInfo();
        this.cash = this.vMemberInfo.cash;
    },
    onLoad(opt) {
        this.doGetPackage()
        console.log('opt', opt.from)
        if (opt.from == 'orider') {
            this.orider = true
        } else if (opt.from == 'buyindex') {
            this.buyindex = true
        }
    },
};
</script>
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.content {
    .t_sm {
        font-size: 25rpx;
        margin-left: 10rpx;
    }

    // .adContainer {
    //   margin-top: 30rpx;
    // }
    padding: 0 30rpx;

    .image {
        width: 100%;
        height: 100%;
        margin-right: 10rpx;
    }

    .centerArea {
        margin-top: 20rpx;
        // border: 2rpx solid red;
        height: 200rpx;
        border-radius: 20rpx;
        // padding: 20rpx;
        overflow: hidden;
        background-image: url('../static/wallet/eaf1b87c277aaade8bd9de19479366e.png');
        background-repeat: no-repeat;
        background-clip: content-box;
        /* 从内容框开始填充背景图片 */
        background-size: 100%;
        /* 从边框开始填充背景图片 */

        background-position: center;
        color: white;
        display: flex;
        justify-content: space-between;

        .t_left {
            margin: 30rpx 20rpx 0 30rpx;
        }

        .t_m_text {
            color: rgba(255, 255, 255 , 85%);
            font-size: 27rpx;
        }

        .t_b_text {
            font-size: 45rpx;
            margin: 7rpx 0;
            color: white;
        }

        .t_right {
            margin: 30rpx 30rpx 0 0;
            font-size: 24rpx;
            color: rgb(240, 238, 238);
        }

        .t_m {
            margin-left: 10rpx;
        }

    }

    .buttomArea {
        background-color: white;
        margin-top: 30rpx;
        border-radius: 16rpx;
        padding: 20rpx;

        .but_title {
            font-size: 35rpx;
            font-weight: bold;
            margin-bottom: 20rpx;
        }

        .center {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .cent_itme {
            border: 2rpx solid #9a9a9a;
            border-radius: 10rpx;
            width: 310rpx;
            margin-bottom: 20rpx;
            padding: 20rpx 0;
        }

        .cent_top {
            margin-left: 30rpx;
        }

        .cent_title {
            font-weight: bold;
            font-size: 45rpx;
            color: #666;
        }

        .cent_btm {
            font-size: 24rpx;
            color: #9a9a9a;
            margin: 5rpx 0;
        }

        .cent_btn {
            display: flex;
            align-items: center;
            margin-top: 22rpx;
            height: 50rpx;
        }

        .btn {
            width: 115rpx;
            height: 45rpx;
            background-image: url('../static/wallet/btnbg.png');
            background-repeat: no-repeat;
            background-clip: content-box;
            background-position: center;
            background-size: 100%;
            color: #ffd57a;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 22rpx;
        }

        .btm_left {
            background: linear-gradient(to right, $pageBgColor, #fffbfb);
            width: 150rpx;
            padding-left: 20rpx;
            margin-right: 10rpx;
            line-height: 45rpx;
            color: #c1090d;
            font-weight: bold;
        }

        .btm_sm {
            font-size: 20rpx;
        }


    }

    .bt_text {
        font-size: 23rpx;
        color: #666;

    }

    .line_h {
        line-height: 40rpx;
    }
}
</style>