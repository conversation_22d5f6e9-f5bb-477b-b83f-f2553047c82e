<template>
    <!-- <view>{{ info.url }}</view> -->
    <image class="template" src="../../static/mbg.png" @click="selectUrl"></image>
</template>
  
<script>
export default {
    props: {
        info: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            selectValueIndex: 0,
        };
    },
    methods: {
        selectUrl() {
            console.log('点击')
            uni.navigateTo({
                url: '/pagesD/updataImge/updataImge?form=select&url=' + this.info.url,
            });
        },
    },
    mounted() {
        console.log('info', this.info)
    }
};
</script>
  
<style lang="scss" scoped>
.template {
    height: 500rpx;
    // width: 100%;
    // flex: 0.5;
    width: 340rpx;
    margin-bottom: 10rpx;

    image {
        width: 100%;
        height: 100%;
    }
}
</style>