<template>
  <view class="landscape-container">
    <!-- 背景图 -->
    <image
      class="bg-image"
      src="https://img2.baidu.com/it/u=1242887946,1467773963&fm=253&fmt=auto&app=138&f=JPEG?w=698&h=500"
      mode="aspectFill"
    />
    <button
      @click="toggleDevelopmentMode"
      @touchstart.stop="toggleDevelopmentMode"
      style="
        position: absolute;
        width: 60px;
        height: 60px;
        left: 180px;
        top: 10px;
        z-index: 1000;
        background-color: rgba(255, 255, 255, 0.8);
        border: 2px solid #333;
        border-radius: 50%;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        -webkit-user-select: none;
        -webkit-tap-highlight-color: transparent;
      "
    >
      ×
    </button>
    <!-- 主体内容容器 -->
    <view class="content-wrapper">
      <!-- 圆弧背景纯黑色 -->
      <view class="ellipse"></view>
      <!-- 顶部调速区域 -->
      <view class="top-speed-control">
        <SteeringWheel
          :isStop="isStop"
          :steering-angle="steeringAngle"
          @one-press="onePress"
          @gear-press="sendGear"
          @gear-release="handleGearRelease"
          @gear-change="sendGear"
          @speed-change="sendSpeedPercent"
          @steer-start="handleTouchStart"
          @steer-change="handleSteerChange"
          @steer-end="handleButtonRelease('steer')"
        />
      </view>

      <!-- 主控制区域 -->
      <view class="main-controls-layout">
        <!-- 左侧前进后退控制 -->
        <view class="left-side">
          <ControlPanel
            :isStop="isStop"
            :bBleConnected="bBleConnected"
            :isPlay="isPlay"
            :is-forward-active="isForwardActive"
            :is-back-active="isBackActive"
            @forward-press="sendCommand('forward')"
            @forward-release="handleButtonRelease('forward')"
            @back-press="sendCommand('backward')"
            @back-release="handleButtonRelease('back')"
            @reconnect-bluetooth="handleReconnectBluetooth"
          />
        </view>

        <!-- 右侧左右转控制 -->
        <view class="right-side">
          <TurnControls
            :is-left-turn-active="isLeftTurnActive"
            :is-right-turn-active="isRightTurnActive"
            @left-turn-press="sendCommand('left')"
            @left-turn-release="handleButtonRelease('left')"
            @right-turn-press="sendCommand('right')"
            @right-turn-release="handleButtonRelease('right')"
          />
        </view>
      </view>

      <!-- 底部仪表盘区域 -->
      <view class="bottom-gauge-area">
        <!-- 仪表盘图片 -->
        <view class="fuel-gauge-container">
          <FuelGauge
            :isStop="isStop"
            :isPlay="isPlay"
            :isParkingActive="isParkingActive"
            @stop-press-start="sendCommand('brake')"
            @stop-press-end="handleButtonRelease('brake')"
            @parking-press-start="sendCommand('parking')"
            @parking-press-end="handleButtonRelease('parking')"
            @play-press="handlePlayPress"
          />
          <view
            class="steering-wheel"
            @touchstart="onTouchStart"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
            :style="{
              transform: `translateX(-50%) rotate(${wheelAngle}deg)`,
              transition: isTouching ? 'none' : 'transform 0.3s ease-out',
            }"
          >
            <image
              src="../static/方向盘.png"
              mode="aspectFit"
              class="wheel-img"
            />
          </view>
        </view>
      </view>

      <!-- 🎯 蓝牙通信记录列表 - 仅开发环境显示 -->
      <view v-if="isDevelopment" class="communication-log">
        <view class="log-header">
          <text class="log-title">📡 通信数据</text>
          <view class="log-controls">
            <button
              size="mini"
              @click="clearAllData"
              type="primary"
              plain="true"
            >
              清空
            </button>

            <text class="log-count"
              >{{ allDataList.length || 0 }}条 (显示{{
                displayDataList.length
              }}条)</text
            >
          </view>
        </view>
        <view class="viewDivider"></view>
        <view class="scanResultGroup">
          <view
            class="result-content"
            :class="
              item.type === 'send'
                ? 'send-item'
                : item.type === 'receive'
                ? 'receive-item'
                : 'event-item'
            "
            v-for="(item, index) in displayDataList"
            :key="index"
          >
            <template v-if="item.type === 'event'">
              {{ item.time }}：[事件] {{ item.meaning }}
            </template>
            <template v-else-if="item.type === 'send'">
              {{ item.time }}：{{ item.data }} [发送] {{ item.meaning }}
              <span
                :class="
                  item.broadcastStatus === 'success'
                    ? 'broadcast-success'
                    : 'broadcast-failed'
                "
                class="broadcast-status"
              >
                [{{ item.broadcastStatus === "success" ? "✅成功" : "❌失败" }}]
              </span>
              <span
                v-if="item.broadcastStatus === 'failed'"
                class="broadcast-error"
              >
                {{ item.broadcastMessage }}
              </span>
            </template>
            <template v-else>
              {{ item.time }}：{{ item.data }} [接收] {{ item.meaning }}
            </template>
          </view>
          <view v-if="allDataList.length === 0" class="no-data-tip">
            暂无通信数据，请发送指令等待设备响应
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import AudioManager from "@/utils/AudioManager" // 音频管理模块
import ControlPanel from "./components/ControlPannel.vue" // 左侧控制区组件
import TurnControls from "./components/TurnControls.vue" // 左右转控制组件
import FuelGauge from "./components/FuelGauge.vue" // 油表盘组件
import SteeringWheel from "./components/SteeringWheel.vue" // 方向盘组件
// 🎯 使用新架构的蓝牙模块
import toyCarBleUtil from "@/utils/ble/toyCarBleUnifiedNew.js"
import { globalEvents } from "@/global/globalEvents"
export default {
  components: {
    ControlPanel,
    TurnControls,
    FuelGauge,
    SteeringWheel,
  },
  data() {
    return {
      bBleConnected: false, // 蓝牙连接状态
      bluetoothInitialized: false, // 蓝牙是否初始化成功
      showBluetoothAlert: false, // 是否显示蓝牙提示弹窗
      isReconnecting: false, // 是否正在重连中
      receiveDataList: [], // 接收数据列表
      sendDataList: [], // 发送数据列表
      allDataList: [], // 🎯 合并的通信数据列表

      // 🎯 新架构：简化的按钮状态管理
      buttonStates: {}, // 从新架构蓝牙模块获取的按钮状态
      activeBroadcasts: [], // 活跃的广播列表

      // 🎯 其他状态
      isStop: false, // 停止状态
      isPlay: false, // 播放状态
      isParkingActive: false, // 驻车状态
      steeringAngle: 0, // 方向盘角度

      // 🎯 初始化状态管理
      isInitializing: false, // 是否正在初始化

      // 🎯 环境判断
      isDevelopment: true, // 是否为开发环境

      // 🎯 方向盘相关数据
      wheelAngle: 0,
      startAngle: 0,
      wheelCenter: { x: 0, y: 0 },
      isTouching: false,
      animationTimer: null,

      // 🎯 状态更新定时器
      stateUpdateTimer: null,
    }
  },
  computed: {
    // 🎯 限制显示的数据条数，提升页面性能
    displayDataList() {
      // 只显示最新的50条数据，减少DOM元素数量
      return this.allDataList.slice(0, 50)
    },

    // 🎯 新架构：计算属性获取按钮状态
    isForwardActive() {
      return this.buttonStates.forward || false
    },
    isBackActive() {
      return this.buttonStates.backward || false
    },
    isLeftTurnActive() {
      return this.buttonStates.left || false
    },
    isRightTurnActive() {
      return this.buttonStates.right || false
    },
  },

  onLoad() {
    console.log("📱 页面加载 - 新架构初始化")
    this.initNewArchitecture()
  },

  onShow() {
    console.log("👁️ 页面显示 - 检查蓝牙状态")
    // 只有在未初始化时才重新检查
    if (!this.bluetoothInitialized) {
      this.initNewArchitecture()
    }
    // 启动状态更新
    this.startStateUpdates()
  },

  onHide() {
    // 页面隐藏时停止状态更新
    this.stopStateUpdates()
  },

  onUnload() {
    // 页面卸载时清理所有资源
    this.stopStateUpdates()
    this.cleanupTimers()
    toyCarBleUtil.onNoneLoad()
  },

  methods: {
    // ===== 新架构：蓝牙初始化和管理 =====

    /**
     * 🎯 新架构：初始化
     */
    async initNewArchitecture() {
      if (this.isInitializing || this.bluetoothInitialized) {
        console.log("🔄 已在初始化或已初始化，跳过")
        return
      }

      console.log("🎯 开始新架构初始化")
      this.isInitializing = true

      try {
        // 设置回调函数
        this.setupCallbacks()

        // 初始化蓝牙
        await toyCarBleUtil.initBluetooth()

        this.bluetoothInitialized = true
        this.bBleConnected = true

        console.log("✅ 新架构初始化完成")
        this.addEventLog("新架构蓝牙初始化完成")

        // 启动状态更新
        this.startStateUpdates()
      } catch (error) {
        console.error("❌ 新架构初始化失败:", error)
        this.bBleConnected = false
        this.bluetoothInitialized = false

        uni.showToast({
          title: "蓝牙初始化失败，请检查蓝牙权限",
          icon: "none",
          duration: 3000,
        })
      } finally {
        this.isInitializing = false
      }
    },

    /**
     * 🎯 新架构：设置回调函数
     */
    setupCallbacks() {
      // 设置数据接收回调
      toyCarBleUtil.setDataCallback((data) => {
        if (data === "CLEAR_DATA") {
          this.allDataList = []
          return
        }

        const receiveItem = {
          ...data,
          type: "receive",
          meaning: this.interpretCommand(data.data),
        }

        if (this.isDevelopment) {
          this.allDataList.unshift(receiveItem)
          if (this.allDataList.length > 100) {
            this.allDataList = this.allDataList.slice(0, 100)
          }
        }
      })

      // 设置发送数据回调
      toyCarBleUtil.setSendDataCallback(
        (hexCommand, commandType, options = {}) => {
          const sendItem = {
            time:
              new Date().toLocaleTimeString() +
              " " +
              new Date().getMilliseconds(),
            data: hexCommand,
            type: "send",
            meaning: this.interpretCommand(hexCommand),
            broadcastStatus: options.broadcastStatus || "success",
            broadcastMessage: options.broadcastMessage || "",
          }

          if (this.isDevelopment) {
            this.allDataList.unshift(sendItem)
            if (this.allDataList.length > 100) {
              this.allDataList = this.allDataList.slice(0, 100)
            }
          }
        }
      )

      // 设置连接状态回调
      toyCarBleUtil.setConnectionCallback((isConnected) => {
        console.log("🔗 连接状态变化:", isConnected)
        this.bBleConnected = isConnected
      })

      // 设置错误回调
      toyCarBleUtil.setErrorCallback((error) => {
        console.error("🚨 蓝牙错误:", error)
        uni.showToast({
          title: "蓝牙错误: " + error,
          icon: "none",
        })
      })
    },

    /**
     * 🎯 新架构：启动状态更新
     */
    startStateUpdates() {
      if (this.stateUpdateTimer) {
        clearInterval(this.stateUpdateTimer)
      }

      // 每100ms更新一次状态
      this.stateUpdateTimer = setInterval(() => {
        this.updateStates()
      }, 100)
    },

    /**
     * 🎯 新架构：停止状态更新
     */
    stopStateUpdates() {
      if (this.stateUpdateTimer) {
        clearInterval(this.stateUpdateTimer)
        this.stateUpdateTimer = null
      }
    },

    /**
     * 🎯 新架构：更新状态
     */
    updateStates() {
      // 从新架构蓝牙模块获取状态
      this.buttonStates = toyCarBleUtil.getButtonStates()
      this.activeBroadcasts = toyCarBleUtil.getActiveBroadcasts()

      // 更新驻车状态
      this.isParkingActive = this.buttonStates.parking || false
    },

    /**
     * 监听蓝牙状态变化
     */
    startBluetoothStateListener() {
      // 监听蓝牙适配器状态变化
      try {
        uni.onBluetoothAdapterStateChange((res) => {
          console.log("🔄 蓝牙状态变化:", res)

          // 🎯 防抖处理，避免频繁触发
          if (this.bluetoothStateChangeTimer) {
            clearTimeout(this.bluetoothStateChangeTimer)
          }

          this.bluetoothStateChangeTimer = setTimeout(() => {
            this.handleBluetoothStateChange(res)
          }, 500) // 500ms防抖
        })
      } catch (error) {
        console.log("❌ 蓝牙状态监听启动失败:", error)
      }
    },

    /**
     * 处理蓝牙状态变化
     */
    handleBluetoothStateChange(res) {
      const wasEnabled = this.bluetoothEnabled
      this.bluetoothEnabled = res.available

      // 只在状态真正改变时处理
      if (wasEnabled === res.available) {
        console.log("🔄 蓝牙状态无变化，跳过处理")
        return
      }

      if (res.available) {
        console.log("✅ 蓝牙已打开，重新初始化服务")
        // 蓝牙打开时重新初始化
        this.reinitializeBluetoothServices()
      } else {
        console.log("❌ 蓝牙已关闭，清理状态")
        // 蓝牙关闭时清理状态
        this.cleanupBluetoothState()
      }
    },

    /**
     * 停止蓝牙状态监听
     */
    stopBluetoothStateListener() {
      try {
        uni.offBluetoothAdapterStateChange()
        console.log("🛑 停止蓝牙状态监听")
      } catch (error) {
        console.log("❌ 停止蓝牙状态监听失败:", error)
      }
    },

    /**
     * 清理所有定时器
     */
    cleanupTimers() {
      console.log("🧹 清理所有定时器")

      // 清理蓝牙状态相关定时器
      if (this.bluetoothStateTimer) {
        clearInterval(this.bluetoothStateTimer)
        this.bluetoothStateTimer = null
      }

      if (this.bluetoothStateChangeTimer) {
        clearTimeout(this.bluetoothStateChangeTimer)
        this.bluetoothStateChangeTimer = null
      }

      if (this.serviceConnectionTimer) {
        clearInterval(this.serviceConnectionTimer)
        this.serviceConnectionTimer = null
      }

      // 清理动画定时器
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
    },

    /**
     * 清理内存数据
     */
    cleanupMemory() {
      console.log("🧹 清理内存数据")

      // 清理数据列表
      this.allDataList = []

      // 重置状态
      this.wheelAngle = 0
      this.isReconnecting = false
      this.isInitializing = false
    },

    /**
     * 重新初始化蓝牙服务
     */
    async reinitializeBluetoothServices() {
      console.log("🔄 重新初始化蓝牙服务")

      // 🎯 检查当前蓝牙服务状态，避免不必要的重置
      const serviceStatus = toyCarBleUtil.checkServiceStatus()
      console.log("🔍 当前服务状态:", serviceStatus)

      // 如果服务状态正常，只是状态监听触发，不需要重新初始化
      if (
        serviceStatus.advertiseReady &&
        serviceStatus.discoveryReady &&
        serviceStatus.hasServer
      ) {
        console.log("✅ 蓝牙服务状态正常，跳过重新初始化")

        // 只恢复连接状态，不重置服务
        if (!this.bBleConnected) {
          this.bBleConnected = true
          this.bluetoothInitialized = true
          console.log("🔗 恢复蓝牙连接状态")
        }
        return
      }

      // 服务状态异常，需要重新初始化
      console.log("⚠️ 蓝牙服务状态异常，执行重新初始化")

      // 重置状态
      this.bluetoothInitialized = false
      this.bBleConnected = false
      this.advertisingServiceStatus = false
      this.receivingServiceStatus = false
      this.deviceInitialized = false // 重置设备初始化状态

      // 🎯 重置蓝牙工具类状态
      toyCarBleUtil.resetInitializationState()

      // 重新初始化
      await this.checkBluetoothStatus()
    },

    /**
     * 清理蓝牙状态
     */
    cleanupBluetoothState() {
      console.log("🧹 清理蓝牙状态")

      this.bluetoothInitialized = false
      this.bBleConnected = false
      this.advertisingServiceStatus = false
      this.receivingServiceStatus = false

      // 停止广播
      if (this.isBroadcasting) {
        this.stopBroadcastSafely("bluetooth_disabled")
      }

      this.addEventLog("蓝牙已关闭，服务已停止")
    },

    /**
     * 检查蓝牙状态
     */
    async checkBluetoothStatus() {
      // 防止重复初始化
      if (this.isInitializing || this.bluetoothInitialized) {
        console.log("  蓝牙正在初始化或已初始化，跳过")
        return
      }

      console.log(" 🔍 开始检查蓝牙状态")
      this.isInitializing = true

      try {
        await this.initBluetoothWithCheck()
        console.log("✅ 蓝牙初始化成功")

        // 启动服务连接监测
        this.startServiceConnectionMonitor()
      } catch (error) {
        console.error("❌ 蓝牙初始化失败:", error)
        this.bBleConnected = false
        this.bluetoothInitialized = false

        // 只有在真正的权限问题时才显示弹窗
        if (this.shouldShowBluetoothDialog(error)) {
          this.showBluetoothPermissionDialog()
        }
      } finally {
        this.isInitializing = false
      }
    },

    /**
     * 带检查的蓝牙初始化
     */
    async initBluetoothWithCheck() {
      // 先尝试初始化蓝牙适配器
      await this.openBluetoothAdapter()

      // 获取蓝牙适配器状态
      const adapterState = await this.getBluetoothAdapterState()

      if (!adapterState.available) {
        throw new Error("bluetooth not available")
      }

      // 蓝牙可用，继续初始化
      this.initBluetooth()
    },

    /**
     * 判断是否应该显示蓝牙权限对话框
     */
    shouldShowBluetoothDialog(error) {
      // 检查错误信息，判断是否是权限问题
      const errorMsg = error.errMsg || error.message || ""

      console.log("🔍 检查错误类型:", errorMsg)

      // 不需要显示弹窗的错误（适配器已初始化等）
      const ignoredErrors = [
        "already opened",
        "10009", // 当前连接已断开
      ]

      // 如果是可忽略的错误，不显示弹窗
      if (ignoredErrors.some((errType) => errorMsg.includes(errType))) {
        return false
      }

      // 需要显示权限对话框的错误
      const permissionErrors = [
        "not available",
        "bluetooth not available",
        "permission denied",
        "unauthorized",
        "not authorized",
        "open fail", // 蓝牙未开启
        "10001", // 蓝牙适配器错误（包括未开启）
        "10004", // 未授权
        "10005", // 系统版本不支持
      ]

      return permissionErrors.some((errType) =>
        errorMsg.toLowerCase().includes(errType)
      )
    },

    /**
     * 打开蓝牙适配器
     */
    openBluetoothAdapter() {
      return new Promise((resolve, reject) => {
        wx.openBluetoothAdapter({
          success: (res) => {
            console.log("✅ 蓝牙适配器初始化成功:", res)
            resolve(res)
            // 初始化成功先停止广播一下 避免残留
            toyCarBleUtil.stopAdvertising()
          },
          fail: (error) => {
            console.error("❌ 蓝牙适配器初始化失败:", error)

            // 检查是否是因为适配器已经初始化
            const errorMsg = error.errMsg || ""
            if (
              errorMsg.includes("already opened") ||
              errorMsg.includes("10001")
            ) {
              console.log("ℹ️ 蓝牙适配器已经初始化")
              resolve({ alreadyOpened: true })
            } else {
              reject(error)
            }
          },
        })
      })
    },

    /**
     * 获取蓝牙适配器状态
     */
    getBluetoothAdapterState() {
      return new Promise((resolve, reject) => {
        wx.getBluetoothAdapterState({
          success: (res) => {
            console.log("🔍 蓝牙适配器状态:", res)
            resolve(res)
          },
          fail: (error) => {
            console.error("❌ 获取蓝牙适配器状态失败:", error)
            reject(error)
          },
        })
      })
    },

    /**
     * 显示蓝牙权限提示
     */
    showBluetoothPermissionDialog() {
      uni.showModal({
        title: "提示",
        content: "蓝牙连接失败，请打开蓝牙和定位",
        showCancel: true,
        confirmText: "确定",
        cancelText: "取消",
      })

      console.log("💡 已提示用户开启蓝牙和定位权限")
      this.addEventLog("提示用户开启蓝牙权限")
    },

    /**
     * 开始蓝牙状态监听
     */
    startBluetoothStateMonitoring() {
      // 清除之前的定时器
      if (this.bluetoothStateTimer) {
        clearInterval(this.bluetoothStateTimer)
      }

      // 只有在蓝牙初始化完成后才开始监听
      if (!this.bluetoothInitialized) {
        console.log("⏳ 蓝牙未初始化，暂不启动状态监听")
        return
      }

      // 每10秒检查一次蓝牙状态（降低频率）
      this.bluetoothStateTimer = setInterval(() => {
        this.checkCurrentBluetoothState(true) // 强制检查
      }, 10000)

      console.log("🔍 开始蓝牙状态监听")
    },

    /**
     * 停止蓝牙状态监听
     */
    stopBluetoothStateMonitoring() {
      if (this.bluetoothStateTimer) {
        clearInterval(this.bluetoothStateTimer)
        this.bluetoothStateTimer = null
        console.log("🛑 停止蓝牙状态监听")
      }
    },

    /**
     * 检查当前蓝牙状态
     */
    async checkCurrentBluetoothState(forceCheck = false) {
      const now = Date.now()

      // 避免频繁检查，除非强制检查
      if (!forceCheck && now - this.lastBluetoothCheck < 5000) {
        return
      }

      this.lastBluetoothCheck = now

      try {
        const adapterState = await this.getBluetoothAdapterState()

        if (!adapterState.available) {
          // 蓝牙不可用，更新状态
          if (this.bBleConnected) {
            console.log("❌ 检测到蓝牙已关闭")
            this.bBleConnected = false
            this.bluetoothInitialized = false
            this.addEventLog("检测到蓝牙已关闭")
          }
        } else {
          // 🎯 蓝牙可用，检查服务状态是否正常
          if (!this.bBleConnected && !this.isInitializing) {
            console.log("✅ 检测到蓝牙已开启，检查服务状态")

            // 检查蓝牙工具的实际状态
            const serviceStatus = toyCarBleUtil.checkServiceStatus()
            console.log("🔍 蓝牙服务状态检查:", serviceStatus)

            if (serviceStatus.advertiseReady && serviceStatus.discoveryReady) {
              // 服务实际上是正常的，只是连接状态不同步，直接恢复状态
              console.log("✅ 蓝牙服务实际正常，恢复连接状态")
              this.bBleConnected = true
              this.bluetoothInitialized = true
              this.addEventLog("蓝牙服务状态已恢复")
            } else {
              // 服务确实有问题，需要重新初始化
              console.log("🔄 蓝牙服务异常，重新初始化")
              this.addEventLog("检测到蓝牙已开启，尝试重新连接")
              this.checkBluetoothStatus()
            }
          }
        }
      } catch (error) {
        // 获取状态失败，可能是蓝牙已关闭或未初始化
        const errorMsg = error.errMsg || error.message || ""

        if (errorMsg.includes("not init")) {
          // 蓝牙适配器未初始化，这是正常情况，不需要处理
          console.log("ℹ️ 蓝牙适配器未初始化，跳过状态检查")
          return
        }

        if (this.bBleConnected) {
          console.log("❌ 蓝牙状态检查失败，可能已关闭:", error)
          this.bBleConnected = false
          this.bluetoothInitialized = false
          this.addEventLog("蓝牙连接丢失")
        }
      }
    },

    /**
     * 初始化蓝牙功能
     */
    async initBluetooth() {
      console.log("🔧 开始初始化蓝牙功能")

      // 设置蓝牙工具回调
      toyCarBleUtil.setConnectionCallback(this.onConnectionChange)
      toyCarBleUtil.setDataCallback(this.onDataReceived)
      toyCarBleUtil.setErrorCallback(this.onBluetoothError)
      toyCarBleUtil.setSendDataCallback(this.onSendData)

      // 初始化蓝牙
      toyCarBleUtil.initBluetooth()

      // 等待蓝牙完全初始化
      await this.waitForBluetoothReady()

      // 标记初始化完成
      this.bluetoothInitialized = true
      this.bBleConnected = true

      this.addEventLog("蓝牙初始化完成")
      console.log("✅ 蓝牙初始化完成，可以发送指令")

      // 🎯 移除自动设备初始化，让用户手动控制
      // 自动初始化会导致设备一直响应，影响用户体验

      // 初始化完成后启动状态监听
      this.startBluetoothStateMonitoring()
    },

    /**
     * 等待蓝牙准备就绪
     */
    waitForBluetoothReady() {
      return new Promise((resolve) => {
        // 检查蓝牙工具是否准备就绪
        const checkReady = () => {
          if (toyCarBleUtil.advertiseReady && toyCarBleUtil.discoveryReady) {
            console.log("✅ 蓝牙适配器准备就绪")
            resolve()
          } else {
            console.log("⏳ 等待蓝牙适配器准备就绪...")
            setTimeout(checkReady, 100)
          }
        }
        checkReady()
      })
    },

    /**
     * 蓝牙连接状态变化回调
     */
    onConnectionChange(isConnected) {
      console.log("🔗 蓝牙连接状态变化:", isConnected)
      this.bBleConnected = isConnected
    },

    /**
     * 蓝牙数据接收回调
     */
    onDataReceived(data) {
      if (data === "CLEAR_DATA") {
        this.allDataList = []
        return
      }

      // 更新最后响应时间
      this.lastResponseTime = Date.now()

      // 始终显示接收到的数据到列表，不做任何过滤
      const receiveItem = {
        ...data,
        type: "receive",
        meaning: this.interpretCommand(data.data),
      }
      // 🎯 只在开发模式下处理数据，提高性能
      if (this.isDevelopment) {
        this.allDataList.unshift(receiveItem)

        // 🎯 限制数据列表长度，防止内存泄漏
        if (this.allDataList.length > 100) {
          this.allDataList = this.allDataList.slice(0, 100)
        }

        console.log("📡 接收到数据:", data.data, "解析:", receiveItem.meaning)
      }

      // 处理数据接收状态
      this.handleDataReception()
    },

    /**
     * 蓝牙发送数据回调
     */
    onSendData(hexCommand, commandType, options = {}) {
      const sendItem = {
        time:
          new Date().toLocaleTimeString() + " " + new Date().getMilliseconds(),
        data: hexCommand,
        type: "send",
        meaning: this.interpretCommand(hexCommand),
        broadcastStatus: options.broadcastStatus || "success",
        broadcastMessage: options.broadcastMessage || "",
      }
      // 🎯 只在开发模式下处理数据，提高性能
      if (this.isDevelopment) {
        this.allDataList.unshift(sendItem)

        // 🎯 限制数据列表长度，防止内存泄漏
        if (this.allDataList.length > 100) {
          this.allDataList = this.allDataList.slice(0, 100)
        }
      }
    },

    /**
     * 蓝牙错误回调
     */
    onBluetoothError(error) {
      console.error("🚨 蓝牙错误:", error)
      wx.showToast({
        title: "蓝牙错误: " + error,
        icon: "none",
      })
    },

    /**
     * 重连蓝牙
     */
    async handleReconnectBluetooth() {
      console.log("🔄 用户点击重连蓝牙")
      this.isReconnecting = true

      // 添加重连事件记录
      this.addEventLog("用户手动重连蓝牙")

      // 🎯 简单直接：检查系统蓝牙是否开启
      try {
        // 获取蓝牙状态
        const result = await uni.getBluetoothAdapterState()
        console.log("🔍 蓝牙状态原始结果:", result)

        // 🎯 正确解构蓝牙状态
        let bluetoothState
        if (Array.isArray(result)) {
          bluetoothState = result[1] || result[0]
        } else {
          bluetoothState = result
        }

        console.log("🔍 解析后的蓝牙状态:", bluetoothState)

        if (!bluetoothState || !bluetoothState.available) {
          // 蓝牙没开，提示用户
          console.log("❌ 系统蓝牙未开启")
          this.promptUserToEnableBluetooth()
          this.isReconnecting = false
          return
        }

        // 蓝牙开了，重新初始化服务
        console.log("✅ 系统蓝牙已开启，重新初始化服务")

        // 重置状态
        this.bluetoothInitialized = false
        this.bBleConnected = false
        this.isInitializing = false
        this.advertisingServiceStatus = false
        this.receivingServiceStatus = false

        // 重置蓝牙工具状态
        toyCarBleUtil.resetInitializationState()

        // 重新初始化蓝牙服务
        await this.checkBluetoothStatus()
        console.log("✅ 蓝牙重新初始化完成")
        this.addEventLog("蓝牙重新初始化完成")
      } catch (error) {
        console.log("❌ 蓝牙检查失败:", error)
        // 检查失败，可能是蓝牙未开启或权限问题
        this.promptUserToEnableBluetooth()
      }

      this.isReconnecting = false
    },

    // ===== 左侧控制区按钮方法 =====

    /**
     * 🎯 新架构：发送指令的统一方法 - 按钮按下事件
     */
    async sendCommand(command) {
      if (!this.bluetoothInitialized || !this.bBleConnected) {
        console.log("⚠️ 蓝牙未连接，显示权限提示")
        uni.showToast({
          title: "请先初始化蓝牙",
          icon: "none",
          duration: 2000,
        })
        return
      }

      if (this.isInitializing) {
        console.log("⏳ 蓝牙正在初始化，请稍候")
        uni.showToast({
          title: "蓝牙正在初始化，请稍候",
          icon: "none",
          duration: 1500,
        })
        return
      }

      console.log("📤 按钮按下，发送指令:", command)
      this.addEventLog(`${this.getCommandName(command)}按钮按下`)

      // 🎯 新架构：直接调用对应的方法
      switch (command) {
        case "forward":
          toyCarBleUtil.sendForward()
          break
        case "backward":
          toyCarBleUtil.sendBackward()
          break
        case "left":
          toyCarBleUtil.sendLeft()
          break
        case "right":
          toyCarBleUtil.sendRight()
          break
        case "brake":
          this.isStop = true
          toyCarBleUtil.sendBrake()
          break
        case "parking":
          // 切换驻车状态
          const newParkingState = !this.isParkingActive
          console.log(
            `🅿️ 驻车状态切换: ${this.isParkingActive} → ${newParkingState}`
          )
          toyCarBleUtil.sendParking(newParkingState ? 1 : 0)
          break
        default:
          console.log("❓ 未知指令:", command)
          return
      }

      this.addEventLog(`发送 ${this.getCommandName(command)}指令`)
    },

    /**
     * 按钮释放处理 - 按钮抬起事件
     */
    handleButtonRelease(buttonType) {
      console.log("🔄 按钮抬起:", buttonType)

      // 记录抬起事件
      this.addEventLog(`${this.getCommandName(buttonType)}按钮抬起`)

      // 更新按钮状态并检查是否真正抬起
      let isReallyReleased = false

      switch (buttonType) {
        case "forward":
          this.isForwardActive = false
          this.isForwardActiveEnd = true
          // 检查是否真正抬起：确定都是松开的态为true
          isReallyReleased = !this.isForwardActive && this.isForwardActiveEnd
          // 🎯 清理原始运动指令
          if (isReallyReleased) {
            toyCarBleUtil.originalMotionPayload = null
            console.log("🗑️ 清理原始运动指令")
          }
          break
        case "back":
          this.isBackActive = false
          this.isBackActiveEnd = true
          // 检查是否真正抬起
          isReallyReleased = !this.isBackActive && this.isBackActiveEnd
          // 🎯 清理原始运动指令
          if (isReallyReleased) {
            toyCarBleUtil.originalMotionPayload = null
            console.log("🗑️ 清理原始运动指令")
          }
          break
        case "left":
          this.isLeftTurnActive = false
          this.isLeftTurnActiveEnd = true
          // 检查左转是否真正抬起
          isReallyReleased = !this.isLeftTurnActive && this.isLeftTurnActiveEnd
          // 🎯 检查是否在运动中
          if (isReallyReleased) {
            const isInMotion = this.isForwardActive || this.isBackActive
            if (isInMotion) {
              // 运动中的转向结束，不发送停转指令，让车辆自然回到直线前进
              console.log("🎯 运动中转向结束，保持直线前进")
              isReallyReleased = false // 不停止广播
            } else {
              // 独立转向结束，发送停转指令
              toyCarBleUtil.sendStopTurn()
              // 🎯 清理可能错误设置的原始运动指令
              toyCarBleUtil.originalMotionPayload = null
              console.log("🗑️ 清理独立转向的原始运动指令")
            }
          }
          break
        case "right":
          this.isRightTurnActive = false
          this.isRightTurnActiveEnd = true
          // 检查右转是否真正抬起
          isReallyReleased =
            !this.isRightTurnActive && this.isRightTurnActiveEnd
          // 🎯 检查是否在运动中
          if (isReallyReleased) {
            const isInMotion = this.isForwardActive || this.isBackActive
            if (isInMotion) {
              // 运动中的转向结束，不发送停转指令，让车辆自然回到直线前进
              console.log("🎯 运动中转向结束，保持直线前进")
              isReallyReleased = false // 不停止广播
            } else {
              // 独立转向结束，发送停转指令
              toyCarBleUtil.sendStopTurn()
              // 🎯 清理可能错误设置的原始运动指令
              toyCarBleUtil.originalMotionPayload = null
              console.log("🗑️ 清理独立转向的原始运动指令")
            }
          }
          break
        case "brake":
          // 刹车按钮处理
          this.isStop = false // 🎯 重置刹车状态
          isReallyReleased = true
          break
        case "parking":
          // 驻车按钮处理（等待设备响应后再停止广播）
          isReallyReleased = false // 不立即停止广播，等待设备响应
          break
        case "steer":
          // 方向盘释放处理
          this.steeringAngle = 0
          isReallyReleased = true
          break
        default:
          isReallyReleased = true
      }

      // 只有确认抬起才停止广播
      if (isReallyReleased) {
        console.log("✅ 确认按钮已抬起，停止广播")
        this.stopBroadcastSafely(buttonType)
      } else {
        console.log("⚠️ 按钮状态不一致，继续监控")
        // 继续监控状态
        this.monitorBroadcastState()
      }
    },

    /**
     * 播放按钮处理
     */
    handlePlayPress() {
      this.isPlay = !this.isPlay
      console.log("🎵 播放状态切换:", this.isPlay)
      this.addEventLog(`播放状态切换为: ${this.isPlay ? "播放" : "暂停"}`)
    },

    /**
     * 方向盘触摸开始
     */
    handleTouchStart(event) {
      console.log("🎯 方向盘触摸开始")
    },

    /**
     * 方向盘转向变化
     */
    handleSteerChange(angle) {
      this.steeringAngle = angle
      console.log("🎯 方向盘角度变化:", angle)
    },

    /**
     * 档位切换
     */
    async sendGear(gear) {
      if (!this.bluetoothInitialized || !this.bBleConnected) {
        console.log("⚠️ 蓝牙未连接，静默忽略档位切换:", gear)
        // 蓝牙未初始化时静默忽略，不显示弹窗
        return
      }

      console.log("⚙️ 切换档位:", gear)

      // 🎯 检查是否在运动过程中
      const isMoving =
        this.isBroadcasting && (this.isForwardActive || this.isBackActive)

      if (isMoving) {
        console.log("🎯 运动中切换档位，保持当前广播状态")
        // 运动过程中切换档位，不改变广播状态
        toyCarBleUtil.sendGear(gear, true)
        this.addEventLog(`运动中切换档位: ${gear}档`)
      } else {
        console.log("🎯 停车状态切换档位")
        // 停车状态下的档位切换
        this.isBroadcasting = true
        this.cachedCommand = "gear"
        toyCarBleUtil.sendGear(gear, false)
        this.addEventLog(`切换到${gear}档`)
      }
    },

    /**
     * 档位释放处理
     */
    handleGearRelease(gear) {
      console.log("🔄 档位按钮释放:", gear)

      // 档位释放时停止广播
      if (this.isBroadcasting && this.cachedCommand === "gear") {
        console.log("✅ 档位切换完成，停止广播")
        this.stopBroadcastSafely("gear")
      }

      this.addEventLog(`档位${gear}按钮释放`)
    },

    // ===== 方向盘触摸事件方法 =====

    /**
     * 初始化方向盘位置
     */
    initWheelPosition() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this)
        query
          .select(".steering-wheel")
          .boundingClientRect((rect) => {
            if (rect) {
              this.wheelCenter = {
                x: rect.left + rect.width / 2,
                y: rect.top + rect.height / 2,
              }
            }
            resolve()
          })
          .exec()
      })
    },

    /**
     * 触摸开始
     */
    async onTouchStart(e) {
      this.stopAnimation()
      await this.initWheelPosition()

      const touch = e.touches[0]
      this.startAngle = this.calculateAngle(touch.clientX, touch.clientY)
      this.isTouching = true
      this.handleTouchStart()
    },

    /**
     * 触摸移动
     */
    onTouchMove(e) {
      if (!this.isTouching || !this.wheelCenter.x) return

      const touch = e.touches[0]
      const currentAngle = this.calculateAngle(touch.clientX, touch.clientY)
      let angleDiff = currentAngle - this.startAngle

      // 标准化角度差
      angleDiff = this.normalizeAngle(angleDiff)

      // 计算目标角度（灵敏度增强）
      const targetAngle = angleDiff * (180 / Math.PI) * 2.5
      this.wheelAngle = Math.min(Math.max(targetAngle, -90), 90)
      console.log("方向盘角度", this.wheelAngle)
      this.handleSteerChange(this.wheelAngle)
    },

    /**
     * 触摸结束
     */
    onTouchEnd() {
      if (!this.isTouching) return

      this.isTouching = false
      this.returnToCenter()
      this.handleButtonRelease("steer")
    },

    /**
     * 计算角度
     */
    calculateAngle(x, y) {
      return Math.atan2(y - this.wheelCenter.y, x - this.wheelCenter.x)
    },

    /**
     * 标准化角度
     */
    normalizeAngle(angle) {
      while (angle > Math.PI) angle -= 2 * Math.PI
      while (angle < -Math.PI) angle += 2 * Math.PI
      return angle
    },

    /**
     * 回中动画
     */
    returnToCenter() {
      const startAngle = this.wheelAngle
      const duration = 400
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用缓动函数
        const easeProgress = this.easeOutElastic(progress)
        this.wheelAngle = startAngle * (1 - easeProgress)

        if (progress < 1) {
          this.animationTimer = setTimeout(animate, 16)
        } else {
          this.wheelAngle = 0
          this.animationTimer = null
        }
      }

      this.animationTimer = setTimeout(animate, 16)
    },

    /**
     * 停止动画
     */
    stopAnimation() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
    },

    /**
     * 缓动函数
     */
    easeOutElastic(t) {
      const p = 0.3
      return (
        Math.pow(2, -10 * t) * Math.sin(((t - p / 4) * (2 * Math.PI)) / p) + 1
      )
    },

    /**
     * 速度百分比设置
     */
    async sendSpeedPercent(speedPercent) {
      if (!this.bluetoothInitialized || !this.bBleConnected) {
        console.log("⚠️ 蓝牙未连接，静默忽略速度设置:", speedPercent)
        // 蓝牙未初始化时静默忽略，不显示弹窗
        return
      }

      console.log("🚀 设置速度百分比:", speedPercent)
      toyCarBleUtil.sendCustomSpeed(Math.round((speedPercent * 100) / 100))
      this.addEventLog(`设置速度为${speedPercent}%`)
    },

    /**
     * 一键操作
     */
    onePress() {
      console.log("🔘 一键操作")
      this.addEventLog("执行一键操作")
    },

    // ===== 按钮状态管理方法 =====

    /**
     * 检查是否有其他按钮正在活跃
     */
    hasOtherActiveButtons(currentCommand) {
      const activeButtons = []

      if (currentCommand !== "forward" && this.isForwardActive) {
        activeButtons.push("前进")
      }

      if (currentCommand !== "backward" && this.isBackActive) {
        activeButtons.push("后退")
      }

      if (currentCommand !== "left" && this.isLeftTurnActive) {
        activeButtons.push("左转")
      }

      if (currentCommand !== "right" && this.isRightTurnActive) {
        activeButtons.push("右转")
      }

      if (activeButtons.length > 0) {
        console.log("🔍 检测到活跃的按钮:", activeButtons.join(", "))
        return true
      }

      return false
    },

    /**
     * 清理其他按钮状态，确保只有一个按钮活跃
     */
    clearOtherButtonStates(currentCommand) {
      console.log("🧹 清理其他按钮状态，当前指令:", currentCommand)

      // 记录之前活跃的按钮
      const previousActiveButtons = []
      // 如果当前指令不是前进，且前进按钮活跃，则清理前进状态
      if (currentCommand !== "forward" && this.isForwardActive) {
        this.isForwardActive = false
        this.isForwardActiveEnd = true
        previousActiveButtons.push("前进")
      }

      if (currentCommand !== "backward" && this.isBackActive) {
        this.isBackActive = false
        this.isBackActiveEnd = true
        previousActiveButtons.push("后退")
      }

      if (currentCommand !== "left" && this.isLeftTurnActive) {
        this.isLeftTurnActive = false
        this.isLeftTurnActiveEnd = true
        previousActiveButtons.push("左转")
      }

      if (currentCommand !== "right" && this.isRightTurnActive) {
        this.isRightTurnActive = false
        this.isRightTurnActiveEnd = true
        previousActiveButtons.push("右转")
      }

      // 记录清理的按钮
      if (previousActiveButtons.length > 0) {
        console.log("🧹 清理了以下按钮状态:", previousActiveButtons.join(", "))
        this.addEventLog(
          `自动清理按钮状态: ${previousActiveButtons.join(", ")}`
        )
      }
    },

    /**
     * 获取指令名称
     */
    getCommandName(command) {
      const nameMap = {
        forward: "前进",
        backward: "后退",
        back: "后退",
        left: "左转",
        right: "右转",
        turn: "转向",
        brake: "刹车",
        parking: "驻车",
      }
      return nameMap[command] || command
    },

    /**
     * 监控广播状态
     */
    monitorBroadcastState() {
      // 检查当前是否应该继续广播
      const shouldContinue = this.shouldContinueBroadcast()
      // 判断必须是没有按钮按下 并且 当前正在广播
      if (!shouldContinue && this.isBroadcasting) {
        console.log("🛑 检测到应该停止广播")
        this.stopBroadcastSafely("monitor")
      }
    },

    /**
     * 判断是否应该继续广播
     */
    shouldContinueBroadcast() {
      // 检查是否有任何按钮仍在按下状态（使用现有状态变量）  有一个是在按下就是true
      const hasActiveButton =
        this.isForwardActive ||
        this.isBackActive ||
        this.isLeftTurnActive ||
        this.isRightTurnActive

      console.log("🔍 广播状态检查:", {
        hasActiveButton,
        isBroadcasting: this.isBroadcasting,
        cachedCommand: this.cachedCommand,
      })

      return hasActiveButton
    },

    /**
     * 安全停止广播
     */
    stopBroadcastSafely(source) {
      console.log(`🛑 安全停止广播，来源: ${source}`)

      // 停止广播  全部松开按钮为true的时候
      toyCarBleUtil.stopAdvertising()

      // 更新全局广播状态并清空缓存指令
      this.isBroadcasting = false
      const previousCommand = this.cachedCommand
      this.cachedCommand = null

      console.log("🔄 更新广播状态: 已停止广播")
      if (previousCommand) {
        console.log("🗑️ 清空缓存指令:", previousCommand)
      }

      // 记录停止事件
      this.addEventLog(
        `广播已停止 (${source})${
          previousCommand
            ? ` - 清空${this.getCommandName(previousCommand)}指令缓存`
            : ""
        }`
      )
    },

    // ===== 数据接收处理 =====

    /**
     * 处理数据接收状态
     */
    handleDataReception() {
      // 检查是否是驻车响应，强制停止驻车广播
      if (this.allDataList.length > 0) {
        const latestData = this.allDataList[0]
        if (
          latestData.type === "receive" &&
          latestData.meaning &&
          latestData.meaning.includes("驻车响应")
        ) {
          console.log("🅿️ 收到驻车响应:", latestData.meaning)
          console.log("🅿️ 当前驻车状态:", this.isParkingActive)
          console.log("🅿️ 强制停止所有广播")

          // 强制停止所有广播，特别是驻车指令
          if (this.isBroadcasting) {
            this.stopBroadcastSafely("parking")
          }

          // 额外确保停止广播
          toyCarBleUtil.forceStopAllAdvertising()
        }
      }

      // 简化处理，始终记录接收状态
      console.log("📡 数据接收处理完成")
    },

    /**
     * 判断是否应该继续接收
     */
    shouldContinueReceiving() {
      // 只要有任何按钮处于活跃状态就继续接收
      return (
        this.isForwardActive ||
        this.isBackActive ||
        this.isLeftTurnActive ||
        this.isRightTurnActive
      )
    },

    // ===== 工具方法 =====

    /**
     * 获取当前广播状态信息
     */
    getBroadcastStatusInfo() {
      return {
        isBroadcasting: this.isBroadcasting,
        cachedCommand: this.cachedCommand,
        cachedCommandName: this.cachedCommand
          ? this.getCommandName(this.cachedCommand)
          : null,
        activeButtons: {
          forward: this.isForwardActive,
          back: this.isBackActive,
          leftTurn: this.isLeftTurnActive,
          rightTurn: this.isRightTurnActive,
        },
      }
    },

    /**
     * 添加事件日志
     */
    addEventLog(message) {
      const eventItem = {
        time:
          new Date().toLocaleTimeString() + " " + new Date().getMilliseconds(),
        type: "event",
        meaning: message,
      }
      // 🎯 只在开发模式下处理数据，提高性能
      if (this.isDevelopment) {
        this.allDataList.unshift(eventItem)

        // 🎯 限制数据列表长度，防止内存泄漏
        if (this.allDataList.length > 100) {
          this.allDataList = this.allDataList.slice(0, 100)
        }
      }
    },

    /**
     * 解释指令含义
     */
    interpretCommand(hexData) {
      if (!hexData) return "空数据"

      const data = hexData.toUpperCase()

      // V1.10协议指令解析
      // 运动指令 (0x10)
      if (data.includes("81000210")) {
        if (data.includes("0113")) return "前进指令"
        if (data.includes("0214")) return "后退指令"
        if (data.includes("0012")) return "停止运动"
        if (data.includes("0315")) return "刹车指令"
      }

      // 运动响应 (0x11)
      if (data.includes("81000211")) {
        if (data.includes("0114")) return "前进响应"
        if (data.includes("0215")) return "后退响应"
        if (data.includes("0013")) return "停止运动响应"
        if (data.includes("0316")) return "刹车响应"
      }

      // 速度设置指令 (0x12)
      if (data.includes("81000212")) {
        // 提取速度值（第5-6位）
        const speedMatch = data.match(/81000212(.{2})/)
        if (speedMatch) {
          const speedHex = speedMatch[1]
          const speedDec = parseInt(speedHex, 16)
          return `速度设置指令 (${speedDec}%)`
        }
        return "速度设置指令"
      }

      // 速度响应 (0x13)
      if (data.includes("81000213")) {
        return "速度设置响应"
      }

      // 转向指令 (0x14)
      if (data.includes("81000214")) {
        if (data.includes("0117")) return "左转指令"
        if (data.includes("0218")) return "右转指令"
        if (data.includes("0016")) return "停止转向指令"
      }

      // 转向响应 (0x15)
      if (data.includes("81000215")) {
        if (data.includes("0118")) return "左转响应"
        if (data.includes("0219")) return "右转响应"
        if (data.includes("0017")) return "停止转向响应"
      }

      // 驻车指令 (0x1A)
      if (data.includes("8100021A")) {
        if (data.includes("011D")) return "开启驻车指令"
        if (data.includes("001C")) return "关闭驻车指令"
      }

      // 驻车响应 (0x1B)
      if (data.includes("8100021B")) {
        if (data.includes("011E")) return "开启驻车响应"
        if (data.includes("001D")) return "关闭驻车响应"
      }

      return "未知指令"
    },

    /**
     * 清空所有数据
     */
    clearAllData() {
      this.allDataList = []
      console.log("🗑️ 清空所有通信数据")
    },

    /**
     * 切换开发模式
     */
    toggleDevelopmentMode() {
      console.log("🔄 切换开发模式:", !this.isDevelopment)
      this.isDevelopment = !this.isDevelopment
    },

    /**
     * 启动服务连接检测
     */
    startServiceConnectionMonitor() {
      console.log("🔍 启动服务连接检测")

      // 清除之前的定时器
      if (this.serviceConnectionTimer) {
        clearInterval(this.serviceConnectionTimer)
      }

      // 🎯 降低检测频率，每10秒检查一次，减少误判
      this.serviceConnectionTimer = setInterval(() => {
        this.checkServiceConnection()
      }, 10000) // 从3秒改为10秒
    },

    /**
     * 停止服务连接检测
     */
    stopServiceConnectionMonitor() {
      console.log("🛑 停止服务连接检测")
      if (this.serviceConnectionTimer) {
        clearInterval(this.serviceConnectionTimer)
        this.serviceConnectionTimer = null
      }
    },

    /**
     * 检查服务连接状态
     */
    checkServiceConnection() {
      const now = Date.now()

      // 🎯 更智能的连接检测逻辑

      // 1. 如果没有在广播且最近没有发送指令，不需要检查
      if (!this.isBroadcasting && now - this.lastCommandTime > 10000) {
        return
      }

      // 2. 只有在持续发送指令但长时间没有响应时才认为断开
      const hasRecentCommand = now - this.lastCommandTime < 10000 // 10秒内有发送指令
      const hasLongNoResponse =
        this.lastResponseTime > 0 &&
        now - this.lastResponseTime > this.connectionTimeoutThreshold * 2 // 10秒没响应

      // 3. 检查蓝牙服务状态是否真的异常
      const serviceStatus = toyCarBleUtil.checkServiceStatus()
      const isServiceAbnormal =
        !serviceStatus.advertiseReady ||
        !serviceStatus.discoveryReady ||
        !serviceStatus.hasServer

      if (hasRecentCommand && hasLongNoResponse && isServiceAbnormal) {
        console.log(
          "⚠️ 确认服务已断开 - 指令发送正常但服务状态异常，上次响应时间:",
          new Date(this.lastResponseTime).toLocaleTimeString()
        )
        this.handleServiceDisconnection()
      } else if (hasLongNoResponse && !isServiceAbnormal) {
        // 没有响应但服务状态正常，可能只是设备暂时没有数据返回
        console.log("ℹ️ 暂时没有响应，但蓝牙服务状态正常，继续监控")
      }
    },

    /**
     * 处理服务断开
     */
    async handleServiceDisconnection() {
      console.log("🚨 处理服务断开")

      // 停止当前广播
      this.stopBroadcastSafely("service_disconnected")

      // 更新连接状态
      this.bBleConnected = false
      this.advertisingServiceStatus = false
      this.receivingServiceStatus = false

      // 显示提示
      uni.showToast({
        title: "设备连接已断开，请重新连接",
        icon: "none",
        duration: 3000,
      })

      // 添加事件日志
      this.addEventLog("检测到服务断开，已停止操作")

      // 尝试重新连接
      setTimeout(() => {
        this.handleReconnectBluetooth()
      }, 1000)
    },
  },

  mounted() {
    // 初始化方向盘位置
    this.initWheelPosition()
  },

  beforeDestroy() {
    // 清理动画定时器
    this.stopAnimation()
  },
}
</script>

<style lang="scss" scoped>
/* 速度控制样式 */
.speed-control-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 10rpx;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8rpx;
  width: 50%; /* 只占一半位置 */
  justify-content: center;
}

.speed-input {
  width: 60rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  text-align: center;
  font-size: 20rpx;
  padding: 0;
  line-height: 40rpx;
}

.speed-unit {
  color: white;
  font-size: 20rpx;
}

.speed-btn {
  width: 50rpx;
  height: 40rpx;
  background: rgba(0, 255, 255, 0.2);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 关键样式修正 */
.landscape-container {
  width: 100vh;
  height: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  transform-origin: 0 0;
  transform: rotate(90deg) translateY(-100%);
  justify-content: space-between;

  .bg-image {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .content-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-between;
    align-items: flex-end;
    position: relative;
  }

  /* 右侧方向盘容器 - 固定右侧定位 */
  .steering-wheel-container {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 10;
  }

  .content-wrapper {
    .ellipse {
      position: absolute;
      bottom: -250rpx;
      left: -10%;
      width: 120%;
      height: 500rpx;
      border-radius: 50%;
      background-color: rgb(34, 34, 34);
    }
  }
}

/* 接收数据显示列表样式 */
.communication-log {
  position: absolute;
  top: 20rpx;
  left: 55%;
  transform: translateX(-50%);
  z-index: 50;
  width: 600rpx;
  height: 650rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* 发送数据项样式 */
.send-item {
  background: rgba(0, 255, 0, 0.1) !important;
  border-left: 3rpx solid #00ff00 !important;
  color: #333 !important;
}

/* 接收数据项样式 */
.receive-item {
  background: rgba(0, 150, 255, 0.1) !important;
  border-left: 3rpx solid #0096ff !important;
  color: #333 !important;
}

/* 事件数据项样式 */
.event-item {
  background: rgba(255, 165, 0, 0.1) !important;
  border-left: 3rpx solid #ffa500 !important;
  color: #333 !important;
  font-style: italic;
}

/* 🎯 广播状态样式 */
.broadcast-status {
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 10rpx;
}

.broadcast-success {
  color: #00aa00;
}

.broadcast-failed {
  color: #ff4444;
}

.broadcast-error {
  font-size: 22rpx;
  color: #ff6666;
  margin-left: 5rpx;
  font-style: italic;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.log-count {
  font-size: 24rpx;
  color: #666;
}

.viewDivider {
  width: 100%;
  height: 2rpx;
  background-color: #a1a1a1;
  margin-top: 10rpx;
  margin-bottom: 15rpx;
}

.scanResultGroup {
  flex-grow: 1;
  overflow-y: scroll;
  max-height: 550rpx;
  padding-right: 10rpx;
}

.result-content {
  margin-top: 15rpx;
  white-space: normal;
  word-break: break-all;
  padding: 10rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  font-size: 24rpx;
  line-height: 1.4;
  color: #333;
  border-left: 3rpx solid #007aff;
}

/* 新布局样式 */
/* 左上角 WiFi 状态 */
.wifi-status-corner {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 30;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  cursor: pointer;
}

.wifi-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.wifi-connected {
  filter: brightness(1.2);
}

.wifi-disconnected {
  filter: grayscale(1) brightness(0.8);
}

.wifi-text {
  font-size: 24rpx;
  font-weight: 500;
}

.text-connected {
  color: #00ff88;
}

.text-disconnected {
  color: #ff6b35;
}

.top-speed-control {
  position: absolute;
  top: 50rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 20;
  width: 50%;
  display: flex;
  justify-content: center;
}

.main-controls-layout {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 40%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 50rpx;
}

.left-side {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.right-side {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

/* 底部仪表盘区域 */
.bottom-gauge-area {
  position: absolute;
  bottom: 0rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  // background-color: pink;
  .steering-wheel {
    width: 300rpx;
    height: 300rpx;
    // background-color: pink;
    position: absolute;
    z-index: 10;
    bottom: 10rpx;
    left: 50%; /* 将元素左侧边缘对齐父元素中心 */
    transform-origin: center center; /* 设置旋转中心点 */
    .wheel-img {
      width: 100%;
      height: 100%;
      top: 0rpx;
    }
  }
}

.fuel-gauge-container {
  position: relative;
  z-index: 1;
}

.steering-overlay {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}
</style>
