<template>
  <!--消费明细列表项-->
  <view>
    <view class="comContent">
      <view class="topLine flexRowVertCenter">
        <view class="type">{{ info.type }}</view>
        <view class="value">{{ info.value }} {{ vHName }}</view>
      </view>
      <view class="orderNo"> {{ info.type }}单号：{{ info.order_sn }} </view>

      <view class="dateTime">
        <view class="date">{{
          $u.timeFormat(info.time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
        <!-- <view class="status" :style="{ color: info.pay_status === 2 ? '#0DACE0' : 'red' }">
        {{ payStatus[info.pay_status] }}
      </view> -->
      </view>
      <!-- <view class="dateTime" v-if="info.pay_time && info.pay_status === 2">
      支付时间：{{ $u.timeFormat(info.pay_time * 1000, 'yyyy-mm-dd hh:MM:ss') }}
    </view> -->
    </view>
  </view>
</template>

<script>
export default {
  name: "ConsumeDetailItem",
  props: { info: { type: Object, default: {} } },
  data() {
    return {
      payStatus: {
        1: "未支付",
        2: "已支付",
      },
    };
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.comContent {
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  background-color: #fff;

  .topLine {
    .type {
      font-size: $font-size-xlarge;
      font-weight: bold;
      color: $textBlack;
    }

    .value {
      margin-left: auto;
      font-size: $font-size-middle;
      color: $themeColor;
      font-weight: bold;
    }
  }

  .orderNo {
    margin-top: 30rpx;
    color: $textGray;
    font-size: $font-size-xsmall;
  }

  .dateTime {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    color: $textGray;
    font-size: $font-size-xsmall;
  }
}
</style>