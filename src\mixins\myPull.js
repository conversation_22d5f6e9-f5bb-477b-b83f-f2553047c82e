/**
 * @name 封装下拉
 * @params getList 刷新数据的函数
 * @params listData 存放数据的变量名
 * @params page 页数变量名
 */
export default ({ getList = "getList", listData = "listData", page = "page" } = {}) => {
    return {
        data() {
            return {
                listData: [],
                page: 1,
                pageSize: 10,
                loadingType: -1,
            }
        },
        onPullDownRefresh() {
            this.onTop();
        },
        onReachBottom() {
            console.log("onReachBottom() ");
            this.onBottom();
        },
        methods: {
            refresh() {
                this.page = 1;
                this.listData = [];
                this.loadingType = -1;
                this.getList.call(this, this.page, this.__pulldone);
            },
            __pulldone(data) {
                if (!data || data === " " || data.length == 0) {
                    if (this.listData.length == 0) {
                        this.loadingType = 3;
                    } else {
                        this.loadingType = 2;
                    }
                    return;
                }
                var db = data || [];
                if (this.page == 1) {
                    this.listData = db;
                } else {
                    this.listData = Array.prototype.concat.call(this.listData || [], db);

                }
                this.loadingType = 0;
                // 加载更多，请数据的长度小于pageSize的时候，则显示没有更多数据了
                //console.log("__pulldone() " + (this.loadingType == 0) + " ， listData.length : " + this.listData.length + " ， this.pageSize" + this.pageSize);
                if (this.loadingType == 0 && this.listData.length < this.pageSize) {
                    this.loadingType = 2;
                }
                if (this.listData && this.listData.length <= 0) {
                    this.loadingType = 3;
                }
                uni.stopPullDownRefresh();
                this[page]++;
            },
            onBottom() {
                console.log("onBottom() " + this.loadingType);
                if (this.loadingType !== 0 || this.loadingType == 1) {
                    return;
                }
                this.loadingType = 1;
                this.getList.call(this, this[page], this.__pulldone);
            },
            onTop() {
                console.log("onTop() ");
                if (this.loadingType == 1) {
                    return;
                }
                this.refresh();
            }
        }
    }
}