<template>
  <view class="step-container">
  
    <view class="price-info item-info">
      <view>
        起步价：
        <span class="price-text">{{ order.good_price }}元（含{{ order.game_time || 0 }}分钟）</span>
      </view>
    </view>
    <view class="price-info item-info">
      <view>
        超起步价后：<span class="price-text">{{ order.pre_per_minute }}元/分钟
        </span><span class="grey-text">（没到1分钟按1分钟算）</span>
      </view>
    </view>
    <view class="credit-info item-info">
      <view>
        押金：
        <span class="price-text">{{ order.pay_many }}元</span>
        <span class="deposit-text">{{ depositText }}</span>
      </view>
      <view @click="queryCreditScore" class="green-text">
        查询信誉积分
      </view>
    </view>
    <view class="pre_msg">
      <view class="pre_btn_icon">
        <BaseIcon name="info-circle-fill" size="20" color="#ff852f" />
      </view>
      <view>
        请在上述地图规定的p点内并连接电源归还设备
      </view>

    </view>
    <view class="create-order-button" @click="createOrder">{{ orderButtonText }}</view>
  </view>
</template>

<script>
import BaseIcon from "@/components/base/BaseIcon.vue";
export default {
  components: {
    BaseIcon,
  },
  props: {
    order: {
      type: Object,
      default() {
        return {
          pay_many: 100,
          game_time: 60,
          good_price: 100,
          pre_per_minute: 1

        }
      }
    }
  },
  data() {
    return {
      creditScore: 0 // 初始化信用积分为0
    }
  },
  computed: {
    needDeposit() {
      return this.creditScore < 600
    },
    orderButtonText() {
      return this.creditScore >= 600 ? '创建免密支付订单' : '支付押金'
    },
    depositText() {
      if (this.creditScore === 0) {
        return '（信誉满600免押金）'
      } else if (this.creditScore < 600) {
        return '（低于600分）'
      } else {
        return '（满600分）'
      }
    }
  },
  methods: {
    async fetchCreditScore() {
      // 假设有一个查询积分的接口
      try {
        // const response = await this.op.queryCreditScore(this.order.id)
        this.creditScore = 600
      } catch (error) {
        console.error('查询积分失败', error)
      }
    },
    queryCreditScore() {
      this.fetchCreditScore()
    },
    createOrder() {
      // 创建订单的逻辑
    },
    handleDepositPaid() {
      // 处理押金支付完成的逻辑
    }
  },
  created() {

  }
}
</script>

<style scoped lang="scss">
.step-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  /* 修改为左对齐 */
  padding: 20rpx 30rpx;
}

.pre_msg {
  margin: 0 20rpx;
  display: flex;
  background-color: rgb(95, 95, 95);
  padding: 15rpx 20rpx;
  color: #fff;
  font-size: 28rpx;
  width: 650rpx;
  margin: auto;
  margin-top: 20rpx;
  &_icon {
    margin-right: 10rpx;
  }

}

.item-info {
  line-height: 50rpx;
}

.price-info,
.credit-info {
  margin-bottom: 10rpx;
}

.green-text {
  color: rgb(57, 253, 76);
  border: 1rpx solid rgb(57, 253, 76);
}

.price-text {
  font-size: 30rpx;
  color: red;
  /* 价格后面的文字改成红色 */
}

.grey-text {
  color: #9a9a9a;
  font-size: 28rpx;
}

.credit-info {
  display: flex;
  justify-content: space-between;
  /* 添加两端对齐 */
  width: 100%;
  /* 确保宽度占满 */
}

.create-order-button {
  background: linear-gradient(to right, #007aff, #0056b3);
  /* 添加背景渐变色 */
  color: white;
  padding: 10rpx 20rpx;
  border: none;
  border-radius: 5rpx;
  height: 60rpx;
  /* 调整按钮的高度 */
  line-height: 60rpx;
  width: 650rpx;
  /* 调整按钮的宽度 */
  text-align: center;
  margin: auto;
  margin-top: 50rpx;
}

.deposit-text {
  font-size: 30rpx;
  color: #9a9a9a;
}
</style>