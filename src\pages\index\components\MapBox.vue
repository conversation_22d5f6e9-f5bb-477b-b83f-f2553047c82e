<template>
  <view class="map-box">
    <!-- #ifdef MP-WEIXIN -->
    <view class="tip-layout flexColumnVertCenter" v-if="!vIsLocation">
      <view class="tip">
        您还未授权获取位置信息，请<text class="impt" @click="getLocPermission">
          点击授权
        </text>
      </view>
    </view>

    <map v-else class="map" :latitude="curLatitude" :longitude="curLongitude" :markers="markers" id="map"
      :show-location="true" @markertap="onMarkerTap" @tap="onMapTap" />
    <view class="nearby flexRowAllCenter" @click="onClickNearby" v-if="vIsLocation">
      <view class="textNearby" v-if="!selectProductTab.id">全部机器</view>
      <view class="textNearby" v-else>附近{{ selectProductTab.name }}</view>
      <BaseIcon name="arrow-right" color="#fff" size="16" />
    </view>
    <!--  #endif  -->
    <!--   #ifdef MP-ALIPAY -->
    <map class="map" :latitude="curLatitude" :longitude="curLongitude" :markers="markers" id="map" :show-location="true"
      @markertap="onMarkerTap" @tap="onMapTap" />
    <view class="nearby flexRowAllCenter" @click="onClickNearby" v-if="vIsLocation">
      <view class="textNearby" v-if="!selectProductTab.id">全部机器</view>
      <view class="textNearby" v-else>附近{{ selectProductTab.name }}</view>
      <BaseIcon name="arrow-right" color="#fff" size="16" />
    </view>
    <!-- #endif  -->

    <view v-if="vIsLocation && !isShowPointPlace" class="float-btn" style="      
      bottom: calc(220rpx + constant(safe-area-inset-bottom)); 
 	    bottom: calc(220rpx + env(safe-area-inset-bottom)); 
      ">
      <view v-for="(item, index) in leftFloatingBtnArray" :key="item.name" class="float-box"
        @click="onClickLeftFloatBtn(index)">
        <image class="float-img" :src="item.icon" />
      </view>
    </view>
  </view>

</template>
<script>
import { getNearbyMachineByProductId } from "@/common/http/api";
import { locationMixin } from "@/mixins/locationMixin";
import BaseIcon from '../../../components/base/BaseIcon.vue';
export default {
  components: { BaseIcon },
  props: {
    selectProductTab: { type: Object, default: {} },
  },
  mixins: [locationMixin],
  watch: {
    selectProductTab: {
      handler() {
        this.selectProductTab?.id &&
          this.doGetNearbyMachines(this.selectProductTab?.id);
      },
      deep: true,
      immediate: true

    },
  },
  data() {
    return {
      nearbyMachineList: [], //附近机器列表
      //地图坐标信息
      markers: [],
      //map上下文
      mapCtx: {},
      icMarkerDefault: "/static/theme/ic_marker_default.png",
      leftFloatingBtnArray: [
        {
          name: '搜索位置',
          icon: require("@/static/icon/ic_search_white_bg.png"),
        },
        {
          name: '定位当前位置',
          icon: require("@/static/icon/ic_locate.png"),
        },
      ],
      isShowPointPlace: false,
    };
  },
  methods: {
    //API 获取附近设备
    doGetNearbyMachines(id) {

      let params = {
        lon: this.curLongitude,
        lat: this.curLatitude,
        /* #ifndef MP-TOUTIAO */
        radius: 10,
        /* #endif */
        page: 1,
        limit: 10,
        product_id: id ?? '',
      };

      getNearbyMachineByProductId(params).then((res) => {
        this.nearbyMachineList = res?.data || [];
        this.initMarkers();
      });
    },
    //初始化地图
    initMarkers() {
      let markerArr = this.nearbyMachineList.map((item) => ({
        id: item.id,
        title: item.adr_title,
        latitude: parseFloat(item.lat),
        longitude: parseFloat(item.lon),
        iconPath: this.icMarkerDefault,
        width: 40,
        height: 50,
      }));
      console.log("🚀 ~ markerArr", markerArr)
      this.markers = markerArr;
    },
    //获取位置信息
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.doGetNearbyMachines(this.selectProductTab?.id);
        });
      });
      //#endif
      // #ifdef MP-ALIPAY
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.doGetNearbyMachines(this.selectProductTab?.id);
            });
          }
        },
      });

      //#endif
      // #ifdef H5
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              this.doGetNearbyMachines(this.selectProductTab?.id);
            });
          }
        },
      });
      //#endif
    },
    //点击了地图
    onMapTap() {
      this.isShowPointPlace = false
      this.$emit("onMark", false)
    },
    onMarkerTap(el) {
      this.isShowPointPlace = true
      let pointPlaceInfo = this.nearbyMachineList.find(item => item.id == el.markerId)
      this.$emit("onMark", true, pointPlaceInfo)
    },
    onClickNearby() {
      uni.navigateTo({
        url: `/pagesD/nearbyDevices/index?productId=${this.selectProductTab?.id ?? ''}`,
      });
    },
    //点击左边图标
    onClickLeftFloatBtn(index) {

      if (index == 0) {
        //选择位置
        this.chooseLocation((res) => {
          console.log("选择的位置信息：", res);
        });
      } else if (index == 1) {
        this.getCurrentLocation(() => {
          this.mapCtx.moveToLocation({
            latitude: this.curLatitude,
            longitude: this.curLongitude,
          });
        });
      }
    }
  },
  created() {
    this.mapCtx = uni.createMapContext("map"); // map为地图的id

  },
  mounted() {
    this.getLocPermission()

  }
};
</script>

<style scoped lang="scss">
.map-box {
  position: relative;
  width: 100%;
  height: 100%;

  .tip-layout {
    margin-top: 500rpx;
    text-align: center;

    .tip {
      font-size: $font-size-base;
      color: $textBlack;

      .impt {
        color: $themeColor;
      }
    }
  }

  .map {
    width: 100%;
    height: 100%;
  }

  .nearby {
    position: absolute;
    left: 50%;
    top: 50%;
    background-color: #737371;
    height: 65rpx;
    padding: 0 30rpx;
    border-radius: 40rpx;
    /* #ifdef MP-ALIPAY */
    transform: translate(-50%, -300%);
    /* #endif */
    /* #ifndef MP-ALIPAY */
    transform: translate(-50%, -400%);

    /* #endif */
    &::before {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      width: 4rpx;
      height: 60rpx;
      transform: translateX(-50%);
      background-color: #737371;
    }

    .textNearby {
      font-size: $font-size-small;
      color: white;
    }

    .imgNearby {
      width: 14rpx;
      height: 25rpx;
      margin-left: 25rpx;
    }
  }

  .float-btn {
    position: fixed;
    bottom: 220rpx;
    left: 30rpx;

    .float-box {
      &:nth-child(n+2) {
        margin-top: 20rpx;

      }

      .float-img {
        width: 92rpx;
        height: 92rpx;
      }
    }
  }
}
</style>
