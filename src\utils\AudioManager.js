// utils/AudioManager.js
export default class AudioManager {
  constructor(audioMap) {
    this.audioMap = {};
    this.handlers = {}; // 存储每个音频的循环控制逻辑
    for (const [key, src] of Object.entries(audioMap)) {
      this.audioMap[key] = this.createAudio(src);
    }
  }

  // 创建音频实例
  createAudio(src) {
    const audio = uni.createInnerAudioContext();
    audio.src = src;
    audio.onError((err) => {
      console.error(`音频加载失败: ${src}`, err);
    });
    return audio;
  }

  // 播放音频（新增 loopWithOffset 参数）
  play(type, loopWithOffset = false) {
    const audio = this.audioMap[type];
    if (!audio) return;

    try {
      // 停止并重置音频
      audio.stop();
      audio.seek(0);

      // 清理旧的循环逻辑
      if (this.handlers[type]) {
        audio.offEnded(this.handlers[type]);
      }

      // 定义新的循环逻辑
      const handler = () => {
        if (loopWithOffset) {
          // 从第2秒开始播放
          audio.seek(2);
          audio.play();
        } else {
          // 普通循环
          audio.seek(0);
          audio.play();
        }
      };

      // 绑定循环逻辑
      if (loopWithOffset) {
        audio.onEnded(handler);
        this.handlers[type] = handler;
      }

      // 首次播放
      audio.play();
    } catch (err) {
      console.error(`音频播放失败: ${type}`, err);
    }
  }

  // 停止音频（清理循环逻辑）
  stop(type) {
    const audio = this.audioMap[type];
    if (audio) {
      audio.stop();
      if (this.handlers[type]) {
        audio.offEnded(this.handlers[type]);
        delete this.handlers[type];
      }
    }
  }

  // 销毁所有实例
  destroy() {
    Object.values(this.audioMap).forEach(audio => {
      audio.destroy();
    });
    this.audioMap = null;
    this.handlers = {};
  }
}