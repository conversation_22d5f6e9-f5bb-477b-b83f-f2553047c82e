import Vue from 'vue'
import App from './App'
import uView from "uview-ui";
import store from '@/store';
let vuexStore = require("@/store/$u.mixin.js");

// 全局注册常用组件
import LoginPopup from '@/components/LoginPopup.vue'
import BaseNavbar from '@/components/base/BaseNavbar.vue'
import CommonAd from '@/components/WxAd/CommonAd.vue'

Vue.component('LoginPopup', LoginPopup)
Vue.component('BaseNavbar', BaseNavbar)
Vue.component('CommonAd', CommonAd)

Vue.config.productionTip = false

App.mpType = 'app'
Vue.use(uView);
Vue.mixin(vuexStore);
const app = new Vue({
  store,
  ...App
})
require('./common/http/request')(app)
app.$mount()
