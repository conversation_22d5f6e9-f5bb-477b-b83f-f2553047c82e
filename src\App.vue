<script>
import { calcBottomBarHeight } from '@/utils/safeArea'
/* #ifdef H5 */
import wx from 'weixin-js-sdk'
/* #endif */
export default {
  data() {
    return {
      code: '',
    }
  },
  onLaunch: function (opt) {
    // console.log('App Launch', opt)
      uni.getSystemInfo({
        success: (res) => {
          this.globalData.SystemInfo = res
        },
      })
   

      //计算iphoneX小黑条高度
      this.$u.vuex('vIphoneXBottomHeight', calcBottomBarHeight() || 0)
      /* #ifndef H5*/
      let appid = uni.getAccountInfoSync().miniProgram.appId
      this.$u.vuex('vAppId', appid)
      /* #endif */
      console.log('this.checkForUpdate前')

      /* #ifdef MP-WEIXIN  */
      this.checkForUpdate()
            
      console.log('this.checkForUpdate后')
      wx.preloadAd([
        {
          unitId: this.vAd.gameDetailBannerAd, // 骑行界面banner
          type: 'banner', // banner广告
        },
        {
          unitId: this.vAd.screenAdGameDetail, // 骑行界面插屏
          type: 'custom', // 插屏广告
        },
        {
          unitId: this.vAd.myOutBagBanner, // 骑行界面插屏
          type: 'banner', // 插屏广告
        },
      ])
      /* #endif */
      this.getBaseInfo() //获取网站基本配置
  },

  onShow: function (opt) {
    // console.log('App Show', opt)

    /* #ifdef MP-ALIPAY */
    // 支付宝扫码普通二维码
    if (opt) {
      if (opt.query && opt.query.qrCode) {
        getApp().globalData.aliScanCode = opt.query.qrCode
      }
    }
    /* #endif */
    if (this.globalData.timeout_id) {
      clearTimeout(this.globalData.timeout_id)
      this.globalData.timeout_id = null
    }
  },
  onHide: function () {
    // console.log('App Hide')
    const ble = require('./utils/ble/newBle')
    if (this.vIsBleDevice) {
      // console.log('延迟15S 关闭蓝牙,ble=', ble)
      this.globalData.timeout_id = setTimeout(() => {
        // console.log('时间到 关闭蓝牙,ble=', ble)
        uni.hideLoading()
        ble.default.closeBle()
      }, 15000)
    }
  },
  globalData: {
    wifiInfo: {
      wifi_name: '',
      wifi_psd: '',
    }, //wifi信息
    wifiConnected: false, //wifi是否连接
    connected: false, //蓝牙是否连接
    device_type: '', //设备类型
    device_sn: '', //设备编号
    // 系统参数
    SystemInfo: {}, // 系统信息
    //延迟关闭蓝牙定时器
    timeout_id: null,
    aliScanCode: '', // 支付宝扫码获取的参数
    appid: 'wx7ff056fa6da3ae8a',
    appSecret: '7e955bbff96d75d1e214468045e252b2',
    access_token: '',
    url: 'https://192.168.6.195:80',
    openid: '',
  },
  methods: {
    /* 获取签名 */
    Scan(timestamp, noncestr, signature) {
      let uri = location.href.split('#')[0]
      let data = {
        url: uri,
      }
      let that = this
     
    },
    /* 获取code */
    getCode() {
      let code = ''
      let url = location.search
      if (url.indexOf('?') != -1) {
        var strs = url.substr(1).split('&')
        let ua = window.navigator.userAgent.toLowerCase()
        //判断是不是微信
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          // 微信
          // console.log('微信浏览器')
          for (var i in strs) {
            if (strs[i].indexOf('code') == 0) {
              code = strs[i].split('=')[1]
            }
          }
        }
        //判断是不是支付宝
        if (ua.match(/AlipayClient/i) == 'alipayclient') {
          //支付宝
          // console.log('支付宝浏览器')
          for (var i in strs) {
            if (strs[i].indexOf('auth_code') == 0) {
              code = strs[i].split('=')[1]
            }
          }
        }
      }
      return code
    },
    /* 获取jsapi_ticket */
    ticket(access_token) {
      let that = this
      this.$u.http
        .get(
          `/cgi-bin/ticket/getticket?type=jsapi&access_token=${access_token}`,
        )
        .then((res) => {
        
        })
    },
    /* 获取assToken */
    assToken() {
      let that = this
      this.$u.http
        .get(
          `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx80381f96714c7a85&secret=a8a964d4f8cdae89ff8c6024a2f8d1d6`,
        )
        .then((res) => {
          // console.log('assToken', res)
          // this.access_token = res.access_token
          that.ticket(res.access_token)
        })
    },
    wechatLogin() {
      this.code = this.getCode()
      let localUrl = encodeURIComponent(window.location.href)
      if (!this.code) {
        //第一步获取微信code 1个code只允许使用一次
        // url 是当前链接 encodeURIComponent字符串作为 URI 组件进行编码 来做可识别的回调链接
        let uri = encodeURIComponent(window.location.href)
        // console.log('请求路径', uri, localUrl, this.vAppId)
        if (process.env.NODE_ENV === 'development') {
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.globalData.appid}&redirect_uri=${localUrl}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`
        } else {
          let ua = window.navigator.userAgent.toLowerCase()
          //判断是不是微信
          if (ua.match(/MicroMessenger/i) == 'micromessenger') {
            // 微信
            // console.log('微信浏览器')
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.vAppId}&redirect_uri=${localUrl}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`
          }
          //判断是不是支付宝
          if (ua.match(/AlipayClient/i) == 'alipayclient') {
            //支付宝
            // console.log('支付宝浏览器')
            window.location.href = `https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=${this.vAppId}&scope=auth_user&redirect_uri=${localUrl}`
          }
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          // this.Scan()
          // this.assToken()
        } else {
        }
      }
    },
    getURLParams(wurl) {
      let appid = ''
      let url = wurl
      if (url.indexOf('?') != -1) {
        var strs = url.split('?')
        var strs = strs[1].split('?')
        var strs = strs[0].split('&')
        for (var i in strs) {
          if (strs[i].indexOf('appid') == 0) {
            appid = strs[i].split('=')[1]
          }
        }
      }
      return appid
    },
    getBaseInfo() {
      this.$u.http.post('/waapi/index/baseInfo').then((res) => {
        this.$u.vuex('vBaseInfo', res)
        this.$u.vuex('vHName', res.vip_cash_name)
        /* #ifdef H5 */
        let appid = res.app_id
        this.$u.vuex('vAppId', appid)
        this.$u.vuex('vHName', res.vip_cash_name)
        this.$u.vuex('vUrl', location.href.split('#')[0])
        let localUrl = encodeURIComponent(window.location.href)
        this.wechatLogin()
        /* #endif */
      })
      this.$u.http.post('waapi/user/getUserInfo').then((res) => {
        uni.$u.vuex('vMemberInfo', res)
      })
    },
    //检测版本更新
    checkForUpdate() {
      const updateManager = uni.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        // console.log('【版本更新】', res.hasUpdate)
      })

      updateManager.onUpdateReady(function (res) {
        uni.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success(res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate()
            }
          },
        })
      })

      updateManager.onUpdateFailed(function (res) {
        // console.log('🚀 ~ res', res)
        // 新的版本下载失败
        // console.log('【版本更新】更新失败')
      })
    },
  },
}
</script>

<style lang="scss">
/*每个页面公共css */
@import 'uview-ui/index.scss';
.uni-toast{
    width: 00rpx;
}
</style>
