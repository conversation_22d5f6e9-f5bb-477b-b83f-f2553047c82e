<template>
  <u-upload ref="upload" :accept="accept" :name="name" :max-count="maxCount" :width="width" :height="height || width"
    :max-size="maxSize * 1024 * 1024" :upload-text="uploadText" :file-list="fileListUpload" @afterRead="afterRead"
    @delete="deletePic"></u-upload>
</template>

<script>
export default {
  name: "BaseUpload",
  props: {
    accept: {
      type: String,
      default: "image",
    },
    maxCount: {
      //最大上传数量
      type: Number | String,
      default: 3,
    },
    width: {
      type: Number | String,
      default: 200,
    },
    maxSize: {
      //限制图片大小多少M
      type: Number | String,
      default: 5,
    },
    uploadText: {
      type: String,
      default: "选择图片",
    },
    auto: {
      type: Boolean,
      default: false,
    },
    height: { type: String | Number, default: 0 },
    fileListUpload: { type: String | Array, default: [] },
  },
  computed: {
    action() {
      return this.vBaseURL + "/waapi/machine/imgUpload";

    },
    header() {
      let session3rd = this.vSession3rd;
      return {
        "content-type": "application/x-www-form-urlencoded",
        // #ifdef MP-WEIXIN
        "XX-App-Type": "wx",
        // #endif
        // #ifdef MP-ALIPAY
        "XX-App-Type": "ali",
        // #endif
        /* #ifdef MP-TOUTIAO */
        'XX-App-Type': 'dy',
        /* #endif */
        session3rd: session3rd,
        // 'session3rd': '548601b2c08d28345a6564b69571d5bc'
      };
    },
  },
  data() {
    return {
      name: "Upload",
    };
  },
  methods: {
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
      this.$emit("onUpload", this[`fileList${event.name}`]);
    },
    // 新增图片
    async afterRead(event) {
      console.log("🚀 ~ event", event);
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);

      console.log(
        "🚀 ~       this[`fileList${event.name}`]",
        this[`fileList${event.name}`]
      );
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url);
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: "success",
            message: "",
            url: result,
          })
        );
        fileListLen++;
      }

      console.log(
        "🚀 ~        this[`fileList${event.name}`",
        this[`fileList${event.name}`]
      );
      this.$emit("onUpload", this[`fileList${event.name}`]);
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.action, // 仅为示例，非真实的接口地址
          filePath: url,
          name: "file",
          header: this.header,
          success: (res) => {
            console.log("🚀 ~ res", JSON.parse(res.data)?.data);

            resolve(JSON.parse(res.data)?.data);
          },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
