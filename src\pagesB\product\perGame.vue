<template>
  <view>
    <view class="top-Map">
      <view class="map-box">
        <view class="search-box">
          <!-- <BaseSearch placeholder="请输入地址" @onClickIcon="onClickIcon" @search="searchAddress" :isShowSerch="false" /> -->
          <!-- <input type="text" v-model="searchQuery" placeholder="请输入地址" />
        <button @tap="searchAddress">搜索</button> -->
        </view>
        <map
          id="map"
          :longitude="longitude"
          :latitude="latitude"
          scale="18"
          :markers="markers"
          :polyline="polyline"
          :polygons="polygons"
          show-location
          style="width: 100%; height: calc(100vh - 800rpx)"
        >
        </map>
        <!-- 底部主要内容 -->
        <view class="pre_main">
          <view class="pre_order"> 设备编号：{{ orderInfo.device_sn }} </view>
          <view class="pre_order_box" v-if="gameStatus == 3 || gameStatus == 4">
            <view class="pre_top">
              <view>
                <span class="pre_top_blod"
                  >驾驶时长{{ orderInfo.length_time || 0 }}分钟</span
                >
              </view>
              <view>
                <span class="pre_top_right"
                  >订单编号：{{ orderInfo.order_sn || "" }}</span
                >
              </view>
            </view>
            <view class="pre_order_chunk">
              <view class="pre_order_main">
                <view class="pre_order_top">实付金额</view>
                <view class="pre_order_pre"
                  >{{ orderInfo.order_amount || 0 }}元</view
                >
                <view class="pre_order_flex">
                  <view>起步价</view>
                  <view>{{ orderInfo.pre_min_price || 0 }}元</view>
                </view>
                <view class="pre_order_flex">
                  <view>超起步价</view>
                  <view>{{ orderInfo.extra_amount || 0 }}元</view>
                </view>
                <view class="pre_order_flex pre_order_btn">
                  <view></view>
                  <view>合计{{ orderInfo.order_amount || 0 }}元</view>
                </view>
              </view>
            </view>
            <view class="pre_order_btm">
              <view> 预付金额 </view>
              <view>
                <span>{{ orderInfo.total_amount }}元</span>
                <span class="pre_order_btm_red"
                  >(已退{{ orderInfo.back_amount || 0 }}元)</span
                >
              </view>
            </view>
            <view class="pre_buy_right">
              <view></view>
              <view class="pre_buy_flex">
                <view class="pre_buy_btn" @click="onClickBtnScan"
                  >再次购买</view
                >
              </view>
            </view>
            <view class="pre_list">
              <!-- <view @click="onClickServerPhone()">客服电话></view> -->
              <view class="game_phone" v-if="phone" @click="onClickServerPhone"
                >售后服务热线：<span class="game_phone_line">{{
                  phone
                }}</span></view
              >

              <!-- <view @click="errLoction()">故障上报></view> -->
              <!-- <view>价格说明></view> -->
            </view>
          </view>
          <view class="pre_chunk" v-else>
            <view class="pre_msg">
              <view class="pre_btn_icon">
                <BaseIcon name="info-circle-fill" size="20" color="#ff852f" />
              </view>
              <view> 请在上述地图规定的p点内并连接电源归还设备 </view>
            </view>

            <!-- <view class="pre_msg_warn">
                            <view>
                                <span>
                                    停车点外还车将收取调度费：

                                </span>
                                <span class="pre_msg_red">
                                    {{ '1545' }}元
                                </span>
                            </view>
                            <view class="pre_msg_red">
                                注： 车辆集中摆放点不一定是当前车辆的可停车点
                            </view>
                        </view> -->

            <view class="pre_btn">
              <view class="pre_top">
                <!-- <view>
                                    <span class="pre_top_blod">剩余电量</span>
                                    <span class="pre_top_pre">{{ '100' }}%</span>
                                    <span class="pre_top_small">(预计行驶{{ '30' }}km)</span>
                                </view> -->
                <view>
                  <span class="pre_top_right"
                    >订单编号：{{ orderInfo.order_sn || "" }}</span
                  >
                </view>
              </view>
              <view class="text_center"> 剩余时间 </view>
              <view class="Timing">
                <u-count-down
                  ref="countDown"
                  :time="remainTime * 60 * 1000"
                  format="HH:mm:ss"
                  :autoStart="true"
                  :millisecond="false"
                  @change="onChange"
                  @finish="finish"
                >
                  <view class="Timing_time">
                    <view class="Timing_time__custom">
                      <text class="Timing_time__custom__item">{{
                        timeData.hours || "00"
                      }}</text>
                    </view>
                    <view class="Timing_time__doc">:</view>
                    <view class="Timing_time__custom">
                      <text class="Timing_time__custom__item">{{
                        timeData.minutes || "00"
                      }}</text>
                    </view>
                    <view class="Timing_time__doc">:</view>
                    <view class="Timing_time__custom">
                      <text class="Timing_time__custom__item">{{
                        timeData.seconds || "00"
                      }}</text>
                    </view>
                  </view>
                </u-count-down>
                <!-- <view class="hour">{{ "00" }}</view>
              <view class="spot">:</view>
              <view class="min">{{ "01" }}</view>
              <view class="spot">:</view>
              <view class="second">{{ "30" }}</view> -->
              </view>
              <view class="pre_btn_main">
                <view class="pre_btn_main_item">
                  <view class="blod"> 预付总金额 </view>
                  <view>
                    <span class="big">
                      {{
                        orderInfo.order_amount
                          ? orderInfo.order_amount.split(".")[0]
                          : "0"
                      }}.
                    </span>
                    <span class="small">
                      {{
                        orderInfo.order_amount
                          ? orderInfo.order_amount.split(".")[1]
                          : "00"
                      }}元</span
                    >
                  </view>
                </view>
                <view class="pre_btn_main_item">
                  <view class="blod"> 总时长 </view>
                  <view>
                    <span class="big">
                      {{ orderInfo.length_time || 0 }}
                    </span>
                    <span class="small"> 分钟 </span>
                  </view>
                </view>
                <view class="green" @click="gotoInfo()"> 价格说明> </view>
              </view>
            </view>
            <view class="pre_buy_left">
              <view class="pre_buy_left_flex">
                <view
                  class="pre_buy_left_btn"
                  @click="errLoction"
                  v-if="isError"
                  >故障上报还车</view
                >
              </view>
            </view>
            <view class="pre_buy">
              <view class="pre_buy_flex">
                <view class="pre_buy_btn" @click="showLoction">立即还车</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <Floating y="185"></Floating>
    <BasePopup :show.sync="isShowLoction" mode="bottom" :safeArea="false">
      <view class="popup_chunk" v-if="isP">
        <view class="popup_chunk_top"> 你已在P点内 </view>
        <view class="popup_chunk_title"> 是否确认归还设备？ </view>
        <view class="popup_chunk_img">
          <image class="image" src="../static/pd.png" />
        </view>
        <view class="popup_chunk_title"> 是否接入插座？ </view>
        <view class="popup_f_chunk_img" style="height: 200rpx">
          <image class="image" src="../static/cd.jpg" />
        </view>
        <view class="popup_chunk_btn">
          <view class="popup_chunk_btn_item exit" @click="exit()"> 取消 </view>
          <view class="popup_chunk_btn_item succes" @click="enPer()">
            确认还车
          </view>
        </view>
      </view>
      <view class="popup_f_chunk" v-else>
        <view class="popup_f_chunk_top">
          你未在P点内，结束租赁需缴纳调度费
        </view>
        <view class="popup_f_chunk_title">
          <view class="popup_f_chunk_title_item">
            <view class="popup_f_chunk_title_item_green">起步价</view>
            <view class="popup_f_chunk_title_item_pre">{{
              orderInfo.pre_min_price || 0
            }}</view>
          </view>
          <view class="popup_f_chunk_title_item">
            <view class="popup_f_chunk_title_item_green">超起步价</view>
            <view class="popup_f_chunk_title_item_pre">{{
              orderInfo.pre_per_minute || 0
            }}</view>
          </view>
        </view>
        <view class="popup_f_chunk_img">
          <image class="image" src="../static/no_p.png" />
        </view>

        <view class="popup_f_chunk_btn">
          <view class="popup_f_chunk_btn_item succes" @click="gotoLocation()">
            前往附近停车点
          </view>
        </view>
      </view>
    </BasePopup>
    <BasePopup
      :show.sync="isShowProgres"
      mode="bottom"
      :safeArea="false"
      :closeOnClickOverlay="false"
    >
      <view class="popup_pr_chunk">
        <view class="popup_pr_chunk_title">还车中...</view>
        <ProgressCircle :progress="progressCount"></ProgressCircle>
      </view>
    </BasePopup>
    <u-modal
      :show="newShow"
      @confirm="confirm"
      showCancelButton
      @cancel="cancel"
    >
      <view>
        <view class="model_title">请选择故障原因</view>
        <BaseRadio
          :size="20"
          :radioIndex.sync="radioIndex"
          width="100"
          :list="reasonList"
        />
        <u-input
          v-if="radioIndex == 2"
          :border="bottom"
          v-model="reason"
          placeholder="请输入故障原因"
        />
      </view>
    </u-modal>
  </view>
</template>
<script>
import BasePopup from "../../components/base/BasePopup.vue"
import { locationMixin } from "@/mixins/locationMixin"
import BaseIcon from "@/components/base/BaseIcon.vue"
import BaseRadio from "../components/BaseRadio.vue"
// import BaseModal from '@/components/base/BaseModal.vue'
import ProgressCircle from "../components/ProgressCircle.vue"
import { globalCodes } from "@/global/globalCodes"
import Floating from "./components/Floating.vue"
import utils from "@/utils/utils"
import {
  endPreOrder,
  getUserOrderByNo,
  searchBoardRemoteInfo,
  getBoardRemoteInfo,
  getDeviceAllInfo,
} from "@/common/http/api"
export default {
  name: "index",
  components: {
    BaseIcon,
    BasePopup,
    BaseRadio,
    Floating,
    ProgressCircle,
    // BaseModal
  },
  data() {
    return {
      polygons: [],
      polygonLabels: [], // 用于存储多边形中心的文字标注
      currentPolygonIndex: -1, // 当前正在创建的多边形索引
      isCreatingPolygon: false, // 用于判断是否在创建多边形
      isCreating: false, //是否可以创建多边形
      nearbyMachineList: [],
      longitude: 0,
      latitude: 0,
      searchQuery: "", // 搜索框的值
      polygonMarkers: [], // 用于存储多边形的标记点
      hotel_id: "",
      isP: false, //是否p点附近
      mapCtx: null,
      orderInfo: {
        pay_money: "12",
      },
      isShowLoction: false,
      order_sn: "",
      orderInfo: {},
      remainTime: 0, //剩余时间
      timeData: {}, //倒计时时间
      order_time: "", // 订单时间
      gameStatus: 0, //充电状态 3：结束 4退款
      markers: [
        {
          id: 0,
          latitude: "",
          longitude: "",
          iconPath: "../static/p.png",
          width: 40,
          height: 50,
        },
      ],
      reasonList: [
        {
          title: "已在p点显示不在",
          name: "0",
          disabled: false,
          selectIndex: 0,
        },
        {
          title: "已链接电源还车失败",
          name: "1",
          disabled: false,
          selectIndex: 1,
        },
        {
          title: "其他原因",
          name: "2",
          disabled: false,
          selectIndex: 2,
        },
      ],
      radioIndex: 0,
      reason: "",
      newShow: false,
      isError: false,
      phone: "",
      isShowProgres: false,
      progressCount: 0,
    }
  },
  mixins: [locationMixin],
  methods: {
    onClickBtnScan() {
      /* #ifndef H5 */
      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          // console.log('条码类型：' + res.scanType)
          // console.log('条码内容：' + res.result)
          // 条码内容：https://test.51xhkj.com/mini/index?vscode=253364733236916
          let result = decodeURIComponent(res.result)
          let dataVsCode = utils.getUrlParams(result, "vscode")
          let dataMid = utils.getUrlDynamicData(result, "mid")
          let dataDeviceSn = utils.getUrlDynamicData(result, "device_sn")
          let freeType =
            utils.getUrlParams(result, "ft") ||
            utils.getUrlDynamicData(result, "ft") ||
            ""
          if (dataVsCode) {
            // console.log('扫码进入获取设备虚拟码vscode', dataVsCode)
            uni.navigateTo({
              url: `/pagesB/product/index?vscode=${dataVsCode}&ft=${freeType}`,
            })
          } else if (dataMid) {
            // console.log('扫码进入获取设备mid', dataMid)
            uni.navigateTo({
              url: `/pagesB/product/index?mid=${dataMid}&ft=${freeType}`,
            })
          } else if (dataDeviceSn) {
            // console.log('扫码进入获取设备device', dataDeviceSn)
            uni.navigateTo({
              url: `/pagesB/product/index?device_sn=${dataDeviceSn}&ft=${freeType}`,
            })
          } else {
            uni.showToast({
              title: "请扫描正确二维码",
              icon: "error",
            })
          }
        },
        fail: (err) => {
          console.log("🚀 ~ err 调用扫码失败", err)
        },
      })
      /* #endif */
      /* #ifdef H5 */
      if (typeof AlipayJSBridge != "undefined") {
        AlipayJSBridge.call(
          "scan",
          {
            type: "qr", // 扫描类型  qr 二维码  / bar 条形码
            // actionType: "scanAndRoute",// 如果只是扫码,拿到码中的内容，这项不用设置都可以
          },
          (res) => {
            // alert(JSON.stringify(res));
            if (res.error == 10) {
              // 错误码为10：用户取消操作
              Toast("取消操作")
            } else if (res.error == 11) {
              // 错误码为11：扫码失败
              Toast("网络异常，请重试")
            } else {
              // res.codeContent为扫码返回的结果
              window.location.replace(res.codeContent)
            }
          }
        )
      } else if (typeof WeixinJSBridge != "undefined") {
        let uri = location.href.split("#")[0]
        // let uri = this.vUrl
        let data = {
          url: uri,
        }
        let that = this

        getJsSign(data)
          .then((res) => {
            wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.appId, // 必填，公众号的唯一标识
              timestamp: res.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.nonceStr, // 必填，生成签名的随机串
              signature: res.signature, // 必填，签名
              jsApiList: ["scanQRCode"], // 必填，需要使用的JS接口列表, 这里只需要调用扫一扫
            })
            wx.ready(function () {
              wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                success: function (res) {
                  // 扫码成功，跳转到二维码指定页面（res.resultStr为扫码返回的结果）
                  // window.location.replace(res.resultStr);
                  setTimeout(() => {
                    window.location.replace(res.resultStr)
                  }, 20)
                },
              })
            })
          })
          .catch((err) => {
            console.log("错误结果", err)
          })
      } else {
        uni.navigateTo({
          url: `/pagesC/Scan/Scan?from=batch&hotel_id=${this.hotel_id}`,
        })
      }

      /* #endif */
    },
    onClickServerPhone() {
      console.log("电话号码", this.vPhone)
      uni.makePhoneCall({
        phoneNumber: this.vPhone,
        // 成功回调
        success: (res) => {},
        // 失败回调
        fail: (res) => {},
      })
    },
    //故障还车
    async confirm() {
      // console.log('故障原因', this.radioIndex);
      let reason = ""
      try {
        // 参数校验逻辑
        if (this.radioIndex == 2) {
          if (!this.reason) {
            uni.showToast({
              title: "请输入故障原因",
              icon: "none",
            })
            return // 直接返回，不继续执行
          } else {
            reason = this.reason
            this.newShow = false
          }
        } else {
          reason = this.reasonList[this.radioIndex].title
          this.newShow = false
        }

        // 显示加载状态
        uni.showLoading({
          title: "正在还车...",
          mask: true,
        })

        // 构造请求参数并发送请求
        const params = {
          order_sn: this.order_sn,
          end_order_error_str: reason,
        }
        await endPreOrder(params) // 假设此函数可能抛出错误

        // 请求成功后的操作
        uni.showToast({
          title: "还车成功",
          icon: "none",
        })
        this.doGetUserOrderByNo(this.order_sn) // 确保 this 指向正确
      } catch (error) {
        // 统一错误处理
        console.error("还车失败:", error)
        uni.showToast({
          title: `还车失败: ${error.message || "未知错误"}`,
          icon: "none",
        })
      } finally {
        uni.hideLoading() // 无论成功失败都隐藏加载状态
      }
    },
    cancel() {
      this.newShow = false
    },
    exit() {
      this.isShowLoction = false
    },
    showLoction() {
      //获取当前位置
      this.isShowLoction = true
      this.getLocPermission()
    },
    errLoction() {
      // console.log("故障还车");
      this.newShow = true
    },
    //前往附近停车点
    gotoLocation() {
      this.isError = true
      this.isShowLoction = false
    },
    // 联系客服
    onClickServerPhone() {
      // console.log("电话号码", this.vServicePhone);
      uni.makePhoneCall({
        phoneNumber: this.vServicePhone,
        // 成功回调
        success: (res) => {},
        // 失败回调
        fail: (res) => {},
      })
    },
    // 价格说明

    gotoInfo() {
      uni.navigateTo({
        url:
          "/pagesB/product/preInfo?orderInfo=" +
          encodeURIComponent(JSON.stringify(this.orderInfo)),
      })
    },
    //订单状态
    getGameStatus() {
      const { gameStatus, orderInfo } = this
      const isCarDevice = orderInfo.is_car_device

      const statusMapping = isCarDevice
        ? globalCodes.carOrderStatus.nameArr
        : orderInfo.is_cannon_device
        ? globalCodes.cannonOrderStatus.nameArr
        : globalCodes.gameOrderStatus.nameArr

      switch (gameStatus) {
        case globalCodes.gameOrderStatus.STATUS_GAME_TO:
        case globalCodes.gameOrderStatus.STATUS_GAME_ING:
        case globalCodes.gameOrderStatus.STATUS_GAME_END:
        case globalCodes.gameOrderStatus.STATUS_GAME_ERR:
          return statusMapping[gameStatus]
        default:
          return "订单状态异常"
      }
    },
    onChange(time) {
      // console.log("🚀 ~ time", time);
      time.hours = (time.hours.toString()?.length == 1 ? "0" : 0) + time.hours
      time.minutes =
        (time.minutes.toString()?.length == 1 ? "0" : 0) + time.minutes
      time.seconds =
        (time.seconds.toString()?.length == 1 ? "0" : 0) + time.seconds
      this.timeData = time
    },
    finish() {
      console.log("🚀 ~ item  倒计时结束")
      this.gameStatus = globalCodes.gameOrderStatus.STATUS_GAME_END
      // console.log("🚀 ~ item  倒计时结束 状态", this.gameStatus);
      uni.setNavigationBarTitle({
        title: "骑行结束",
      })
      this.doGetUserOrderByNo(this.order_sn) // 确保 this 指向正确
    },
    //分钟转换成 00:00:00格式
    toHourMinute(minutes) {
      if (minutes) {
        let h = Math.floor(minutes / 60)
        if (h.toString().length < 2) h = "0" + h
        let m = minutes % 60
        if (m.toString().length < 2) m = "0" + m
        return h + ":" + m + ":" + "00"
      } else {
        return "00:00:00"
      }
    },
    //订单信息查询
    doGetUserOrderByNo(order_sn) {
      let params = {
        order_sn,
      }
      getUserOrderByNo(params).then((res) => {
        // console.log("--订单返回--", res);
        this.orderInfo = res.data
        // console.log("--订单信息--", this.orderInfo);
        res.data?.device_sn &&
          (getApp().globalData.device_sn = this.orderInfo?.device_sn)
        res.data?.machine_type &&
          (getApp().globalData.device_type = this.orderInfo?.machine_type)
        this.user_recharge = this.orderInfo?.user_recharge
        this.order_time = utils.parseTime(this.orderInfo?.add_time)
        // console.log("-----订单时间：", this.order_time);

        this.oldTime = this.orderInfo.start_time * 1000
        let endTime = this.orderInfo.end_time * 1000
        this.gameStatus = this.orderInfo.game_status
        // this.isShowLoction = res.data.game_status === 2;

        // if (this.gameStatus > globalCodes.gameOrderStatus.STATUS_CHARGE_ING) {
        //   this.oldGameStatus = this.gameStatus;
        // }

        let nowTime = new Date().getTime() //现在时间
        //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间
        this.remainTime = ((endTime - nowTime) / 1000 / 60).toFixed(2)
        // let start_count_time = this.remainTime * 60 * 1000;

        // console.log(
        //   "--时间--",
        //   this.orderInfo.start_time,
        //   this.orderInfo.end_time,
        //   endTime,
        //   nowTime,
        //   this.remainTime,
        //   start_count_time
        // );
        this.getDevice()
      })
    },
    //获取位置信息
    async enPer() {
      let params = {
        device_sn: this.orderInfo.device_sn,
      }
      let data = {
        order_sn: this.order_sn,
      }

      const maxOuterAttempts = 3 // 外层最大尝试次数
      const maxInnerAttempts = 3 // 内层最大尝试次数
      let that = this

      that.isShowLoction = false
      that.isShowProgres = true
      that.progressCount = 0
      const totalSteps =
        maxOuterAttempts * 3 + maxOuterAttempts * (maxInnerAttempts - 1) * 3 // 总等待秒数
      const stepProgress = 100 / totalSteps // 每一秒对应的进度值

      // 外层循环控制 search 和整体重试
      for (
        let outerAttempt = 0;
        outerAttempt < maxOuterAttempts;
        outerAttempt++
      ) {
        try {
          // 1. 执行 searchBoardRemoteInfo
          if (outerAttempt != 0) {
            await searchBoardRemoteInfo(params)
          }
          // 2. 等待 3 秒
          await new Promise((resolve) =>
            setTimeout(async () => {
              that.progressCount = Math.round(
                that.progressCount + 3 * stepProgress
              )
              resolve()
            }, 3000)
          )

          // 内层循环执行三次 getBoardRemoteInfo
          let isChargeSuccess = false
          for (
            let innerAttempt = 0;
            innerAttempt < maxInnerAttempts;
            innerAttempt++
          ) {
            try {
              // 3. 执行 getBoardRemoteInfo
              let getRes = await getBoardRemoteInfo(params)
              // 4. 判断是否成功
              if (getRes.is_plug_power === 1) {
                isChargeSuccess = true
                break // 成功则跳出内层循环
              }
            } catch (error) {
              console.error("检查充电状态失败", error)
            }

            // 5. 每次检查间隔 3 秒（最后一次无需等待）
            if (innerAttempt < maxInnerAttempts - 1) {
              await new Promise((resolve) =>
                setTimeout(() => {
                  that.progressCount = Math.round(
                    that.progressCount + 3 * stepProgress
                  )
                  resolve()
                }, 3000)
              )
            }
          }

          // 内层循环成功则结束整个流程
          if (isChargeSuccess) {
            // 执行结束订单操作
            await endPreOrder(data)
            that.isShowProgres = false
            uni.hideLoading()
            uni.showToast({
              title: "还车成功",
              icon: "none",
            })
            that.doGetUserOrderByNo(that.order_sn)
            that.progressCount = 100 // 确保进度为 100%
            return
          }
        } catch (error) {
          console.error("查询设备信息失败", error)
        }
      }

      // 全部循环失败后提示用户

      that.isShowProgres = false
      uni.showModal({
        title: "温馨提示：",
        content: "检查是否已经将车辆接通电源?",
      })

      that.isError = true
      that.progressCount = 100 // 确保进度为 100%
    },

    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          this.longitude = this.curLongitude
          this.latitude = this.curLatitude
          this.isP = this.areAddressesWithin100Meters(
            this.curLatitude * 1,
            this.curLongitude * 1,
            this.markers[0].latitude * 1,
            this.markers[0].longitude * 1
          )
          // console.log('this.isp', this.isP)
        })
      })
      //#endif
      // #ifdef MP-ALIPAY
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {})
          }
        },
      })

      //#endif
      // #ifdef H5
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {})
          }
        },
      })
      //#endif
    },
    //获取电子围栏信息
    async getDevice() {
      let params = {
        device_sn: this.orderInfo.device_sn,
      }
      let res = await getDeviceAllInfo(params)
      this.phone = res?.hotel_info?.service_phone || this.vServicePhone
      if (
        res &&
        res.hotel_info &&
        res.hotel_info.p_info &&
        res.hotel_info.p_info.length > 0
      ) {
        this.markers[0].latitude = res.hotel_info.p_info[0].latitude
        this.markers[0].longitude = res.hotel_info.p_info[0].longitude
        let points = res?.hotel_info?.geo_info.map((item) => {
          return {
            longitude: item.longitude * 1,
            latitude: item.latitude * 1,
          }
        })
        this.polygons.push({
          points: [...points],
          fillColor: "#1791fc66",
          strokeColor: "#FFF",
          strokeWidth: 2,
          zIndex: 3,
        })
      }

      this.getLocPermission()
      this.mapCtx = uni.createMapContext("map") // map为地图的id
      // this.markers=[...this.markers[0],...marske]
    },
    //判断两点间距离
    // 将度转换为弧度
    degreesToRadians(degrees) {
      return degrees * (Math.PI / 180)
    },

    // 使用 Haversine 公式计算两个地理坐标之间的距离（米）
    haversineDistance(lat1, lon1, lat2, lon2) {
      const R = 6371e3 // 地球半径，单位为米
      const φ1 = this.degreesToRadians(lat1)
      const φ2 = this.degreesToRadians(lat2)
      const Δφ = this.degreesToRadians(lat2 - lat1)
      const Δλ = this.degreesToRadians(lon2 - lon1)

      const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

      return R * c // 距离，单位为米
    },

    // 计算两个地址的距离并返回布尔值
    areAddressesWithin100Meters(lat1, lon1, lat2, lon2) {
      const distance = this.haversineDistance(lat1, lon1, lat2, lon2)
      // console.log('相差多远', distance < 100)
      return distance < 100
    },
  },
  onLoad(opt) {
    this.order_sn = opt.order_sn
    if (opt.order_sn) {
      //执行查询订单
      this.doGetUserOrderByNo(opt.order_sn)
    }
  },
}
</script>
<style lang="scss">
page {
  background-color: rgb(238, 236, 236);
}
</style>
<style scoped lang="scss">
.blod {
  font-weight: bold;
}

::v-deep .contact {
  padding: 0 !important;
}

.game_phone {
  width: 100%;
  margin-top: 10rpx;
  font-size: 40rpx;
  color: red;
  text-align: center;
  font-weight: bold;
  &_line {
    text-decoration: underline;
  }
}

.model_title {
  text-align: center;
}

.text_center {
  text-align: center;
  font-size: 30rpx;
  margin: 10rpx auto;
  color: #9a9a9a;
}

.Timing {
  display: flex;
  width: 330rpx;
  height: 80rpx;
  background-color: rgb(227, 241, 255);
  box-sizing: border-box;
  padding: 0 32rpx;
  justify-content: space-between;
  align-items: center;
  margin: auto;
  margin-bottom: 20rpx;
  border-radius: 10px;
  color: rgb(68, 161, 251);

  ::v-deep .u-count-down {
    width: 100%;
    height: 100%;
  }

  // > view {
  //   flex: 20%;
  //   flex-shrink: 0;
  //   font-size: 40rpx;
  // }
  &_time {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    :nth-child(n) {
      flex: 20%;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &__custom {
      width: 25%;
      height: 100%;
    }
  }
}

.popup_chunk {
  // border-top-left-radius: 15rpx;
  // height: 500rpx;
  padding: 30rpx 20rpx 50rpx 20rpx;

  &_top {
    text-align: center;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
  }

  &_img {
    height: 200rpx;
    background-color: #666;
    margin: 20rpx 0;

    .image {
      width: 100%;
      height: 100%;
    }
  }

  &_btn {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;

    &_item {
      width: 49%;
      padding: 25rpx 0;
      // border: 1rpx solid #d8d8d8;
      border-radius: 15rpx;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 35rpx;
      font-weight: bold;
    }

    .succes {
      background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
      color: #fff;
      border-radius: 15rpx;
    }

    .exit {
      background-color: #dad8d8;
    }
  }

  &_title {
    font-size: 28rpx;
    margin-bottom: 30rpx;
  }
}

.popup_f_chunk {
  // border-top-left-radius: 15rpx;
  // height: 500rpx;
  padding: 30rpx 20rpx 50rpx 20rpx;

  &_top {
    text-align: center;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    color: #ff852f;
    padding: 0 120rpx;
  }

  &_img {
    height: 400rpx;
    background-color: #666;
    margin: 20rpx 0;

    .image {
      width: 100%;
      height: 100%;
    }
  }

  &_btn {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;

    &_item {
      width: 49%;
      padding: 25rpx 0;
      // border: 1rpx solid #d8d8d8;
      border-radius: 15rpx;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 35rpx;
      font-weight: bold;
    }

    .succes {
      width: 700rpx;
      background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
      color: #fff;
      border-radius: 15rpx;
    }

    .exit {
      background-color: #dad8d8;
    }
  }

  &_title {
    font-size: 28rpx;
    margin-bottom: 30rpx;
    background-color: #e0e0e0;
    display: flex;
    justify-content: space-around;
    padding: 20rpx;

    &_item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      height: 100rpx;

      &_pre {
        color: #e60404;
      }
    }
  }
}

.popup_pr_chunk {
  padding: 30rpx 20rpx 200rpx 20rpx;
  &_title {
    text-align: center;
    margin: 20rpx 0 50rpx 0;
    font-size: 40rpx;
  }
}

::v-deep .u-transition {
  border-top-left-radius: 50rpx;
  border-top-right-radius: 50rpx;
  overflow: hidden;
}

.green {
  font-size: 25rpx;
  color: #666;
}

.big {
  font-weight: bold;
  font-size: 50rpx;
}

.small {
  font-weight: bold;
  font-size: 25rpx;
}

.pre_order {
  font-size: 25rpx;
  margin-bottom: 10rpx;

  &_chunk {
    background-color: #e6e7e8;
    // height: 300rpx;
    padding: 20rpx 30rpx 0 30rpx;
  }

  &_flex {
    display: flex;
    justify-content: space-between;
    margin: 20rpx 0;
    font-size: 28rpx;
  }

  &_top {
    text-align: center;
    font-weight: bold;
  }

  &_pre {
    text-align: center;
    font-size: 70rpx;
    margin: 20rpx 0;
    font-weight: bold;
  }

  &_btn {
    padding: 15rpx 0;
    margin-top: 20rpx;
    border-top: 1rpx solid #d8d8d8;
  }

  &_btm {
    background-color: #e6e7e8;
    display: flex;
    justify-content: space-between;
    padding: 15rpx 20rpx;
    font-size: 25rpx;

    &_red {
      color: #ff0000;
      margin-left: 20rpx;
    }
  }
}

.pre_list {
  display: flex;
  justify-content: space-between;
  // margin-top: 10rpx;
  font-size: 28rpx;
  // margin-bottom: 200rpx;
}

.pre_buy_right {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 28rpx;
  // margin-bottom: 200rpx;
}

.pre_main {
  padding: 20rpx;

  .pre_chunk {
    position: relative;
    background-color: #fff;
    padding: 20rpx;
    // height: 650rpx;

    .pre_msg_warn {
      background-color: #ffdcd9;
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      margin: 20rpx auto;
    }
  }

  .pre_order_box {
    background-color: #fff;
    padding: 10rpx 20rpx 50rpx 20rpx;
  }
}

.pre_top {
  // background-color: rgb(238, 236, 236);

  padding: 10rpx 0;
  display: flex;
  border-bottom: 1rpx solid rgba(216, 216, 216, 0.667);
  justify-content: space-between;

  &_blod {
    font-weight: bold;
  }

  &_small {
    margin-left: 10rpx;
    font-size: 25rpx;
    color: #666;
  }

  &_pre {
    color: #ffbe09;
  }

  &_right {
    font-size: 25rpx;
    color: #666;
  }
}

.pre_btn_main {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;

  &_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 120rpx;
    justify-content: space-between;
  }
}

.pre_btn {
  position: relative;
  // padding: 0 20rpx 20rpx 20rpx;
  padding: 10rpx 20rpx;
  background-color: #e7e7e8;
  margin-bottom: 120rpx;

  // color: #ff852f;
  &_item {
    display: flex;
    margin: 10rpx 0;
    align-items: center;

    &_pre {
      color: #ff852f;
    }

    &_line {
      width: 25rpx;
      height: 25rpx;
      margin-right: 10rpx;
      display: flex;
      align-items: center;

      .image {
        width: 100%;
        height: 100%;
      }
    }

    &_green {
      font-size: 25rpx;
      color: #666;
    }

    &_top {
      vertical-align: top;
    }

    &_small {
      font-size: 25rpx;
      color: #666;
    }

    &_pre {
      color: #d73b03;
      font-size: 25rpx;
    }

    &_left {
      width: 160rpx;
      margin-right: 20rpx;
      font-size: 30rpx;
      font-weight: bold;
    }
  }

  &_icon {
    margin-right: 10rpx;
  }
}

.pre_msg {
  // margin: 0 20rpx;
  display: flex;
  background-color: rgb(95, 95, 95);
  padding: 15rpx 20rpx;
  color: #fff;
  font-size: 28rpx;
  margin-bottom: 30rpx;

  &_red {
    color: #cb0505;
    font-weight: bold;
  }

  &_icon {
    margin-right: 10rpx;
  }
}

.pre {
  // width: 100%;
  position: relative;
}

.pre_buy {
  // width: 100%;
  // box-sizing: border-box;

  &_btn {
    width: 250rpx;
    padding: 20rpx;
    background-color: #ff852f;
    color: #fff;
    text-align: center;
  }

  &_add {
    border: 1rpx solid #ff852f;
  }

  position: absolute;
  bottom: 30rpx;
  right: 20rpx;
}

.pre_buy_left {
  // width: 100%;
  // box-sizing: border-box;

  &_btn {
    width: 250rpx;
    padding: 20rpx;
    background-color: #9a9a9a;
    color: #fff;
    text-align: center;
  }

  &_add {
    border: 1rpx solid #9a9a9a;
  }

  position: absolute;
  bottom: 30rpx;
  left: 20rpx;
}
</style>
