<template>
  <!--点击同意服务协议组件-->
  <view class="comContent">
    <view class="agreeProtocol flexRowVertCenter">
      <checkbox class="checkbox" :checked="bAgreeProtocol" @click="switchAgree"></checkbox>
      <view class="protocol flexRowVertCenter">
        <view @click="switchAgree">点击购买即代表同意</view>
        <view class="spec" @click="gotoServiceProtocol">《用户服务协议》</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ServiceProtocolAgree",
  data() {
    return {
      bAgreeProtocol: true,
    };
  },

  methods: {
    switchAgree() {
      if (!this.bAgreeProtocol) {
        this.bAgreeProtocol = "true";
      } else {
        this.bAgreeProtocol = "";
      }
    },

    gotoServiceProtocol() {
      uni.navigateTo({
        url: `/pagesC/userServiceNotice/index`,
      });
    },
  },
  watch: {
    bAgreeProtocol(newVal) {
      this.$emit("agreeChange", newVal);
    },
  },
};
</script>

<style scoped lang="scss">
.comContent {
  .agreeProtocol {
    .protocol {
      font-size: $font-size-xsmall;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: $textBlack;

      .spec {
        color: $themeColor;
      }
    }
  }
}
</style>
