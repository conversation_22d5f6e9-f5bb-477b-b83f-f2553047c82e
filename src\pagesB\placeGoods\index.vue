<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <CommonAd :ad="vAd.pointPlaceGoodsCustomAd||''" type="custom" />
    <!-- #endif -->
    <ComList :loadingType="loadingType" :bottom="120">
      <block v-for="(item, index) in listData" :key="index">
        <PlaceGoodsCard
          :info="item"
          :index="index"
          @onCheck="selectItem(item, index)"
          @checkValue="checkValue"
        />
      </block>
    </ComList>
    <view
      class="fixed-btn flexRowBetween"
      style="
            paddingBottom: calc(20rpx + constant(safe-area-inset-bottom)); 
            paddingBottom: calc(20rpx + env(safe-area-inset-bottom)); "
    >
      <u-checkbox
        :checked="isAllCheck"
        shape="circle"
        :label-disabled="false"
        @change="changeCheckAll"
        :label="'全选(' + selectNum + ')'"
      >
      </u-checkbox>
      <view class="total">
        <view class="total-box">
          <view>合计：</view>
          <view class="total-box-mark">￥</view>
          <view class="total-box-price">{{ goodsTotalPrice.toFixed(2) }}</view>
        </view>
        <view class="total-num"> 共计{{ goodsTotalNum }}件 </view>
      </view>
      <view class="btn" @click="confirmOrder">提交订单</view>
    </view>
    <LoginPopup />
  </view>
</template>

<script>
import myPull from "@/mixins/myPull.js";
import { getGoodsList } from "@/common/http/api";
import ComList from "@/components/list/ComList.vue";
import PlaceGoodsCard from "../components/PlaceGoodsCard.vue";
import LoginPopup from "../../components/LoginPopup.vue";
export default {
  components: { ComList, PlaceGoodsCard, LoginPopup },
  mixins: [myPull()],
  data() {
    return {
      device_sn: "",
      isAllCheck: false, //是否全部选中
      selectNum: 0,
      selectInfoList: [], //选中的list
      goodsTotalPrice: 0, //选中商品总价格
      goodsTotalNum: 0, //总数量
    };
  },
  methods: {
    getList(page, done) {
      let data = {
        page,
        device_sn: this.device_sn,
      };
      getGoodsList(data).then((res) => {
        let newData = res.data?.map((item) => {
          if (typeof item.isAmount == "undefined") item.isAmount = 1;
          if (typeof item.isCheck == "undefined") item.isCheck = false;
          return item;
        });
        done(newData);
      });
    },
    //选中状态改变
    selectItem(item, index) {
      if (typeof item.isCheck == "undefined") {
        this.$set(item, "isCheck", true);
      } else {
        item.isCheck = !item.isCheck;
      }
      this.selectInfoList = this.listData.filter((item) => item.isCheck) || [];
      this.isAllCheck = this.selectInfoList.length == this.listData.length;
      this.selectNum = this.selectInfoList.length;
      this.countGoodPrice();
    },
    //编辑框数量改变
    checkValue(item) {
      this.listData[item.index].isAmount = item.value;
      this.listData[item.index].isCheck && this.countGoodPrice();
    },
    //全选
    changeCheckAll() {
      this.isAllCheck = !this.isAllCheck;
      this.listData.forEach((item) => (item.isCheck = this.isAllCheck));
      this.selectInfoList = this.listData;
      this.selectNum = this.isAllCheck ? this.listData.length : 0;
      this.countGoodPrice();
    },
    //计算价格
    countGoodPrice() {
      let totalPrice = 0,
        totalNum = 0,
        selectList = [];
      this.listData.forEach((item) => {
        if (item.isCheck) {
          totalPrice += item.shop_price * item.isAmount;
          totalNum += item.isAmount;
          selectList.push(item);
        }
      });
      this.goodsTotalPrice = totalPrice;
      this.goodsTotalNum = totalNum;
      this.selectInfoList = selectList;
    },
    //确认订单
    confirmOrder() {
      if (this.goodsTotalNum > 0) {
        let setData = {
          list: this.selectInfoList,
          totalPrice: this.goodsTotalPrice,
          totalNum: this.goodsTotalNum,
          device_sn: this.device_sn,
        };
        uni.setStorageSync("select_purchase_goods", setData);
        uni.navigateTo({
          url: "/pagesB/placeGoods/PlaceGoodsConfirmOrder",
        });
      }
    },
  },
  onLoad(opt) {
    this.device_sn = opt?.device_sn || this.vDeviceSn || "";
    this.refresh();
  },
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style lang="scss" scoped>
.emptyContent {
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .empty {
    flex: 1;
    background-color: white;
  }
}

.list {
  padding: 30rpx;

  .list-item {
    margin-bottom: 30rpx;
  }
}

.fixed-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 999;

  .total {
    &-box {
      display: flex;
      align-items: flex-end;
      color: $textBlack;
      font-size: $font-size-base;

      &-mark {
        color: red;
        font-size: $font-size-xsmall;
      }

      &-price {
        color: red;
        font-size: $font-size-middle;
      }
    }

    &-num {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }

  .btn {
    padding: 20rpx 50rpx;
    color: #fff;
    font-size: $font-size-xlarge;
    background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
    border-radius: 20rpx;
  }
}
</style>
