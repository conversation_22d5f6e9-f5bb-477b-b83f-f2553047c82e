<template>
  <view :style="{
    height: (Number(height) || 0) + vIphoneXBottomHeight + 20 + 'rpx',
    // height: calc(((Number(height) || 0)  + 20)rpx + constant(safe-area-inset-bottom)); 
    // height: calc(((Number(height) || 0)  + 20)rpx + env(safe-area-inset-bottom)); 
  }">
  </view>
</template>

<script>
//iphonex 等机型的安全占位块
export default {
  name: "SafeBlock",
  props: { height: { type: String | Number, default: 0 } },
};
</script>

<style lang="scss" scoped>
</style>