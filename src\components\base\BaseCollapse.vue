<template>
  <u-collapse class="collapse" :accordion="accordion" :border="border" :isLink="true">
    <u-collapse-item :name="item.head" :title="item.head" v-for="(item, index) in list" :key="index">

      <!-- #ifdef MP-ALIPAY||MP-TOUTIAO -->
      <view slot="title">{{ item.head }}</view>
      <!-- #endif -->
      <view slot="right-icon">
        <BaseIcon name="arrow-right" size="20" />
      </view>
      {{ item.body }}
    </u-collapse-item>
  </u-collapse>
</template>

<script>
import BaseIcon from './BaseIcon.vue';
export default {
  components: { BaseIcon },
  props: {
    list: { type: Array, default: [] },
    accordion: { type: Boolean, default: true }, //手风琴模式
    border: { type: Boolean, default: false }, //是否显示外边框
  },
  data() {
    return {
      //   itemList: [
      //     {
      //       head: "赏识在于角度的转换",
      //       body: "只要我们正确择取一个合适的参照物乃至稍降一格去看待他人，值得赏识的东西便会扑面而来",
      //       open: true,
      //       disabled: true,
      //     },
      //   ],
    };
  },


};
</script>

<style lang="scss" scoped>
// .collapse {
//   /deep/ .u-collapse-item {
//     .u-line-1 {
//       white-space: normal;
//       line-height: 1.6;
//     }
//   }
// }
</style>
