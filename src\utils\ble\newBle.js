import { globalEvents } from "@/global/globalEvents";

var appOptions = {
  showLoading: (tip, methodName = '') => {
    uni.showLoading({
      title: tip,
      success: (res) => {
        console.log('方法名 ' + methodName + ' showLoading Succcess ', res, new Date());
      },
      fail: (res) => {
        console.log('方法名 ' + methodName + ' showLoading fail ', res, new Date());
      }
    });
  },
  hideLoading: (methodName = '', tips, resMsg) => {
    uni.hideLoading({
      success: (res) => {
        console.log("🚀 ~ hideLoading success", methodName, tips, resMsg)
      },
      fail: (res) => {
        console.log("🚀 ~ hideLoading fail", methodName, tips, resMsg)
      }
    });
  },
  showModal: (content, showCancel = false) => {
    uni.showModal({
      title: '温馨提示',
      content,
      showCancel,
      success: ({ confirm, cancel }) => { }
    })
  },
  showToast: (title, isSuccess = false, duration = 2000) => {
    uni.showToast({
      title,
      icon: isSuccess ? 'success' : 'none',
      mask: true,
      duration,
    })
  }

}
//1.初始化蓝牙
//2.搜索蓝牙设备
const app = getApp()
let _init = false;//是否正在初始化
let _discoveryStarted = false;//是否正在搜索
let _isSearchReach = false;//是否已经检索到设备

//回调
let readRechargeCallback = null;//读取充电状态回调
let writeCallback = null;//写入充电回调

//当前设备的操作类型
const OptionEnum = {
  None: -1,
  Connection: 0, //连接
  Reconnection: 1, //重连
  CloseLock: 2, //关闭
  VendingOpenLock: 8, //打开售卖机
  ReadStatus: 9, //读售卖机状态
  ReadBattery: 10, //读售卖机电量
  Recharge: 11,     //充电
  GetVolt: 12,     //获取设备电压
  ReadRecharge: 13,//读取充电状态
  ReadRechargeCallback: 14,//读取充电状态，单独回调
  pink: 15,//配对
  advance: 16,//前进
  back: 17,//后退
  shake: 18,//摇一摇
  direction: 19,//转向
  gear: 20,//挡位
  brake: 21//刹车
};
//这个是变量, 存储数据的变量
let action_type = OptionEnum.None;

var msg_id = 1;


//连接蓝牙的各种参数
let linkBleOption = {
  device_sn: '',//设备编号 也是搜索name
  device_type: '',//设备类型
}
const BluetoothDetail = {
  deviceId: "",//连接蓝牙设备ID
  notify_id: '',//通知特征值ID
  read_id: '',//支持read的特征值
  write_id: '',//支持write的特征值
  serviceId: '',//服务ID
}

//无线充充
class vendingOne {
  serviceUUID = "0000F040-0000-1000-8000-00805F9B34FB"
  readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
  writeUUID = "0000F041-0000-1000-8000-00805F9B34FB"; //写 UUID
  notifyUUID = "0000F042-0000-1000-8000-00805F9B34FB";//通知
  //充电指令
  rechargeDevice(min, hour, callback) {
    action_type = OptionEnum.Recharge; // 充电
    writeCallback = callback;
    console.log('准备写入充电指令，min=', min)
    console.log('准备写入充电指令，hour=', hour)
    //转16进制
    hour = parseInt(min / 60);//取整数
    min = Math.round(min % 60);//取余数
    console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
    let hexHour = 0x00;
    if (hour > 0) {
      hexHour = '0x' + parseInt(hour).toString(16);
    }
    let hexMin = 0x00;
    if (min > 0) {
      hexMin = '0x' + parseInt(min).toString(16);
    }
    console.log('充电时间转16进制：', hexHour, hexMin)

    let buff_array = [];
    buff_array.push(0xFE); // 1
    buff_array.push(0xD5); // 2
    buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
    buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
    buff_array.push(0x00);  // 5 固定数据
    buff_array.push(0x00);	// 6 固定数据
    buff_array.push(0x00);	// 7 固定数据
    buff_array.push(0x00);	// 8 固定数据
    buff_array.push(0x00);  // 9 固定数据
    buff_array.push(0x00);	// 10 固定数据
    buff_array.push(0x00);  // 11 固定数据
    buff_array.push(0x00);	// 12 固定数据
    buff_array.push(0x00);	// 13 固定数据
    buff_array.push(0x00);	// 14 固定数据
    buff_array.push(0x00);  // 15 固定数据
    buff_array.push(0x00);	// 16 固定数据
    buff_array.push(0x00);	// 17 固定数据
    buff_array.push(0x00);  // 18 固定数据
    buff_array.push(0x00);	// 19 固定数据
    buff_array.push(0X45); // 校验位
    console.log('充电指令：', buff_array);
    console.log('充电指令：', buf2hex(buff_array));
    writeData(buff_array,);
  }
  handleResData(data) {
    console.log("🚀 ~ action_type 当前操作类型", action_type)
    this.handleCallback(data)
  }
  handleCallback(resData) {
    console.log("🚀 ~ resData 充电指令回调", resData)
    if (resData && (action_type == OptionEnum.ReadRecharge || action_type == OptionEnum.ReadRechargeCallback)) {
      let length = resData.length;
      let status = resData.substr(length - 4, 4);
      console.log("🚀 ~ status 截取状态 ", status)

      if (status == '0046') {
        //充电成功
        if (action_type == OptionEnum.ReadRecharge) {
          if (writeCallback != null) {
            writeCallback(true);
          }
        } else if (action_type == OptionEnum.ReadRechargeCallback) {
          if (readRechargeCallback != null) {
            readRechargeCallback(true);
          }
        }
      } else if (status == '0000' || status == '3746') {
        //充电失败
        if (action_type == OptionEnum.ReadRecharge) {
          if (writeCallback != null) {
            writeCallback(false);
          }
        } else if (action_type == OptionEnum.ReadRechargeCallback) {
          if (readRechargeCallback != null) {
            readRechargeCallback(false);
          }
        }
      }
      action_type = OptionEnum.None;
    }
  }
  readChargeStatus(flag) {
    // 无线充						
    console.log("🚀 ~ 无线充   开始读状态 ", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.read_id)
    uni.readBLECharacteristicValue({
      // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
      deviceId: BluetoothDetail?.deviceId,
      // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
      serviceId: BluetoothDetail?.serviceId,
      // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
      characteristicId: BluetoothDetail?.read_id,
      success(res) {
        console.log("🚀 ~ res readBLECharacteristicValue", res)
        action_type = flag ? OptionEnum.ReadRechargeCallback : OptionEnum.ReadRecharge;

        /* #ifdef MP-ALIPAY */
        res?.characteristic?.value && agreementBle.handleResData(res?.characteristic?.value);
        /* #endif */
        console.log('readBLECharacteristicValue ', res);
        console.log('readBLECharacteristicValue:', res.errCode)
        /* #ifndef MP-ALIPAY */
        if (res.errno === 1509000) {//未充电，直接回调
          if (readRechargeCallback != null) {
            readRechargeCallback(false);
          }
        }
        /* #endif */
      }, fail: (err) => {
        console.log("🚀 ~ err readBLECharacteristicValue", err)
      }
    })


  }
}
class vendingTwe extends vendingOne {
  constructor() {
    super()
  }
  //充电指令
  rechargeDevice(min, hour, callback) {
    action_type = OptionEnum.Recharge; // 充电
    writeCallback = callback;
    console.log('准备写入充电指令，min=', min)
    console.log('准备写入充电指令，hour=', hour)
    //转16进制
    hour = parseInt(min / 60);//取整数
    min = Math.round(min % 60);//取余数
    console.log('准备写入充电指令，转换后的：', `${hour} 小时,${min} 分钟`)
    let hexHour = 0x00;
    if (hour > 0) {
      hexHour = '0x' + parseInt(hour).toString(16);
    }
    let hexMin = 0x00;
    if (min > 0) {
      hexMin = '0x' + parseInt(min).toString(16);
    }
    console.log('充电时间转16进制：', hexHour, hexMin)

    let buff_array = [];
    buff_array.push(0xA0); // 1
    buff_array.push(0xD5); // 2
    buff_array.push(hexHour); // 3  小时 0x00~0x30  0~48小时 最大支持48小时
    buff_array.push(hexMin); // 4  分钟 0x00~0x3c  0~60分钟
    buff_array.push(0x00);  // 5 固定数据
    buff_array.push(0x00);	// 6 固定数据
    buff_array.push(0x00);	// 7 固定数据
    buff_array.push(0x00);	// 8 固定数据
    buff_array.push(0x00);  // 9 固定数据
    buff_array.push(0x00);	// 10 固定数据
    buff_array.push(0x00);  // 11 固定数据
    buff_array.push(0x00);	// 12 固定数据
    buff_array.push(0x00);	// 13 固定数据
    buff_array.push(0x00);	// 14 固定数据
    buff_array.push(0x00);  // 15 固定数据
    buff_array.push(0x00);	// 16 固定数据
    buff_array.push(0x00);	// 17 固定数据
    buff_array.push(0x00);  // 18 固定数据
    buff_array.push(0x00);	// 19 固定数据
    buff_array.push(0X45); // 校验位
    console.log('充电指令：', buff_array);
    console.log('充电指令：', buf2hex(buff_array));
    writeData(buff_array,);
  }
}
//毛巾机
class vendingThree {
  serviceUUID = "0000FCF0-0000-1000-8000-00805F9B34FB"
  // readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
  writeUUID = "0000FCF2-0000-1000-8000-00805F9B34FB"; //写 UUID
  notifyUUID = "0000FCF1-0000-1000-8000-00805F9B34FB";//通知
  //开锁指令
  openVendingLock(isAll, channel, callBack) {
    action_type = OptionEnum.VendingOpenLock; // 开锁
    writeCallback = callBack;
    let tempVal = channel
    let buff_array = [];
    if (isAll) {
      buff_array.push(0xa5); // 数据头
      buff_array.push(0x01); // 长度
      buff_array.push(0x0b); // 命令
      console.log('全开锁指令：', buff_array);
      console.log('全开始指令：', buf2hex(buff_array));
    } else {
      console.log('====开锁编号===》', tempVal);
      buff_array.push(0xa5); // 数据头
      buff_array.push(0x02); // 长度
      buff_array.push(tempVal); // 命令
      buff_array.push(0x05); // time
    }
    writeData(buff_array, '正在出货...');
  }
  handleResData(data) {
    console.log("🚀 ~ action_type 当前操作类型", action_type)
    this.handleCallback(data)
  }
  handleCallback(resData) {
    console.log("🚀 ~ resData 写入指令回调", resData)
    if (action_type == OptionEnum.VendingOpenLock) { //操作的是 开锁
      writeCallback(resData == 'a5a5a5')
    }
    action_type = OptionEnum.None;
  }
}
class vendingFour {
  serviceUUID = "0783b03e-8535-b5a0-7140-a304f013c3b7"
  // readUUID = "0000F042-0000-1000-8000-00805F9B34FB";// 读UUID
  writeUUID = "0783b03e-8535-b5a0-7140-a304f013c3ba"; //写 UUID
  notifyUUID = "0783b03e-8535-b5a0-7140-a304f013c3b8";//通知
  isNeedCheck = true;//是否需要握手
  //链接握手
  checkConnect() {
    // 0x5C,0x5F,0x57,0x59,0x5E
    console.log('checkConnect 访问：');
    let buff_array = [];
    buff_array.push(0xFF); // 1
    buff_array.push(0x55); // 2
    buff_array.push(0x12); // 3
    buff_array.push(0x01); // 4
    buff_array.push(parseInt(msg_id, 16));
    buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
    buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
    buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
    buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
    buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
    buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
    buff_array.push(0x01);// 指令
    buff_array.push(0x5C);// 固定数据
    buff_array.push(0x5F);// 固定数据
    buff_array.push(0x57);// 固定数据
    buff_array.push(0x59);// 固定数据
    buff_array.push(0x5E);// 固定数据
    buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
    console.log('握手的指令：', buff_array);
    writeData(buff_array, '', false);
  }
  //开锁指令
  openVendingLock(isAll, channel, callBack) {

    action_type = OptionEnum.VendingOpenLock; // 开锁
    writeCallback = callBack;
    let tempVal = channel
    let buff_array = [];
    buff_array.push(0xFF); // 1
    buff_array.push(0x55); // 2
    buff_array.push(0x12); // 3
    buff_array.push(0x01); // 4 ： 上位机 --> 设备
    buff_array.push(parseInt(msg_id, 16));       // 消息序列号
    buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
    buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
    buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
    buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
    buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
    buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
    if (isAll) {
      buff_array.push(0x04);// 指令
      buff_array.push(0xB1);// 固定数据
      buff_array.push(0xB2);// 固定数据
      buff_array.push(0xB3);// 固定数据
      buff_array.push(0xB4);// 固定数据
      buff_array.push(0xB5);// 固定数据
      console.log('全开锁指令：', buff_array);
      console.log('全开始指令：', buf2hex(buff_array));
    } else {
      console.log('====开锁编号===》', tempVal);
      buff_array.push(0x02);// 指令
      buff_array.push(0x00);// 固定数据
      buff_array.push(0x00);// 固定数据
      buff_array.push(tempVal);// 固定数据
      buff_array.push(0x00);// 固定数据
      buff_array.push(0x00);// 固定数据

    }
    buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位
    writeData(buff_array, '正在出货...');
  }
  //充电指令
  rechargeDevice(min, hour, callback) {
    action_type = OptionEnum.Recharge; // 充电
    writeCallback = callback;
    console.log("🚀 ~ min 准备写入充电指令,分钟:", min)

    let hexMin = '0x' + parseInt(min).toString(16);

    console.log('充电时间转16进制：', hexMin)

    let buff_array = [];
    buff_array.push(0xFF); // 1
    buff_array.push(0x55); // 2
    buff_array.push(0x12); // 3
    buff_array.push(0x01); // 4 ： 上位机 --> 设备
    buff_array.push(parseInt(msg_id, 16));       // 消息序列号
    buff_array.push(parseInt(app.globalData.device_sn.substr(0, 2), 16));// 设备号1
    buff_array.push(parseInt(app.globalData.device_sn.substr(2, 2), 16));// 设备号2
    buff_array.push(parseInt(app.globalData.device_sn.substr(4, 2), 16));// 设备号3
    buff_array.push(parseInt(app.globalData.device_sn.substr(6, 2), 16));// 设备号4
    buff_array.push(parseInt(app.globalData.device_sn.substr(8, 2), 16));// 设备号5
    buff_array.push(parseInt(app.globalData.device_sn.substr(10, 2), 16));// 设备号6
    buff_array.push(0x05);// 指令
    buff_array.push(0x00);// 固定数据
    buff_array.push(0x00);// 固定数据
    buff_array.push(0x00);// 固定数据
    buff_array.push(hexMin);// 固定数据
    buff_array.push(0x00);// 固定数据
    buff_array.push(parseInt(bufCheckXor(buff_array), 16)); // 校验位

    console.log('充电指令：', buff_array);
    console.log('充电指令：', buf2hex(buff_array));
    writeData(buff_array,);
  }

  //写入返回
  handleResData(data) {
    console.log("🚀 ~ action_type 当前操作类型", action_type)
    this.handleCallback(data)
  }
  //处理写入返回
  handleCallback(resData) {
    console.log("🚀 ~ resData 写入指令回调", resData)
    if (resData) {
      let resLength = resData?.length;
      let cmd = resData.substr(resLength - 6, 2);
      console.log("🚀 ~ cmd", cmd)
      let status = resData.substr(resLength - 4, 2);
      console.log("🚀 ~ status", status)
      if (action_type == OptionEnum.VendingOpenLock) { //操作的是 开锁
        writeCallback((cmd == '02' || cmd == '04') && status == '03')
      } else if (action_type = OptionEnum.Recharge) {
        console.log("🚀 ~ writeCallback", action_type, cmd, status)
        writeCallback(cmd == '05' && status == '06')

      }
      action_type = OptionEnum.None;
    }

  }
}

//电玩车

class vendingSixe {
  serviceUUID = "0000FFF0-0000-1000-8000-00805F9B34FB"
  writeUUID = "0000FFF2-0000-1000-8000-00805F9B34FB"; //写 UUID
  notifyUUID = "0000FFF1-0000-1000-8000-00805F9B34FB";//通知
  readUUID = "0000FFF1-0000-1000-8000-00805F9B34FB";// 读UUID
  //配对
  openVendingLock(Instruction, direction, speed, brake, swing, type, device_sn) {
    let buff_array = []
    buff_array.push(Instruction)//指令
    buff_array.push(direction)//转向
    buff_array.push(speed)//挡位
    buff_array.push(brake)//刹车
    buff_array.push(swing)//摆动挡位
    buff_array.push(type)//功能标志位
    let str = device_sn//设备序列号
    let arr = stringToPrefixedHexArray(str)
    for (let i = 0; i < arr.length; i++) {
      buff_array.push(arr[i])
    }
    console.log('输入指令', buf2hex(buff_array))
    writeData(buff_array, '');
  }
  //回调函数
  handleResData(data) {
    this.handleCallback(data)
  }
  //回调函数
  handleCallback(resData) {
    console.log('data', resData)
  }
}
const headBle = (type) => {
  switch (type) {
    case '0x88': {
      return new vendingOne();
    }
    case '0x89': {
      return new vendingTwe();
    }
    case '0x92': {
      return new vendingThree();
    }
    case '0x81': {
      return new vendingFour();
    }
    case '0x93': {
      return new vendingSixe();
    }
    default: return new vendingTwe();
  }

}

var agreementBle = ''


/**
 * 初始化连接的参数
 * @param {*} data 是个对象，linkBleOption参数
 */
const initBleData = (data) => {
  _init = false;//是否初始化了
  _isSearchReach = false;//是否搜索到了
  action_type = OptionEnum.None;
  msg_id = 1
  linkBleOption = {
    ...linkBleOption,
    ...data,
  }
  linkBleOption.device_type = linkBleOption?.device_type || app.globalData.device_type
  linkBleOption.device_sn = linkBleOption?.device_sn || app.globalData.device_sn
  agreementBle = headBle(linkBleOption.device_type)
}


/**
 *  初始化蓝牙
 * @param {*} data 初始化数据  同linkBleOption参数
 */
const initBle = async (data) => {
  console.log("🚀 ~ initBle 进入初始化")
  if (app.globalData.connected) return console.log("🚀 ~ initBle 已经连接了");
  if (_init) return console.log('🚀 ~ 已经初始化过了')
  initBleData(data);
  _init = true
  /* #ifdef MP-ALIPAY */
  try {
    await closeBle();
  } catch {
    console.log("🚀 ~ 关闭蓝牙失败")
  }
  /* #endif */
  appOptions.showLoading('初始化蓝牙', 'initBle start');
  uni.openBluetoothAdapter({
    success(res) {
      // if (!res?.isSupportBLE) {
      //     appOptions.hideLoading('initBle 抱歉，您的手机蓝牙暂不可用');
      //     appOptions.showModal({ content: '抱歉，您的手机蓝牙暂不可用' });
      //     return;
      // }
      appOptions.showLoading('蓝牙初始化成功', 'initBle success');
      startBluetoothDevicesDiscovery();
    },
    fail(error) {
      console.log; ("🚀 ~ error 蓝牙初始化失败", error)
      appOptions.hideLoading('蓝牙初始化失败', 'initBle fail', error);
      /* #ifdef MP-ALIPAY */
      if (error?.error == 12) {
        let rtnMsg = '蓝牙未打开';
        app.globalData?.SystemInfo?.platform == 'android' && (rtnMsg = '请检查手机蓝牙和定位是否打开')

        appOptions.showModal(rtnMsg)
      } else {
        appOptions.showModal(error?.errorMessage)
      }
      /* #endif */
      /* #ifndef MP-ALIPAY */
      bleErrState(error);
      /* #endif */
      closeBle();
    }
  });
}



/**
 * 开始搜寻附近的蓝牙外围设备。此操作比较耗费系统资源，请在搜索并连接到设备后调用 uni.stopBluetoothDevicesDiscovery 方法停止搜索。
 */
const startBluetoothDevicesDiscovery = () => {

  uni.startBluetoothDevicesDiscovery({
    allowDuplicatesKey: false,
    success(result) {
      console.log("🚀 ~ result 开始搜寻附近的蓝牙外围设备", result)
      onBluetoothDeviceFound()
    },

    fail(error) {
      appOptions.hideLoading('搜索设备失败', '搜索设备失败 startBluetoothDevicesDiscovery fail', error);
      appOptions.showToast('搜索设备失败');
      // bleErrState(error);
      closeBle();
    }
  })
}
/**
 * 监听寻找到新设备的事件
 */
const onBluetoothDeviceFound = () => {
  appOptions.showLoading('搜索设备中', '搜索设备中 onBluetoothDeviceFound start');
  offBluetoothDeviceFound();//先停止监听获取新设备
  Timer && clearInterval(Timer);
  linkTimer(10).catch((error) => {
    appOptions.hideLoading('搜索设备超时', '搜索设备超时 onBluetoothDeviceFound fail', error);
    closeBle();
    return appOptions.showModal(`1、请查看周围是否有连接此设备的蓝牙
2、请先关闭手机蓝牙在打开一次
3、请检查位置信息是否打开
4、退出小程序重新扫码进入`)
  })
  //这里加loading主要是避免其他请求隐藏了loading,让loading重新显示
  let temporary = setTimeout(() => {
    appOptions.showLoading('搜索设备中', '搜索设备中 onBluetoothDeviceFound start');
    clearTimeout(temporary);
  }, 200);
  uni.onBluetoothDeviceFound(({ devices }) => {
    console.log("🚀 ~ devices 开始搜索设备", devices)
    let searchDevice = devices.find(item => item.name == linkBleOption.device_sn);
    console.log("🚀 ~ searchDevice  搜索到对应的设备信息", searchDevice)
    if (searchDevice?.name == linkBleOption.device_sn && !_isSearchReach) {
      _isSearchReach = true;//搜索到了设备,不再让搜索进来
      Timer && clearInterval(Timer);
      temporary && clearTimeout(temporary);
      BluetoothDetail.deviceId = searchDevice.deviceId;
      console.log("🚀 ~ BluetoothDetail", BluetoothDetail)
      appOptions.showLoading('搜索设备成功', '搜索设备成功 onBluetoothDeviceFound success');
      stopBluetoothDevicesDiscovery();//停止搜寻附近的蓝牙外围设备
      offBluetoothDeviceFound();//取消监听获取新设备
      //连接设备
      createBLEConnection()
    }
  })



}




/**
 * 创建连接 连接蓝牙设备
 */
const createBLEConnection = () => {
  appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
  console.log("🚀 ~ createBLEConnection 正在连接")
  return new Promise((resolve, reject) => {
    uni.createBLEConnection({
      deviceId: BluetoothDetail?.deviceId,
      success(result) {
        console.log("🚀 ~ result 连接成功", result)
        getBLEDeviceServices();//获取蓝牙所有服务
        onBLEConnectionStateChange();//监听连接状态
        resolve(result)
      },
      fail(error) {
        appOptions.hideLoading('连接设备失败', '连接设备失败 createBLEConnection fail', error);
        appOptions.showToast('连接设备失败')
        // bleErrState(error);
        closeBle();
        reject(error)
      }
    })
  })
}


/**
 * 监听连接状态
 */
const onBLEConnectionStateChange = () => {
  //防止重复调用监听事件，先关闭监听
  console.log("🚀 ~ onBLEConnectionStateChange 监听连接状态")
  offBLEConnectionStateChange();
  uni.onBLEConnectionStateChange(({ deviceId, connected }) => {
    console.log("🚀 ~ connecte 监听连接状态", connected)

    if (!connected) {
      setBleConnected(connected)
      appOptions.hideLoading('设备断开连接', '设备断开连接 onBLEConnectionStateChange fail', connected, deviceId);
      appOptions.showToast('与设备连接已断开,请查看蓝牙设备是否打开');
      closeBle();
    }
  })
}
/**
 * 获取蓝牙所有服务
 */
const getBLEDeviceServices = () => {
  appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
  console.log("🚀 ~ getBLEDeviceServices 获取蓝牙所有服务")
  Timer && clearInterval(Timer)
  linkTimer(10).catch((error) => {
    appOptions.hideLoading('搜索设备超时', '搜索设备超时 onBluetoothDeviceFound fail', error);
    closeBle();
    return appOptions.showModal(`1、请查看周围是否有连接此设备的蓝牙
2、请先关闭手机蓝牙在打开一次
3、请检查位置信息是否打开
4、退出小程序重新扫码进入`)
  })
  uni.getBLEDeviceServices({
    deviceId: BluetoothDetail.deviceId,
    success({ services }) {
      console.log("🚀 ~ services  获取服务", services)
      services?.filter(item => item.isPrimary)?.forEach(item => {
        let { uuid } = item
        let serviceFlag = false;
        /* #ifdef MP-ALIPAY */
        serviceFlag = (agreementBle?.serviceUUID)?.toLowerCase().includes(uuid.toLowerCase());
        /* #endif */
        /* #ifdef MP-WEIXIN */
        serviceFlag = uuid.toLowerCase() == (agreementBle?.serviceUUID)?.toLowerCase()

        /* #endif */
        serviceFlag && getBLEDeviceCharacteristics(BluetoothDetail?.deviceId, uuid)
        console.log("🚀 ~ serviceFlag  获取设备服务", serviceFlag)
      });
    },
    fail: (error) => {
      console.log("🚀 ~ error 获取设备服务失败", error)
      appOptions.hideLoading('获取设备服务失败', '获取设备服务失败 getBLEDeviceServices fail', error);
      appOptions.showToast('获取设备服务失败')
      // bleErrState(error);
      closeBle();
    }, complete: () => {
      Timer && clearInterval(Timer)
    }
  })
}
/**
 * 获取蓝牙特征值
 * @param {*} deviceId 
 * @param {*} serviceId 
 */
const getBLEDeviceCharacteristics = (deviceId, serviceId) => {
  BluetoothDetail.serviceId = serviceId;
  // appOptions.showLoading('正在连接', '连接设备中 createBLEConnection start');
  uni.getBLEDeviceCharacteristics({
    deviceId,
    serviceId,
    success: ({ characteristics }) => {
      appOptions.hideLoading('获取设备特征值成功', '获取设备特征值成功 getBLEDeviceCharacteristics success', characteristics);
      appOptions.showToast('蓝牙连接成功', true);
      setBleConnected(true)
      characteristics?.forEach(item => {
        let uuid = "", notifyFlag = false, readFlag = false, writeFlag = false;
        /* #ifdef MP-ALIPAY */
        uuid = item.characteristicId;
        /* #endif */
        /* #ifndef MP-ALIPAY */
        uuid = item.uuid;
        /* #endif */
        const { indicate, notify, read, write } = item?.properties
        /* #ifdef MP-ALIPAY */
        notifyFlag = agreementBle?.notifyUUID && agreementBle?.notifyUUID?.toLowerCase().includes(uuid.toLowerCase());
        readFlag = agreementBle?.readUUID && agreementBle?.readUUID?.toLowerCase().includes(uuid.toLowerCase());
        writeFlag = agreementBle?.writeUUID && agreementBle?.writeUUID?.toLowerCase().includes(uuid.toLowerCase());
        /* #endif */
        /* #ifndef MP-ALIPAY */
        notifyFlag = agreementBle?.notifyUUID && uuid.toLowerCase() == agreementBle?.notifyUUID?.toLowerCase();
        readFlag = agreementBle?.readUUID && uuid.toLowerCase() == agreementBle?.readUUID?.toLowerCase();
        writeFlag = agreementBle?.writeUUID && uuid?.toLowerCase() == agreementBle?.writeUUID?.toLowerCase();
        /* #endif */
        if ((indicate || notify) && notifyFlag) {
          //通知特征
          BluetoothDetail.notify_id = uuid;
          notifyBLECharacteristicValueChange();//开启通知
        }

        if (write && writeFlag) {
          //写特征
          BluetoothDetail.write_id = uuid;

          agreementBle?.isNeedCheck && agreementBle?.checkConnect()
        }

        if (read && readFlag) {
          //读特征
          BluetoothDetail.read_id = uuid;
        }
        console.log("🚀 ~ BluetoothDetail", BluetoothDetail)


      })
      /* #ifndef MP-ALIPAY */
      onBLECharacteristicValueChange();
      /* #endif */
      action_type = OptionEnum.None
    },
    fail: (error) => {

      appOptions.hideLoading('获取设备特征值失败', '获取设备特征值失败 getBLEDeviceCharacteristics fail', error);
      appOptions.showToast('获取设备特征值失败')
      // bleErrState(error);
      closeBle();
    }
  });
}

/**
 * 开启蓝牙通知服务器
 */
const notifyBLECharacteristicValueChange = () => {
  console.log("🚀 ~ notifyBLECharacteristicValueChange  调用通知Api", BluetoothDetail?.deviceId, BluetoothDetail?.serviceId, BluetoothDetail?.notify_id)

  uni.notifyBLECharacteristicValueChange({
    deviceId: BluetoothDetail?.deviceId,
    serviceId: BluetoothDetail?.serviceId,
    characteristicId: BluetoothDetail?.notify_id,
    state: true,
    success: (result) => {
      console.log("🚀 ~ result 调用通知Api成功", result)

    },
    fail: (error) => {
      console.log("🚀 ~ error 调用通知Api失败", error)

    }
  })


}



/**
 * 根据 uuid 获取处于已连接状态的设备
 */
const getConnectedBluetoothDevices = () => {
  return new Promise((resolve, reject) => {
    uni.getConnectedBluetoothDevices({
      services: [],
      success: (res) => {
        if (res?.devices?.length === 0) {
          reject(res)
          return;
        }
        resolve(res)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
/**
 * 监听低功耗蓝牙设备的特征值变化事件
 */
const onBLECharacteristicValueChange = () => {
  console.log("🚀 ~  onBLECharacteristicValueChange  监听数据");
  offBLECharacteristicValueChange();//避免重复监听，先取消
  uni.onBLECharacteristicValueChange(({
    deviceId,
    serviceId,
    characteristicId,
    value
  }) => {
    msg_id++
    console.log("🚀 ~ 获取到特征值变化 characteristicId", characteristicId, "value", value)
    let resData = null
    // #ifdef MP-WEIXIN
    resData = ab2hex(value);
    // #endif
    // #ifdef MP-ALIPAY
    resData = value
    // #endif
    console.log("🚀 ~ resData  设备返回特征值", resData)
    agreementBle.handleResData(resData)
  })

}




/**
 * 写入数据
 * @param {*} hex  写入数据
 * @param {*} title 提示文字
 */
const writeData = (hex, title = '正在写入指令', isShowWriteStatus = true) => {
  if (title) {
    appOptions.showLoading(title, 'writeData');
  }
  !BluetoothDetail?.deviceId && appOptions.showToast('写入数据失败~', false);
  let enDataBuf = new Uint8Array(hex);
  let buffer1 = enDataBuf.buffer;
  console.log("🚀 ~写入指令  hex", hex)
  uni.writeBLECharacteristicValue({
    deviceId: BluetoothDetail?.deviceId,
    serviceId: BluetoothDetail?.serviceId,
    characteristicId: BluetoothDetail?.write_id,
    value: buffer1,
    success: (res) => {
      console.log("🚀 ~ res 写入指令成功", res)
      if (title) {
        appOptions.hideLoading('写入指令成功', res);
      }
      isShowWriteStatus && appOptions.showToast('写入指令成功', true)
      if (action_type == OptionEnum.Recharge
        || action_type == OptionEnum.ReadRecharge
        || action_type == OptionEnum.ReadRechargeCallback) {
        agreementBle?.readChargeStatus();//无线充的读取
      }
      //else {
      // onBLECharacteristicValueChange();

      // }
      /* #ifndef MP-ALIPAY */
      onBLECharacteristicValueChange();
      /* #endif */
    },
    fail(err) {
      console.log("🚀 ~ err 写入指令失败", err)
      appOptions.hideLoading('写入数据失败 writeData', err);
      isShowWriteStatus && appOptions.showToast('写入指令失败');
    }
  })



}




/**
 * 关闭蓝牙模块
 */
const closeBle = () => {
  initBleData()
  return new Promise((resolve, reject) => {
    if (BluetoothDetail?.deviceId) {
      uni.closeBLEConnection({
        deviceId: BluetoothDetail?.deviceId,
        success: (result) => { },
        fail: (error) => { }
      })
    }
    uni.closeBluetoothAdapter({
      success(result) {
        if (Timer) {
          clearInterval(Timer)
          Timer = null
        }
        setBleConnected(false);
        offBLEConnectionStateChange();
        offBLECharacteristicValueChange();
        offBluetoothAdapterStateChange();
        offBluetoothDeviceFound();
        stopBluetoothDevicesDiscovery();
        resolve(result);
      },
      fail(error) {
        console.log("🚀 ~ error 关闭蓝牙失败", error)
        reject(error)
      }
    })
  })


}

/**
 * 停止搜寻附近的蓝牙外围设备
 */
const stopBluetoothDevicesDiscovery = () => {
  uni.stopBluetoothDevicesDiscovery({
    success: (result) => {
      console.log("🚀 ~ result  停止搜索",)

    },
    fail: (error) => { }
  })
}


//取消监听事件》》》》》》》》》》》  start
//取消监听蓝牙连接状态的改变事件
const offBLEConnectionStateChange = () => {

  /* #ifdef MP-ALIPAY */
  my.offBLEConnectionStateChanged();
  /* #endif */

  /* #ifdef MP-WEIXIN */
  wx.offBLEConnectionStateChange()
  /* #endif */

}

//取消监听蓝牙设备的特征值变化事件
const offBLECharacteristicValueChange = () => {
  /* #ifdef MP-ALIPAY */
  my.offBLECharacteristicValueChange();
  /* #endif */
  /* #ifdef MP-WEIXIN */
  wx.offBLECharacteristicValueChange()
  /* #endif */
}
//取消监听蓝牙适配器状态变化事件
const offBluetoothAdapterStateChange = () => {
  /* #ifdef MP-ALIPAY */
  my.offBluetoothAdapterStateChange();
  /* #endif */
  /* #ifdef MP-WEIXIN */
  wx.offBluetoothAdapterStateChange()
  /* #endif */
}
//取消监听寻找到新设备的事件
const offBluetoothDeviceFound = () => {
  /* #ifdef MP-ALIPAY */
  my.offBluetoothDeviceFound();
  /* #endif */
  /* #ifdef MP-WEIXIN */
  wx.offBluetoothDeviceFound()
  /* #endif */
}
//取消监听事件<《《《《《《《《《《《  end


/**
 * 蓝牙 API 错误码对照表
 * @param {*} errorCodeOption 错误信息
 */
const bleErrState = (errorCodeOption) => {
  //{error: 12, errorMessage: '蓝牙未打开'}
  //{errno: 1500102, errMsg: "openBluetoothAdapter:fail open fail", state: 4, errCode: 10001}
  let errorCode = null, rtnMsg = ''
  errorCode = errorCodeOption?.errCode
  if (errorCode) {
    switch (errorCode) {
      case 10001: rtnMsg = '蓝牙未打开';
        app.globalData?.SystemInfo?.platform == 'android' && (rtnMsg = '请检查手机蓝牙和定位是否打开')
        appOptions.showModal(rtnMsg)
        break;
      case 10002:
        appOptions.showToast('没有找到指定设备')
        break;
      case 10003:
        appOptions.showToast('连接失败')
        break;
      case 10004:
        appOptions.showToast('没有找到指定服务')
        break;
      case 10005:
        appOptions.showToast('没有找到指定特征')
        break;
      case 10006: appOptions.showToast('当前连接已断开')
        break;
      case 10007: appOptions.showToast('当前特征不支持此操作')
        break;
      case 10008:
        appOptions.showToast('其余所有系统上报的异常')
        break;
      case 10009:
        appOptions.showToast('系统版本低于 4.3 不支持蓝牙')
        break;
      case 100010:
        appOptions.showToast('蓝牙已连接')
        break;
      case 100011:
        appOptions.showToast('配对设备需要配对码')
        break;
      case 100012:
        appOptions.showToast('连接超时')
        break;
      case 10013:
        appOptions.showToast('连接设备格式不正确')
        break;
    }
  } else {
    errorCodeOption?.errMsg && appOptions.showToast(errorCodeOption?.errMsg)
  }

}

/**
 * 一个定时器 
 * @param {*} time 定时器
 * @returns 
 */
let Timer = null;
const linkTimer = (time) => {
  return new Promise((resolve, rej) => {
    let _time = 0
    Timer = setInterval(function () {
      if (_time < time) {
        _time++

      } else {
        rej(false)
        clearInterval(Timer)
      }
    }, 1000)
  })
}
/**
 * 设置蓝牙连接状态
 * @param {*} connected 
 */
const setBleConnected = (connected = false) => {
  !connected && (_init = false)
  app.globalData.connected = connected
  uni.$emit(globalEvents.EVENT_BLE_CONNECT_CHANGED, {
    connected: connected
  })
}


//一些操作
//读取充电状态
const readChargeStatus = (callback) => {
  console.log("🚀 ~ readChargeStatus 读状态",)
  agreementBle.readChargeStatus(true);
  readRechargeCallback = callback
}

/**
 * 设备执行充电
 * @param {*} time 充电分钟
 * @param {*} callback 充电回调
 * @param {*} hour 充电小时
 */
const rechargeDevice = (time, callback, hour = 0) => {
  agreementBle?.rechargeDevice(time, hour, callback)
}


/**
 * 开锁
 * @param {*} isAll 是否是全开锁
 * @param {*} cancel 货单号
 * @param {*} callback 回调
 */
const openLock = (isAll = false, cancel = 1, callback) => {
  agreementBle?.openVendingLock(isAll, cancel, callback)
}
const remoteControl = (Instruction, direction, speed, brake, swing, type, device_sn) => {
  agreementBle?.openVendingLock(Instruction, direction, speed, brake, swing, type, device_sn)
}

//一些处理方法
// 二进制处理方法 buf转16进制
const buf2hex = (buffer) => {
  return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('')
}
// ArrayBuffer转16进度字符串示例
const ab2hex = (buffer) => {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('');
}


function bufCheckXor(buff_array) {
  var ar2 = 0;
  for (let j = 0; j < buff_array.length; j++) {
    ar2 ^= buff_array[j] //前两位的数组的异或结果在和第三位数组异或，以此类推得到最终结果
  }
  return parseInt(ar2).toString(16) //将得到结果进行整型，转为16进制得到最终结果
}



export default {
  initBleData,//初始化需要连接的参数
  initBle,//蓝牙初始化
  readChargeStatus,//读取充电状态
  rechargeDevice,//充电
  closeBle,//关闭蓝牙
  openLock,//开锁
  remoteControl,//遥控
}