<template>
  <!--我的钱包页面-->
  <view class="content">
    <view class="centerArea" @click="goToMy()">
      <view class="h_left">
        <view>
          <view class="h_my">
            <view class="h_img">
              <image class="image" src="../static/wallet/hb.png"></image>
            </view>
            <text class="h_b_text">{{ vHName }}</text>
            <text class="h_m">|</text>
            <text class="h_m_text h_c_text">我的{{ vHName }}:</text>
            <text class="h_b_text">{{ vMemberInfo.cash || "0.00" }}</text>
          </view>
          <view class="h_c_text">
            <text>
              骑行就用{{ vHName }}
            </text>
            <text class="h_sm_text">
              全平台通用
            </text>

          </view>
        </view>
        <view></view>
      </view>
      <view class="h_right">
        <BaseIcon name="arrow-right" size="20" color="rgb(222, 220, 220)" />
      </view>
    </view>
    <view class="buttomArea">
      <view class="but_item" @click="showTips()">
        <view class="k_img">
          <image class="image" src="../static/wallet/yk.png"></image>
        </view>
        <view class="item_right item_bd item_pading">
          <view class="item_flex">
            <view class="but_text">月卡</view>
          </view>
          <view class="item_flex">
            <text class="but_text">
              {{ '0' }}张
            </text>
            <text class="but_text but_m">></text>
          </view>
        </view>

      </view>
      <view class="but_item" @click="showTips()">
        <view class="k_img">
          <image class="image" src="../static/wallet/ck.png"></image>
        </view>
        <view class="item_right item_pading">
          <view class="item_flex">
            <view class="but_text">次卡</view>
          </view>
          <view class="item_flex">
            <text class="but_text">
              {{ '0' }}张
            </text>
            <text class="but_text but_m">></text>
          </view>
        </view>

      </view>
    </view>
    <LoginPopup />
  </view>
</template>

<script>
import { getUserInfo } from "@/common/http/api";
import BaseIcon from "@/components/base/BaseIcon.vue";
import LoginPopup from "@/components/LoginPopup.vue";
import BaseButton from "@/components/base/BaseButton.vue";

export default {
  name: "index",
  components: { BaseIcon, LoginPopup, BaseButton },

  data() {
    return {
      cash: "0",
      id: [],
    };
  },
  methods: {
    getInfo() {
      getUserInfo().then((res) => {
        uni.$u.vuex('vMemberInfo', res)
      });
    },
    showTips() {
      uni.showToast({
        title: "即将上线,敬请期待~",
        icon: "none",
      });
    },
    goToMy() {
      uni.navigateTo({
        url: `/pagesD/wallet/myWallet`,
      });
    },
    gotoAssetsDetailPage() {
      uni.navigateTo({
        url: `/pagesD/wallet/consumeList?type=1`,
      });
    },

    gotoBeansDetailPage() {
      uni.navigateTo({
        url: `/pagesD/wallet/consumeList?type=2`,
      });
    },

    gotoRecharge() {
      uni.navigateTo({
        url: `/pagesD/wallet/rechargeCenter`,
      });
    },

    onClickWithdraw() {
      uni.navigateTo({
        url: `/profilePages/walletWithdraw/index?cash=${this.cash}`,
      });
    },

    // doGetMemberInfo() {
    //   getMemberInfo().then((res) => {
    //     this.cash = res?.cash;
    //   });
    // },
  },
  onShow() {
    // this.doGetMemberInfo();
    this.cash = this.vMemberInfo.cash;
  },
  onLoad() {
    // this.$u.http.post('waapi/user/getUserInfo').then((res) => {
    //     uni.$u.vuex('vMemberInfo', res)
    //   })
    this.getInfo()
  }
};
</script>
<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.h_sm_text {
  font-size: 25rpx;
  margin-left: 20rpx;

}

.content {

  // .adContainer {
  //   margin-top: 30rpx;
  // }
  padding: 0 30rpx;

  .image {
    width: 100%;
    height: 100%;
    margin-right: 10rpx;
  }

  .centerArea {
    margin-top: 20rpx;
    // border: 2rpx solid red;
    height: 200rpx;
    border-radius: 20rpx;
    // padding: 20rpx;
    overflow: hidden;
    background-image: url('../static/wallet/eaf1b87c277aaade8bd9de19479366e.png');
    background-repeat: no-repeat;
    background-clip: content-box;
    /* 从内容框开始填充背景图片 */
    background-size: 100%;
    /* 从边框开始填充背景图片 */

    background-position: center;
    color: white;
    display: flex;
    justify-content: space-between;

    .h_left {
      margin: 40rpx 20rpx 20rpx 40rpx;

      .h_my {
        display: flex;
        margin-bottom: 30rpx;
        align-items: flex-end;

        .h_img {
          width: 45rpx;
          height: 45rpx;


        }

        .h_b_text {
          // font-weight: bold;
          margin: 0 20rpx;
          font-size: 40rpx;
        }

        .h_c_text {
          color: rgba(248, 248, 248, 0.842);
        }

        .h_m_text {
          font-size: 26rpx;
        }

        .h_m {
          margin-right: 20rpx;
          font-size: 40rpx;
        }
      }
    }

    .h_right {
      font-size: 50rpx;
      display: flex;
      align-items: center;
      margin-right: 40rpx;
      color: rgb(222, 220, 220);
    }

  }

  .buttomArea {
    background-color: white;
    margin-top: 30rpx;
    border-radius: 16rpx;
    padding: 10rpx 0;

    .but_item {
      display: flex;
      height: 80rpx;
      align-items: center;
      padding: 20rpx 30rpx;
    }

    .k_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 30rpx;
    }

    .item_flex {
      display: flex;
    }

    .item_right {
      flex: 1;
      display: flex;
      justify-content: space-between;
    }

    .item_pading {
      padding: 30rpx 0;
    }

    .item_bd {
      // padding:25rpx 0;
      border-bottom: 2rpx solid #d2d1d1;
    }

    .but_text {
      font-size: 40rpx;
      font-weight: bold;
      color: #999;
      display: flex;
      align-items: center;
    }

    .but_m {
      margin-left: 10rpx;
    }
  }
}</style>