<template>
    <!--使用说明页面-->
    <view class="content">
        <BaseTabs :list="navArr" @change="onTabChange" />

        <view class="introduceContent">
            <view class="freeGetPage" v-if="curTabIndex == 0">
                <view class="mainTitle">免费领取第一次<text class="extInst">（免费领取/免费使用同样操作）</text></view>
                <view class="stepContent flexColumnHorzCenter">
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第一步：点击免费领取按钮</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_1-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_1-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第二步：看广告图片，等待出货</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_2-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_2-2.png"></image>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第三步：领取成功</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_3-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_3-2.png"></image>
                    </view>
                </view>

                <view class="mainTitle">免费领取第二次</view>
                <view class="stepContent flexColumnHorzCenter">
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第一步：点击免费领取按钮</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_1-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_1-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第二步：看广告视频，等待出货</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_2-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_2-2.png"></image>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第三步：领取成功</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_3-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_3-2.png"></image>
                    </view>
                </view>
            </view>

            <view class="buyNowPage" v-if="curTabIndex == 1">
                <view class="stepContent flexColumnHorzCenter">
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第一步：扫描机器上的二维码</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_buynow_step_1-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_buynow_step_1-2.png"></image>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第二步：点击立即购买按钮</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_buynow_step_2-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_buynow_step_2-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第三步：确认订单详情，点击去支付</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_buynow_step_3-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_buynow_step_3-2.png"></image>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第四步：选择支付方式，支付订单</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_buynow_step_4-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_buynow_step_4-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef MP-ALIPAY -->
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第四步：领取成功</view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第五步：领取成功</view>
                    </view>
                    <!-- #endif -->

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_3-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_3-2.png"></image>
                    </view>
                </view>
            </view>

            <view class="pointsExchange" v-if="curTabIndex == 2">
                <view class="stepContent flexColumnHorzCenter">
                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第一步：点击积分兑换按钮</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_point_step_1-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_point_step_1-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第二步：确认订单详情，点击确认兑换</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_point_step_2-1.png"></image>
                        <view class="imgRightWrap">
                            <image class="imgRight" src="@/pagesC/static/mock/img_point_step_2-2.png"></image>
                            <image class="imgHand" src="@/pagesC/static/mock/img_freeget_step_1-hand.png"></image>
                        </view>
                    </view>

                    <view class="step">
                        <view class="circle"></view>
                        <view class="stepText">第三步：领取成功</view>
                    </view>

                    <view class="stepImg">
                        <image class="imgLeft" src="@/pagesC/static/mock/img_freeget_step_3-1.png"></image>
                        <image class="imgRight" src="@/pagesC/static/mock/img_freeget_step_3-2.png"></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import BaseTabs from '../../components/base/BaseTabs.vue';
export default {
    components: { BaseTabs },
    name: "index",
    data() {
        return {
            navArr: [
                {
                    name: "免费领取",
                },
                {
                    name: "立即购买",
                },
                {
                    name: "积分兑换",
                },
            ],

            curTabIndex: 0,
        };
    },

    methods: {
        onTabChange(e) {
            this.curTabIndex = e.index;
        },
    },
};
</script>

<style scoped lang="scss">
.content {
    height: 100vh;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .tabBar {
        background-color: white;
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        z-index: 999;
        height: 90rpx;
        box-sizing: border-box;
    }

    .introduceContent {
        flex: 1;
        background-color: $pageBgColor;

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 40rpx;

            .circle {
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background-color: $themeColor;
            }

            .stepText {
                color: $textBlack;
                font-size: $font-size-middle;
                margin-left: 10rpx;
            }
        }

        .stepImg {
            display: flex;
            margin-bottom: 60rpx;

            .imgLeft {
                width: 146rpx;
                height: 260rpx;
            }

            .imgRight {
                margin-left: 4rpx;
                margin-top: 8rpx;
                width: 288rpx;
                height: 150rpx;
            }

            .imgRightWrap {
                margin-left: 4rpx;
                margin-top: 8rpx;
                position: relative;
                height: fit-content;

                .imgRight {
                    width: 288rpx;
                    height: 150rpx;
                }

                .imgHand {
                    width: 103rpx;
                    height: 89rpx;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    transform: translate(20%, 1%);
                }
            }
        }

        .freeGetPage {
            padding: 0 30rpx;
            padding-bottom: 70rpx;

            .mainTitle {
                font-size: $font-size-xlarge;
                color: $textBlack;
                font-weight: bold;
                margin-top: 80rpx;
                margin-bottom: 60rpx;

                .extInst {
                    color: $themeColor;
                    font-size: $font-size-xsmall;
                    margin-left: 6rpx;
                }
            }

            .stepContent {}
        }

        .buyNowPage {
            .stepContent {
                margin-top: 82rpx;
            }
        }

        .pointsExchange {
            .stepContent {
                margin-top: 82rpx;
            }
        }
    }
}
</style>