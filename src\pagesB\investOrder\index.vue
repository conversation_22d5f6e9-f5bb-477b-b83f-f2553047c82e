<template>
    <view>
        <u-sticky>
            <view class="tab">
                <u-subsection :list="tablist" :current="current" mode="subsection" @change="changeTab" />
            </view>
        </u-sticky>

        <ComList :loadingType="loadingType">
            <InvestOrderCard v-for="item in listData" :key="item.id" :info="item" />
        </ComList>
        <LoginPopup />
    </view>
</template>
<script>
import { getMemberInvestList } from "@/common/http/api";

import ComList from "../../components/list/ComList.vue";
import InvestOrderCard from '../components/InvestOrderCard.vue';
import myPull from "@/mixins/myPull"
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: {
        InvestOrderCard, ComList, LoginPopup
    },
    mixins: [myPull()],
    data() {
        return {
            tablist: [
                { name: "待审核", status: 0 },
                { name: "已审核", status: 1 },
                { name: "认养中", status: 2 },
                { name: "认养结束", status: 3 },
            ],
            current: 0,
        };
    },
    methods: {
        getList(page, done) {
            let data = {
                page,
                limit: 10,
                status: this.tablist[this.current]?.status,
            };
            getMemberInvestList(data).then((res) => {
                done(res);
            });
        },
        changeTab(i) {
            this.current = i;
            this.refresh();
        },
    },
    onLoad() {
        this.refresh();
    },

};
</script>
<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style scoped lang="scss">
.tab {
    padding: 20rpx;
    background-color: #fff;
}
</style>
