<template>
  <view>
    <!-- #ifndef H5 -->
    <BaseNavbar
      :isShowSlot="true"
      :bgColor="bgColor || ''"
      @leftClick="onClickHome"
    >
      <view class="nav-left">
        <view class="nav-left-title" @click="nivHome()">{{
          pointPlaceInfo.hotelName
        }}</view>
        <!-- <view class="nav-left-place" style="width:130px">{{ pointPlaceInfo.address }}</view> -->
      </view>
      <view class="nav-center" slot="center"
        >{{ (brandInfo.brandName || "") + (pointPlaceInfo.product_name || "") }}
      </view>
    </BaseNavbar>
    <!-- #endif -->
    <block v-if="deviceInfo.shop_type !== 2">
      <view class="swiper">
        <view class="swiper-bg" :style="{ backgroundColor: bgColor }"></view>
        <view class="swiper-box">
          <swiper
            class="swiper_box"
            :interval="5000"
            autoplay
            :duration="1000"
            :circular="true"
          >
            <!-- #ifdef MP-WEIXIN  -->
            <swiper-item class="swiper-item">
              <!-- 插入广告组件 -->
              <CommonAd :ad="vAd.swiperBannerAd || ''" type="custom" />
            </swiper-item>
            <!--  #endif -->
            <!-- 广告轮播项 -->
            <swiper-item v-for="(item, index) in adInfoList" :key="index">
              <!-- 插入其他内容 -->
              <!-- 这里可以放置您的其他内容，如图片、文字等 -->
              <image :src="item.url" class="image"></image>
            </swiper-item>
          </swiper>
          <view class="device-info">
            <view class="device-info-item">
              <u-icon name="map" size="24rpx" />
              <view class="label">
                {{ pointPlaceInfo.address || "暂无具体地址" }}
              </view>
            </view>
            <view class="device-info-line"></view>
            <view class="device-info-item device-sn">
              <u-icon name="bookmark" size="24rpx" />
              <view class="label">
                设备编号：{{ pointPlaceInfo.device_sn || "暂无设备" }}
              </view>
            </view>
            <view class="device-info-line"></view>
            <view class="device-info-item" v-if="vIsBleDevice">
              <image
                class="icon"
                :src="
                  '/pagesB/static/icon/' +
                  (bBleConnected
                    ? 'ic_ble_connected'
                    : 'ic_ble_not_connected') +
                  '.png'
                "
              />

              <view
                class="label"
                :style="{ color: bBleConnected ? '#0DACE0' : '' }"
              >
                {{ bBleConnected ? "蓝牙已连接" : "蓝牙未连接" }}
              </view>
            </view>
            <view v-else class="device-info-item">
              <image
                class="icon"
                :src="
                  '/pagesB/static/icon/' +
                  (deviceInfo.status === 1
                    ? 'device_online_icon'
                    : 'device_unline_icon') +
                  '.png'
                "
              />
              <view class="label">
                {{ deviceStatus[deviceInfo.status] || "暂无设备" }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="content">
        <view class="menu">
          <MoreMenu
            v-if="pointPlaceInfo.device_sn"
            :deviceSn="deviceInfo.device_sn"
            :miniFoodInfo="miniFoodInfo"
          />
        </view>
        <view class="tabs" v-if="isShowTabList">
          <u-tabs
            :scrollable="false"
            :list="tabList"
            lineColor="#212121"
            :activeStyle="{ color: '#212121', fontWeight: '700' }"
            :inactiveStyle="{ color: '#212121' }"
            @click="tabClick"
          ></u-tabs>
        </view>
        <view class="main">
          <view
            class="goods-area1"
            v-if="currentIndex === 0 && goodList.length == 1 && !isGameDevice"
          >
            <view
              class="goods-item"
              v-for="item in goodList"
              :key="item.goodid"
            >
              <view class="goods-info">
                <image class="goods-info-img" :src="item.goods_img" />
                <view class="goods-info-main">
                  <view class="goods-info-main-title">{{
                    item.goods_name
                  }}</view>
                  <view class="goods-info-main-desc">
                    {{ item.goods_remark }}
                  </view>
                  <view class="goods-info-main-img">
                    <view
                      class="goods-info-main-img-box"
                      v-for="items in item.images"
                      :key="items"
                    >
                      <image class="image" :src="items" />
                    </view>
                  </view>
                </view>
              </view>
              <view class="goods-btn">
                <view
                  v-if="Boolean(freeType)"
                  class="free"
                  @click="onFreeHandle(item, freeType)"
                >
                  <image class="free-img" src="/pagesB/static/goods/free.png" />
                  <view class="free-txt">
                    <block v-if="freeType == 3">
                      {{ item.exchange_integral || 0 }}{{ freeBtnTitle }}
                    </block>
                    <block v-else>
                      {{ freeBtnTitle }}
                    </block>
                  </view>
                </view>
                <block v-if="isGameDevice">
                  <view>
                    <BaseButton
                      :text="`购买 (${item.good_price} 元/ ${item.game_time}分钟)`"
                      color="linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2)"
                      height="66rpx"
                      @onClick="onClickBtnBuyNow(item, 'gameTime')"
                    />
                  </view>
                </block>
                <block v-else>
                  <view>
                    <BaseButton
                      :text="`购买 (${item.good_price})元`"
                      color="linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2)"
                      height="66rpx"
                      @onClick="onClickBtnBuyNow(item, 'openDoor')"
                    />
                  </view>
                </block>
              </view>
              <view class="article">
                <ServiceProtocolAgree
                  @agreeChange="(val) => (isAgreeProtocol = val)"
                />
              </view>
            </view>
          </view>
          <!-- 内容购买区域 -->
          <view class="goods-area posith" v-else-if="currentIndex === 0">
            <block v-if="isGameDevice">
              <view class="posith_btn" @click="goToMy()" v-if="isVipShow">
                <image class="image" src="../static/vip.gif"></image>
              </view>
              <view class="title_center">
                <img
                  src="../static/sport/4.png"
                  alt=""
                  style="
                    width: 12px;
                    height: 12px;
                    vertical-align: middle;
                    margin-right: 5px;
                  "
                />
                <text class="max_size" style="font-weight: 500"
                  >{{
                    (brandInfo.brandName || "") +
                    (pointPlaceInfo.product_name || "")
                  }}
                  {{ vBaseInfo.user_horse || "" }}</text
                >
                <!-- <text class="s_m_g"></text> -->
              </view>
              <view class="game_item" v-for="item in goodList" :key="item.id">
                <GameTimeCard
                  :info="item"
                  @click="onClickBtnBuyNow(item, 'gameTime')"
                ></GameTimeCard>
              </view>
            </block>
            <block v-else>
              <view class="goods-item" v-for="item in goodList" :key="item.id">
                <GoodsCard
                  :info="item"
                  @click="onClickBtnBuyNow(item, 'openDoor')"
                />
              </view>
            </block>
          </view>
          <view class="charge" v-else-if="currentIndex === 1">
            <view class="select-meal">
              <view class="title">选择充电套餐</view>
              <view class="meal-area">
                <RechargeMeal
                  @selectRechargeItem="onSelectRechargeItem"
                  :chargeRule="chargeRule"
                  :isShowKey.sync="isShowKey"
                />
              </view>
              <view class="btnGroup">
                <view class="btn" hover-class="bind_btn">
                  <BaseButton
                    text="立即购买"
                    shape="circle"
                    height="60rpx"
                    width="100%"
                    color="linear-gradient(-81deg, #0c6eb8, #069dcf, #0eade2)"
                    @onClick="onClickBtnBuyNow(null, 'charge')"
                  />
                </view>
              </view>
              <view class="agree">
                <service-protocol-agree
                  @agreeChange="onAgreeChange"
                ></service-protocol-agree>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="game_phone" v-if="vPhone" @click="onClickServerPhone"
        >售后服务热线：<span class="game_phone_line">{{ vPhone }}</span></view
      >
      <view class="logo-box">
        <!-- <image class="logo-img" src="../static/login-ad.png" /> -->
      </view>
      <!-- 
    <CommonAd :ad="vAd.buyProductBannerAd" type="banner" />
    <CommonAd :ad="vAd.homeInsertScreenAd" type="inter" /> 
   -->
      <!-- <CommonAd :ad="vAd.swiperBannerAd" type="custom" /> -->
      <view class="local-video" v-if="isShowLocalVideo">
        <video
          class="video"
          :src="local_video_url"
          play-btn-position="center"
          show-fullscreen-btn="false"
          show-center-play-btn="true"
          autoplay="true"
          @ended="endCocalVideo"
        />
        <view class="close-video" @click="adAlterClose">关闭</view>
      </view>
      <BasePopup
        :show.sync="isShowPopupAdAlter"
        mode="center"
        :safeArea="false"
      >
        <view class="ad_alter" @click="handleAdInfo(ad_index_alter)">
          <image class="img" :src="ad_index_alter.img_url" />
          <view class="close_img" @click.stop="closeAdAlter(ad_index_alter)">
            <BaseIcon name="close" />
          </view>
        </view>
      </BasePopup>
      <tabbar />
      <!-- #ifndef MP-ALIPAY -->
      <SafeBlock height="120" />
      <!-- #endif -->
      <LoginPopup :isNeedAuth="isNeedAuth" :isReloadPage="false" />
      <Floating y="185"></Floating>
      <!-- <view class="serverMenu">

      <button open-type="contact">
        <image class="image" src="../static/icon/phone.gif"></image>
      </button>
    </view> -->
    </block>
    <block v-else>
      <Default
        :deviceInfo="goodList[0]"
        :adInfoList="adInfoList"
        :device_sn="deviceInfo.device_sn"
        :points="points"
        :markers="markers"
        @buy="buy()"
      />
    </block>
  </view>
</template>
<script>
import {
  createFreeOrderAndOut,
  createOrder,
  createOrderAndPrepay,
  getDeviceInfoByMid,
  // isGetFree,
  getDeviceAllInfoByVscode,
  getDeviceAllInfo,
  getShopAndAd,
  setAdLog,
  getRemainCountMini,
  createPointOrder,
  createRechargeOrderAndPrepay,
  getUmDoing,
  createPreOrderAndPrepay,
} from "@/common/http/api"
import { locationMixin } from "@/mixins/locationMixin"
import BaseNavbar from "../../components/base/BaseNavbar.vue"
import MoreMenu from "./components/MoreMenu.vue"
import BaseButton from "../../components/base/BaseButton.vue"
import utils from "@/utils/utils"
import SafeBlock from "../../components/list/SafeBlock.vue"
import BasePopup from "../../components/base/BasePopup.vue"
import BaseIcon from "../../components/base/BaseIcon.vue"
import CommonAd from "../../components/WxAd/CommonAd.vue"
import { adMixin } from "@/mixins/adMixin"
import LoginPopup from "../../components/LoginPopup.vue"
import Tabbar from "./components/Tabbar.vue"
import ServiceProtocolAgree from "./components/ServiceProtocolAgree.vue"
import { globalEvents } from "@/global/globalEvents"
import { globalCodes } from "@/global/globalCodes"
import { bleMixin } from "@/mixins/bleMixin.js"
import GoodsCard from "./components/GoodsCard.vue"
import GameTimeCard from "./components/GameTimeCard.vue"
import RechargeMeal from "./components/RechargeMeal.vue"
import { payOrder } from "@/utils/pay"
import Floating from "./components/Floating.vue"
import Default from "./Default.vue"
const app = getApp()
export default {
  name: "index",
  components: {
    BaseNavbar,
    MoreMenu,
    BaseButton,
    SafeBlock,
    BasePopup,
    BaseIcon,
    CommonAd,
    LoginPopup,
    Tabbar,
    ServiceProtocolAgree,
    GoodsCard,
    GameTimeCard,
    RechargeMeal,
    Floating,
    Default,
  },
  mixins: [locationMixin, adMixin, bleMixin],
  data() {
    return {
      //最后一次点击按钮的时间
      lastOrderTime: 0,
      //是否同意用户协议
      bAgreeProtocol: true,
      //加载框显示的图片和文字
      subOrderTimer: null, //这里类似防抖伐,未连接蓝牙时,免费兑换会创建多次订单,
      isVsCode: false,
      isMid: false,
      isScene: false,
      isGameDevice: false, // 是否是游戏设备
      dataDeviceSn: "",
      dataVsCode: "",
      dataMid: "",
      freeType: "", //显示领取按钮类
      adInfoList: [], //轮播广告
      isMorePage: false, //是否是多页面
      pointPlaceInfo: {}, //点位信息
      deviceInfo: {}, //设备信息
      resInfo: {}, //设备所有信息
      brandInfo: {}, //程序信息

      goodList: [], //商品套餐
      isShowPopupAdAlter: false, //显示自定义插屏广告
      local_video: {}, //本地视频信息
      local_video_url: "", //本地视频地址
      ad_index_alter: {}, //自定义弹窗广告
      isShowLocalVideo: false, //显示本地视频
      selectGoodsInfo: {}, //选择的商品套餐
      isAgreeProtocol: true, //同意协议
      bleDeviceType: ["0x92", "0x81", "0x88", "0x89"], //蓝牙设备
      bBleConnected: false, //蓝牙是否连接
      selectItemInfo: {}, //选择的商品
      subOrderTimer: null, //蓝牙状态改变定时器
      isNowSelectHandle: 0, //当前选择的操作事件 0无 1免费领取 1立即购买
      hotelDeviceList: ["0x81"],
      isNeedAuth: false, // 是否需要授权
      isVipShow: false, //支付悬浮显示
      tabList: [
        {
          name: "商品",
        },
        {
          name: "充电",
        },
      ],
      isShowTabList: false,
      currentIndex: 0,
      chargeRule: {}, //充电规则
      isShowKey: false, //是否显示选择器
      bleTypeHandle: "openDoor", //蓝牙操作类型 openDoor开门  charge 充电
      curSelMeal: {}, //选择的套餐
      deviceStatus: {
        1: "设备在线",
        2: "设备异常",
        3: "设备离线",
      },
      isCurrentPagePay: false, // 是否在当前页支付
      miniFoodInfo: {},
      isHaveCash: false,
      vPhone: "",
      isPerPay: false,
      points: [
        // { latitude: 30.375839, longitude: 114.321272 },
        // { latitude: 30.375839, longitude: 114.32198 },
        // { latitude: 30.375247, longitude: 114.321964 }
      ],
      //#ifdef MP-ALIPAY
      markers: [
        {
          id: 0,
          latitude: 30.37559,
          longitude: 114.32168,
          iconPath: "/static/p.png",
          width: 40,
          height: 50,
        },
      ],
      //#endif
      //#ifdef MP-WEIXIN
      markers: [
        {
          id: 0,
          latitude: 30.37559,
          longitude: 114.32168,
          iconPath: "../static/p.png",
          width: 40,
          height: 50,
        },
      ],
      //#endif
      canUrl: "",
    }
  },
  computed: {
    bgColor() {
      // console.log('颜色吗', this.vBaseInfo?.xcx_custom_theme_color)
      return this.vBaseInfo?.xcx_custom_theme_color ?? "rgba(78, 161, 246,1)"
    },
    // swiperLoading() {
    //   return (this.adInfoList?.length ?? 0) === 0
    // },
    // isHotelDevice() {
    //   return this.hotelDeviceList.includes(this.deviceInfo.deviceType)
    // },
    freeBtnTitle() {
      let ftObj = {
        1: "免 费 领 取",
        2: "看视频免费领取",
        3: "积分兑换",
        4: "看视频免费领取",
      }
      return ftObj[this.freeType]
    },
    text() {
      return this.pointPlaceInfo.is_car_device
        ? "驾驶"
        : this.pointPlaceInfo.is_cannon_device
        ? "运行"
        : "运动"
    },
  },
  methods: {
    //截取域名
    getDomainFromUrl(url) {
      // 使用正则表达式来匹配URL中的域名
      let domainMatch = url.match(/https?:\/\/[^\/]+/)
      return domainMatch ? domainMatch[0] : null
    },
    nivHome() {
      uni.reLaunch({
        url: "/pages/index/index",
      })
    },
    buy(item) {
      console.log("🚀 ~ 创建预售订单", item)
      this.onClickBtnBuyNow(item, "pre")
    },
    onClickServerPhone() {
      console.log("电话号码", this.vPhone)
      uni.makePhoneCall({
        phoneNumber: this.vPhone,
        // 成功回调
        success: (res) => {},
        // 失败回调
        fail: (res) => {},
      })
    },
    selectPayWay(e) {
      if (e?.id == 1) {
        // console.log("钱包余额支付");
        if (this.balance < this.order_amount)
          return uni.showToast({
            title: "余额不足,请选择其他支付方式~",
            icon: "none",
          })
        this.doCreateBuyOrder(this.selectItemInfo)
      } else if (e?.id == 2) {
        // console.log("开始调用支付");
        this.doCreateBuyOrder(this.selectItemInfo)
      }
    },
    goToMy() {
      uni.navigateTo({
        url: `/pagesD/wallet/myWallet?from=buyindex`,
      })
    },

    onFreeHandle(item, ft) {
      if (ft == 1) {
        this.onClickFreeGet(item)
      } else if (ft == 2 || ft == 4) {
        this.onClickVideoAd(item)
      } else if (ft == 3) {
        this.onClickBtnBuyNow(item)
      }
    },
    onSelectRechargeItem(e) {
      this.curSelMeal = e.item

      // console.log('🚀 ~ this.curSelMeal ', this.curSelMeal)
    },
    tabClick({ index }) {
      this.currentIndex = index
      // console.log('🚀 ~ i', index)
    },
    //本地视频广告播放结束
    endCocalVideo() {
      // console.log('本地视频广告播放完成...')
      this.isShowLocalVideo = false
      this.setAdLogHandle(this.local_video, 80, 2)
      this.doCreateFreeOrder(this.selectGoodsInfo, true)
    },
    //关闭本地视频广告
    adAlterClose() {
      uni.showModal({
        title: "提示",
        content: "暂未获得奖励 是否继续观看视频",
        showCancel: true,
        cancelText: "放弃",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
          } else if (res.cancel) {
            this.isShowLocalVideo = false
          }
        },
      })
    },
    //关闭自定义弹窗广告
    closeAdAlter(item) {
      if (Math.random() < 0.7) this.handleAdInfo(item)
      this.isShowPopupAdAlter = false
    },
    handleAdInfo(item) {
      if (item.file_type == 2) return //视频广告不跳转
      //小程序跳转广告
      if (item.wx_mp_appid && item.wx_mp_path)
        return wx.navigateToMiniProgram({
          appId: item.wx_mp_appid,
          path: item.wx_mp_path,
          success: (result) => {
            // console.log('🚀 ~ result', result)
          },
        })
      //链接跳转
      let linkHref = item.img_href
      // console.log('🚀 ~ linkHref', linkHref)

      if (!linkHref || linkHref == "#") return

      linkHref && this.goToWebView(linkHref)
    },
    handleUmModelStatus() {
      // 设备模式状态 ： 在充电，在游戏。 条件 用户，设备
      let params = {
        device_sn: this.deviceInfo.device_sn,
      }
      getUmDoing(params).then((res) => {
        // console.log('设备状态 res', res)
        let umModelStatus = res
        console.log("设备状态", umModelStatus)
        if (
          umModelStatus &&
          umModelStatus.model_status == globalCodes.umModelStatus.CHARGING
        ) {
          // 在充电
          uni.navigateTo({
            url: `/pagesB/chargeDetails/index?charge_sn=${umModelStatus.charge_sn}`,
          })
        }
        if (
          umModelStatus &&
          umModelStatus.model_status == globalCodes.umModelStatus.GAMING
        ) {
          // 在游戏
          if (this.deviceInfo.shop_type == 2) {
            uni.navigateTo({
              url: `/pagesB/product/perGame?order_sn=${umModelStatus.order_sn}`,
            })
          } else {
            uni.navigateTo({
              url: `/pagesB/chargeDetails/gaming?order_sn=${umModelStatus.order_sn}`,
            })
          }
        }
      })
    },
    onAgreeChange(agree) {
      this.bAgreeProtocol = agree
      // console.log('是否同意服务协议：', this.bAgreeProtocol)
    },
    //激励视频播放完成回调
    onRewardAdEndCallbck() {
      // console.log('激励视频广告播放完成...')
      this.doCreateFreeOrder(this.selectGoodsInfo, true)
    },
    //2s内不能重复下单
    checkRepeatOrder(isCountDistance) {
      if (!this.bAgreeProtocol) {
        this.isShowErr("请先同意《服务协议》")
        return false
      }
      let timestamp = new Date().valueOf()
      if (timestamp - this.lastOrderTime < 2000) {
        this.isShowErr("您操作太频繁了")
        this.lastOrderTime = timestamp
        return false
      }
      //是蓝牙设备
      if (this.vIsBleDevice) {
        if (!this.bBleConnected) {
          this.startBleConnect()
          return false
        }
      }

      //是计算距离
      if (isCountDistance) {
        // console.log('🚀 ~ this.vCurLocation', this.vCurLocation)
        let distances = this.distanceLength(
          this.vCurLocation.latitude,
          this.vCurLocation.longitude,
          this.pointPlaceInfo.lat,
          this.pointPlaceInfo.lon
        )

        // console.log(
        //   '🚀 ~ distances 与设备之间的距离',
        //   parseFloat(distances),
        //   '公里',
        // )
        // console.log(
        //   '🚀 ~ this.resInfo.max_distance  最大距离（米）',
        //   parseFloat(this.resInfo.max_distance),
        // )
        if (
          parseFloat(distances * 1000) >
          parseFloat(this.resInfo.max_distance || 500)
        ) {
          uni.showModal({
            title: "提示",
            content: "您距离设备太远了，请在设备附近操作~",
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
              } else if (res.cancel) {
                console.log("用户点击取消")
              }
            },
          })
          return false
        }
      }

      this.lastOrderTime = timestamp
      return true
    },
    //立即购买
    onClickBtnBuyNow(item, type = "openDoor") {
      this.bleTypeHandle = type
      this.isNowSelectHandle = 2
      this.selectItemInfo = item
      if (!this.checkRepeatOrder(false)) return
      this.doCreateBuyOrder(item)
    },
    //点击了免费领取按钮
    onClickFreeGet(item) {
      this.isNowSelectHandle = 1
      this.selectItemInfo = item
      if (!this.checkRepeatOrder(true)) return

      // console.log('🚀 ~  this.selectItemInfo ', this.selectItemInfo)
      this.doCreateFreeOrder(item)
    },
    //看视频免费领取
    onClickVideoAd(item) {
      if (!this.checkRepeatOrder(true)) return
      this.selectGoodsInfo = item
      getRemainCountMini({
        isVideo: true,
      })
        .then((res) => {
          if (res) {
            // if (this.local_video_url) {
            //     this.setAdLogHandle(this.local_video, 80, 1)
            //     return this.isShowLocalVideo = true;
            // }
            this.showRewardAd()
          } else {
            uni.showModal({
              title: "提示",
              content: "亲,暂时没有视频资源,请直接购买啦",
              showCancel: false,
            })
          }
        })
        .catch((err) => {
          uni.showModal({
            title: "提示",
            content: "亲,暂时没有视频资源,请直接购买啦",
            showCancel: false,
          })
        })
    },
    //点击了积分兑换
    onClickBtnPointExchange(item) {
      if (!this.checkRepeatOrder(true)) return
      this.selectItemInfo = item
      let params = {
        device_sn: this.deviceInfo.device_sn,
        channel: item.num,
        goods_id: item.goodid,
        num: 1,
      }
      createPointOrder(params).then((res) => {
        this.handlePayOrder(res, item, globalCodes.getWay.POINT_EXCHANGE)
      })
    },
    //处理跳转到确认订单页面
    handlePayOrder(info, item, getWay) {
      let orderInfo, payInfo, curSelMeal
      if (this.bleTypeHandle === "openDoor") {
        orderInfo = info
        curSelMeal = item
      } else if (this.bleTypeHandle === "gameTime") {
        if (this.isCurrentPagePay && item.good_price > 0) {
          orderInfo = info?.order_info
          payInfo = info?.prepay_info
        } else {
          orderInfo = info
        }
        curSelMeal = item
      } else if (this.bleTypeHandle == "pre") {
        if (item.good_price > 0) {
          orderInfo = info?.order_info
          payInfo = info?.prepay_info
        }
        curSelMeal = item
      } else {
        orderInfo = info?.order_info
        payInfo = info?.prepay_info
        curSelMeal = this.curSelMeal
      }
      uni.$u.vuex("vCreateOrderInfo", {
        orderInfo,
        payInfo,
        pointPlaceInfo: this.pointPlaceInfo,
        curSelMeal,
        bleTypeHandle: this.bleTypeHandle, //操作什么类型
      })

      // 游戏订单直接在当前界面购买 且用户是付费会员，有余额
      // console.log('item',this.vMemberInfo.is_paid_member)
      if (this.bleTypeHandle === "gameTime" && this.isCurrentPagePay) {
        if (item.good_price > 0) {
          // console.log('调起支付')
          payOrder(
            this,
            payInfo,
            `/pagesB/order/OrderDetails?order_sn=${orderInfo?.order_sn}`,
            null,
            false
          )
        } else {
          //跳转到订单详情页面
          uni.navigateTo({
            url: `/pagesB/order/OrderDetails?order_sn=${orderInfo?.order_sn}`,
          })
        }
      } else if (this.bleTypeHandle == "pre") {
        if (item.good_price > 0) {
          console.log("调起支付", item.good_price)
          payOrder(
            this,
            payInfo,
            `/pagesB/order/OrderDetails?order_sn=${orderInfo?.order_sn}`,
            null,
            false
          )
        } else {
          //跳转到订单详情页面
          uni.navigateTo({
            url: `/pagesB/order/orderDetails?order_sn=${orderInfo?.order_sn}`,
          })
        }
      } else {
        //跳转到支付订单页面
        uni.navigateTo({
          url: `/pagesB/product/ConfirmOrder?getWay=${getWay}`,
        })
      }
    },
    //API 创建免费订单
    doCreateFreeOrder(
      item,
      isVideo = false,
      prom_type = globalCodes.commonOrderType.ORDER_COMMON_RATE_BACK
    ) {
      // console.log('🚀 ~ item', item)
      // console.log('这里开始创建免费订单')
      let params = {
        device_sn: this.deviceInfo.device_sn,
        channel: item.num,
        goods_id: item.goodid,
        num: 1,
        isVideo,
        isRedirectOut: !this.vIsBleDevice, //false蓝牙出货，true后台出货
        prom_type,
      }
      createFreeOrderAndOut(params).then((res) => {
        uni.$u.vuex("vCreateOrderInfo", {
          orderInfo: res,
        })
        if (this.vIsBleDevice) {
          this.openBleLock(false, item?.num)
        } else {
          //跳转到订单详情页面
          uni.navigateTo({
            url: `/pagesB/order/OrderDetails?order_sn=${res.order_sn}`,
          })
        }
      })
    },
    //API创建立即购买订单
    async doCreateBuyOrder(item) {
      let res, params
      if (this.bleTypeHandle === "openDoor") {
        //创建订单
        params = {
          device_sn: item.device_sn,
          channel: item.num || "",
          goods_id: item.goodid || "",
          num: 1,
          isRedirectOut: !this.vIsBleDevice, //false蓝牙出货，true后台出货
        }
        res = await createOrder(params)
      } else if (this.bleTypeHandle === "charge") {
        params = {
          device_sn: this.deviceInfo.device_sn,
          amount: parseFloat(this.curSelMeal.price),
          duration: this.curSelMeal.hour,
        }
        res = await createRechargeOrderAndPrepay(params)
      } else if (this.bleTypeHandle === "gameTime") {
        // 游戏创建订单并支付
        //创建订单
        params = {
          device_sn: item.device_sn,
          channel: item.num || "",
          goods_id: item.goodid || "",
          num: 1,
          // isRedirectOut: !this.vIsBleDevice, //false蓝牙出货，true后台出货
        }

        /* 判断是否当前页支付 */
        this.isCurrentPagePay = this.vMemberInfo.is_paid_member != 1
        // console.log('是否在当前页支付', this.isCurrentPagePay)
        if (item.good_price <= 0) {
          params["isRedirectOut"] = !this.vIsBleDevice
          params["prom_type"] = globalCodes.commonOrderType.ORDER_COMMON_FREE
          res = await createFreeOrderAndOut(params)
        } else {
          if (this.vMemberInfo?.is_paid_member) {
            // 是会员
            res = await createOrder(params)
          } else {
            //不是会员
            // console.log('不是会员',this.vMemberInfo)
            res = await createOrderAndPrepay(params)
          }
        }
      } else if (this.bleTypeHandle === "pre") {
        params = {
          device_sn: item.device_sn,
          channel: item.num || "",
          goods_id: item.goodid || "",
          num: 1,
        }
        res = await createPreOrderAndPrepay(params)
      } else {
        return
      }
      // console.log('🚀 ~ res', res)
      res && this.handlePayOrder(res, item, globalCodes.getWay.PAY_MONEY)
    },
    onClickHome() {
      // if (this.isMorePage) {
      //     uni.navigateBack()
      // } else {
      //     uni.redirectTo({
      //         url: "/pages/index/index",
      //     });
      // }
    },
    // //API 根据虚拟码获取设备信息
    doGetDeviceAllInfoByVscode(device_code) {
      let params = {
        device_code,
      }
      getDeviceAllInfoByVscode(params)
        .then((res) => {
          this.handleRes(res)
        })
        .catch((err) => {
          console.log("err", err)
        })
    },
    // //API 根据mid获取设备信息
    doGetDeviceInfoByMid(mid, device_sn) {
      let params = {
        mid,
        device_sn,
      }
      getDeviceInfoByMid(params).then((res) => {
        this.handleRes(res)
      })
    },
    // //API 根据设备编号获取设备信息
    doGetDeviceAllInfo(device_sn) {
      let params = {
        device_sn,
      }
      getDeviceAllInfo(params).then((res) => {
        this.handleRes(res)
      })
    },
    //统一处理返回数据
    handleRes(res) {
      if (res.code == 0) {
        return uni.navigateBack()
      }

      this.resInfo = res
      this.deviceInfo = res?.device_info
      this.brandInfo = res?.brand_info
      this.isGameDevice = res?.device_info?.is_game_device
      this.vPhone = res?.hotel_info?.service_phone
      if (
        res.device_info.shop_type == 2 &&
        res.hotel_info.p_info &&
        res.hotel_info.p_info.length > 0
      ) {
        console.log("没有进来")
        this.markers[0].latitude = res.hotel_info.p_info[0].latitude * 1
        this.markers[0].longitude = res.hotel_info.p_info[0].longitude * 1
        // this.markers=[...this.markers[0],...marske]
      }
      if (res?.hotel_info?.geo_info) {
        this.points = res?.hotel_info?.geo_info.map((item) => {
          return {
            longitude: item.longitude * 1,
            latitude: item.latitude * 1,
          }
        })
      }

      // console.log('是否是游戏设备 ： ', this.isGameDevice)

      //获取点位信息
      this.pointPlaceInfo = { ...res.hotel_info, ...res.device_info }
      this.isVipShow = res.hotel_info && res.hotel_info.is_show_recharge == 1
      // "19000498" ||  "0x92" ||
      let resDeviceSn = res?.device_info?.device_sn
      let resDeviceType = res?.device_info?.deviceType

      this.chargeRule = res?.charge_rule //充电规则
      this.isNeedAuth = res?.isNeedAuth // 是否需要授权

      uni.$u.vuex("vServicePhone", res?.brand_info?.phone)
      // uni.$u.vuex('vAppName', res?.brand_info?.brandName)
      uni.$u.vuex("vDeviceSn", resDeviceSn)
      uni.$u.vuex("vDeviceType", resDeviceType)
      uni.$u.vuex("vPointInfo", this.pointPlaceInfo)
      app.globalData.wifiInfo = {
        wifi_name: res.data?.hotel_info?.wifi_name,
        wifi_psd: res.data?.hotel_info?.wifi_psd,
      }
      app.globalData.device_type = res?.device_info?.deviceType
      app.globalData.device_sn = res?.device_info?.device_sn
      //如果没有定位直接获取定位
      if (!this.vCurLocation?.longitude || !this.vCurLocation?.latitude) {
        this.getLocPermission() //调用获取位置信息
      }
      this.isDeviceSn(resDeviceType, resDeviceSn) //是否是蓝牙设备
      this.handleAd() //获取广告信息
      // 是否登录了，则检测是否该用户在改设备上有时长订单
      if (this.vIsLogin) {
        console.log(
          "游戏设备，或者是充电设备",
          this.isGameDevice || this.isChargeDevice
        )
        if (this.isGameDevice || this.isChargeDevice) {
          // 游戏设备，或者是充电设备

          this.handleUmModelStatus()
        }
      }
    },
    //处理广告信息
    handleAd() {
      let params = {
        device_sn: this.pointPlaceInfo.device_sn,
      }
      getShopAndAd(params).then((res) => {
        this.adInfoList = res?.ads || []
        // let arr = res.goods || []
        // 提取推荐商品和非推荐商品
        let recommendedGoods = res.goods.filter(
          (item) => item.is_recommend === 1
        )
        let nonRecommendedGoods = res.goods.filter(
          (item) => item.is_recommend !== 1
        )
        // 对非推荐商品按 game_time 排序
        nonRecommendedGoods.sort((a, b) => a.game_time - b.game_time)

        // 合并推荐商品和非推荐商品
        let sortedGoods = recommendedGoods.concat(nonRecommendedGoods)
        // arr = arr.sort((a, b) => a.game_time - b.game_time)
        // let sum = res.goods.length === 0 ? 0 : res.goods.reduce((a, b) =>
        //   a.game_time > b.game_time - b ? a.game_time : b.game_time,
        // )
        // 计算 game_time 的总和（假设 game_time 是数值）
        let sum = sortedGoods.reduce((total, item) => total + item.game_time, 0)
        this.goodList = sortedGoods || []
        sortedGoods.forEach((item) => {
          this.goodList.forEach((v) => {
            if (v.game_time == sum) {
              v.count = 100
            } else if (item.game_time == v.game_time) {
              v.count = Math.ceil((v.game_time / sum) * 100)
            }
          })
        })
        this.adInfoList = this.adInfoList?.map((el) => {
          el.url = el.img_url
          return el
        })
        this.chargeRule && (this.currentIndex = 1)
        this.goodList?.length && (this.currentIndex = 0)
        if (this.goodList?.length && this.chargeRule) {
          this.isShowTabList = true
        }
        this.local_video = res?.ad_city_video
        this.local_video_url = res?.ad_city_video?.img_url //本地视频地址
        // console.log('🚀 ~ this.local_video_url', this.local_video_url)
        this.ad_index_alter = res?.ad_index_alter //自定义弹窗广告
        if (this.ad_index_alter?.img_url) this.isShowPopupAdAlter = true

        let ad_city_spread = res?.ad_city_spread //置剪辑板广告
        if (ad_city_spread?.content_text) {
          uni.setClipboardData({
            data: ad_city_spread?.content_text,
            success: (res) => {
              this.setAdLogHandle(ad_city_spread, 90, 1)
              // console.log('🚀 ~ 置剪辑板成功', res)
            },
            fail: (err) => {
              // console.log('🚀 ~ 置剪辑板失败', err)
            },
          })
        }
      })
    },
    //是否是蓝牙设备
    isDeviceSn(type, device_sn) {
      getApp().globalData.device_sn = device_sn
      getApp().globalData.device_type = type

      let isBleSn = this.bleDeviceType.includes(type)
      uni.$u.vuex("vIsBleDevice", isBleSn)
      if (isBleSn) {
        this.bBleConnected = getApp().globalData.connected
        if (!this.bBleConnected) {
          this.startBleConnect()
        }
      }
    },
    //添加广告展示记录
    setAdLogHandle(item, ad_type, active) {
      let data = {
        device_sn: this.deviceInfo.device_sn,
        ad_type, //80=》本地视频，90=》同城推广
        active,
        ad_id: item.aid,
      }
      setAdLog(data).then((res) => {
        // console.log('🚀 ~ 广告统计 res', res)
      })
    },
    // 计算两点的距离
    distanceLength(la1, lo1, la2, lo2) {
      let La1 = (la1 * Math.PI) / 180.0
      let La2 = (la2 * Math.PI) / 180.0
      let La3 = La1 - La2
      let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0
      let s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(La3 / 2), 2) +
              Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
          )
        )
      s = s * 6378.137
      s = Math.round(s * 10000) / 10000
      s = s.toFixed(2)
      return s
    },
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      if (this.vPositioning) {
        this.initLocPermission(() => {
          this.getCurrentLocation(() => {})
        })
      }

      //#endif
      // #ifdef MP-ALIPAY
      if (this.vPositioning) {
        this.getCurrentLocation(() => {})
      }
      //#endif
    },
    clickSwiper(i) {
      this.handleAdInfo(this.adInfoList[i])
    },
  },
  onLoad(opt) {
    /* #ifdef MP-ALIPAY */
    if (my.canIUse("hideBackHome")) {
      my.hideBackHome()
    }
    if (my.canIUse("getLeftButtonsBoundingClientRect")) {
      var { backButtonIcon, backButtonInteractive, homeButtonIcon } =
        my.getLeftButtonsBoundingClientRect()
    }
    /* #endif */
    //没有数据就回到首页
    this.isMorePage = getCurrentPages()?.length >= 2
    // console.log('🚀 ~ this.isMorePage', this.isMorePage)

    console.log("opt-------------------------------", opt)
    if (opt) {
      //判断领取类型
      this.freeType = opt?.ft || ""
      // console.log('opt.q', opt.q)
      // console.log('opt.scanCodeStatus', opt.scanCodeStatus)
      // console.log(
      //   'getApp().globalData.aliScanCode',
      //   getApp().globalData.aliScanCode,
      // )
      //根据二维码的url修改请求域名

      if (opt.q || getApp().globalData.aliScanCode) {
        //扫描虚拟码进入
        let scanCodeUrl = undefined
        /* #ifdef MP-WEIXIN */
        scanCodeUrl = decodeURIComponent(opt.q)
        /* #endif */
        /* #ifdef MP-ALIPAY */
        //如果是支付宝扫普通二维码
        scanCodeUrl = decodeURIComponent(getApp().globalData.aliScanCode)
        getApp().globalData.aliScanCode = ""
        /* #endif */
        this.canUrl = scanCodeUrl

        //https://xh.xhwxc.com/mini/index?vscode=070816000937171
        // getApp().globalData.baseURL = utils.getUrlHttp(scanCodeUrl);
        // console.log("getApp().globalData.baseURL", getApp().globalData.baseURL);
        this.dataVsCode = utils.getUrlParams(scanCodeUrl, "vscode")
        this.dataMid = utils.getUrlDynamicData(scanCodeUrl, "mid")
        this.dataDeviceSn = utils.getUrlDynamicData(scanCodeUrl, "device_sn")
        this.freeType =
          utils.getUrlParams(scanCodeUrl, "ft") ||
          utils.getUrlDynamicData(scanCodeUrl, "ft") ||
          ""
        if (this.dataVsCode) {
          this.isVsCode = true
          // console.log('扫码进入获取设备虚拟码vscode', this.dataVsCode)
        } else if (this.dataMid) {
          this.isMid = true
          // console.log('扫码进入获取设备mid', this.dataMid)
        } else if (this.dataDeviceSn) {
          this.isScene = true
        }
      } else if (opt.scene || opt.name) {
        //扫描小程序码进入
        if (opt.name) {
          this.dataDeviceSn = opt.name
        } else {
          let scanAppletCode = decodeURIComponent(opt.scene)
          this.dataDeviceSn = utils.getUrlParams("?" + scanAppletCode, "name")
          this.freeType = utils.getUrlParams("?" + scanAppletCode, "ft")
          // console.log("🚀 ~ 扫描小程序码获取得ft  freeType", this.freeType);
        }
        this.isScene = this.dataDeviceSn ? true : false
        // console.log('扫描小程序码获取得设备编号', this.dataDeviceSn)
      } else if (opt.mid) {
        //首页扫码获取mid进入
        this.dataMid = opt.mid
        this.isMid = this.dataMid ? true : false
      } else if (opt.vscode) {
        //首页扫码获取 vscode虚拟码进入
        this.dataVsCode = opt.vscode
        this.isVsCode = this.dataVsCode ? true : false
      } else if (opt.device_sn) {
        this.dataDeviceSn = opt.device_sn
        this.isScene = this.dataDeviceSn ? true : false
      } else if (opt.store_id && opt.tableid) {
        this.miniFoodInfo.store_id = opt.store_id
        this.miniFoodInfo.tableid = opt.tableid
      }
      if (opt.url) {
        this.canUrl = opt.url
      }
      if (this.canUrl) {
        let domain = this.getDomainFromUrl(this.canUrl)
        // 初始化请求配置
        uni.$u.http.setConfig((config) => {
          /* config 为默认全局配置*/
          config.baseURL = domain /* 根域名 */
          // 注：如果局部custom与全局custom有同名属性，则后面的属性会覆盖前面的属性，相当于Object.assign(全局，局部)
          config.custom = {
            isShowLoading: false,
            lodingTitle: "加载中",
          } // 全局自定义参数默认值
          return config
        })
        console.log("域名:", domain)
      }
      //判断目前是从哪里进入页面得

      if (this.isVsCode) {
        //根据虚拟码获取设备信息
        // console.log('根据虚拟码获取设备信息vscode', this.dataVsCode)
        this.doGetDeviceAllInfoByVscode(this.dataVsCode)
      } else if (this.isMid) {
        //根据mid获取设备信息等
        // console.log('根据mid获取设备信息mid', this.dataMid)
        this.doGetDeviceInfoByMid(this.dataMid)
      } else if (this.isScene) {
        //根据设备编号获取设备信息
        // console.log('根据设备编号获取设备信息deviceSn', this.dataDeviceSn)
        this.doGetDeviceAllInfo(this.dataDeviceSn)
      } else {
        //没有数据就回到首页
        uni.redirectTo({ url: "/pages/index/index?scanCodeStatus=0" })
        return
      }
    }
    // let params = {
    //   device_sn: this.deviceInfo.device_sn || "",
    // };
    // isGetFree(params).then((res) => {});
    //监测蓝牙连接成功事件
    uni.$off(globalEvents.EVENT_BLE_CONNECT_CHANGED)
    uni.$on(globalEvents.EVENT_BLE_CONNECT_CHANGED, (e) => {
      // console.log('home，收到蓝牙连接状态改变事件，是否连接：', e.connected)
      getApp().globalData.connected = e.connected
      if (this.subOrderTimer) clearInterval(this.subOrderTimer)
      this.subOrderTimer = setTimeout(() => {
        this.bBleConnected = e.connected
        if (e.connected) {
          if (this.isNowSelectHandle === 1) {
            this.isNowSelectHandle = 0
            //创建免费订单
            // console.log('我在蓝牙后创建了  免费订单')
            // console.log('🚀 ~ this.selectItemInfo', this.selectItemInfo)
            this.doCreateFreeOrder(this.selectItemInfo)
          } else if (this.isNowSelectHandle === 2) {
            this.isNowSelectHandle = 0
            // console.log('我在蓝牙后创建了  普通订单')
            this.doCreateBuyOrder(this.selectItemInfo)
          }
          this.isNowSelectHandle = 0
        }
      }, 500)
    })
  },
}
</script>
<style lang="scss">
page {
  background-color: #fff;
}
</style>
<style scoped lang="scss">
.pre_sale {
  width: 100%;
}

.game_phone {
  margin-top: 20rpx;
  font-size: 50rpx;
  color: red;
  text-align: center;
  font-weight: bold;
  &_line {
    text-decoration: underline;
  }
}

.max_size {
  // display: block;
  width: 100%;
  font-size: 45rpx;
  color: #007aff;
}

.title_center {
  width: 100%;
  display: flex;
  align-items: center;
}

.s_m_g {
  font-size: 25rpx;
  color: #9a9a9a;
  margin-left: 20rpx;
}

.posith {
  position: relative;

  .posith_btn {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    right: 0;
    top: -100rpx;
    z-index: 999;
  }
}

.image {
  // border: 1px solid #000;

  width: 100%;
  height: 100%;
}

.swiper_box {
  /* #ifdef MP-WEIXIN  */
  height: 250rpx;
  /* #endif */
  /* #ifdef MP-ALIPAY */
  height: 250rpx;

  /* #endif */
  .swiper-item {
    // background-color:white;
    // background-color:red;
    height: auto;
  }

  ::v-deep .common {
    padding: 0 !important;
  }

  image {
    width: 100%;
    height: 100%;
  }
}

// .pay_type {
//   padding: 50rpx 0 40rpx 30rpx;

//   .pay_item {
//     display: flex;
//     margin: 20rpx 0;
//     // border: 2rpx solid #9a9a9a;

//   }

//   .pay_img {
//     width: 40rpx;
//     height: 40rpx;
//     margin-right: 10rpx;
//   }

// }

.nav-left {
  color: #fff;

  &-title {
    font-size: 20rpx;
  }

  &-place {
    margin-top: 8rpx;
    font-size: $font-size-xsmall;
  }
}

.nav-left-title {
  width: 230rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  display: -webkit-box;
}

.nav-center {
  color: #fff;
  font-size: 35rpx;
}

.swiper {
  position: relative;
  padding: 10rpx 24rpx 0;
  background-color: #fff;

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 220rpx;
    border-radius: 0 0 24rpx 24rpx;
  }

  &-box {
    border-radius: 24rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);

    .device-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18rpx 25rpx;

      &-item {
        display: flex;
        align-items: center;

        &:nth-child(3) {
          flex-shrink: 0;
        }

        &:nth-child(5) {
          flex-shrink: 0;
        }

        .icon {
          flex-shrink: 0;
          width: 24rpx;
          height: 24rpx;
          box-sizing: border-box;
        }

        .label {
          font-size: 18rpx;
          margin-left: 8rpx;
          color: #666;
        }
      }

      &-line {
        width: 2rpx;
        height: 30rpx;
        margin: 0 10rpx;
        background-color: #989898;
      }
    }
  }
}

.content {
  padding: 0 24rpx;
  margin-top: 20rpx;

  .menu {
    padding: 20rpx 0;
    border-radius: 24rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
  }

  .main {
    width: 100%;
    margin-top: 18rpx;
    border-radius: 24rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
    // z-index: 999;
    height: 100%;
    box-sizing: border-box;
    padding: 15rpx 20rpx 20rpx 20rpx;

    .goods-area {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .goods-item {
        flex: 0 0 48%;

        &:nth-child(n + 3) {
          margin-top: 58rpx;
        }
      }

      .game_item {
        flex: 0 0 100%;
        margin-top: 20rpx;
        // &:nth-child(n + 3) {
        //   margin-top: 58rpx;
        // }
      }
    }

    .charge {
      .select-meal {
        width: 100%;
        border-radius: 20rpx;
        padding: 24rpx;
        background-color: #fff;
        box-sizing: border-box;
        box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.2);

        .title {
          color: #333;
          font-size: 30rpx;
          font-weight: 700;
          margin-bottom: 20rpx;
        }

        .btnGroup {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-top: 40rpx;

          .btnFreeGet {
            width: 100%;
            height: 120rpx;
            transition: all 0.3s ease;
          }

          .btn {
            width: 100%;

            transition: all 0.3s ease;
          }

          .bind_btn {
            opacity: 0.4;
            scale: 1.1;
          }
        }

        .agree {
          margin-top: 20rpx;
        }
      }
    }
  }

  .tabs {
    margin-top: 30rpx;
    border-radius: 24rpx;
    background-color: #f6e9fe;
  }
}

.serverMenu {
  // border: 1px solid #000;
  position: fixed;
  bottom: 150rpx;
  left: 50rpx;
  height: 100rpx;
  width: 100rpx;
  z-index: 999;
  // background-color: red;

  button {
    // border: 2rpx solid rgb(189, 189, 189);
    border-radius: 50%;
    // background-color: red;
    width: 100%;
    height: 100%;
  }
}

.goods-area1 {
  width: 100%;
  box-sizing: border-box;

  .goods-item {
    padding: 44rpx 56rpx 30rpx;
    border-radius: 20rpx;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
    background-color: #fff;

    &:nth-child(n + 2) {
      margin-top: 30rpx;
    }

    .goods-info {
      display: flex;

      &-img {
        flex-shrink: 0;
        // width: 290rpx;
        // height: 290rpx;
        width: 250rpx;
        height: 250rpx;
        border-radius: 20rpx;
      }

      &-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 20rpx;
        box-sizing: border-box;

        &-title {
          flex-shrink: 0;
          font-size: 36rpx;
          font-weight: 700;
          color: #333333;
          @include textMaxOneLine();
        }

        &-desc {
          margin-top: 26rpx;
          font-size: 20rpx;
          color: #333333;
          line-height: 1.5;
          @include textMaxTwoLine();
        }

        &-price {
          font-size: 30rpx;
          color: #666;
          @include flexRowBetween();
        }

        &-img {
          display: flex;
          justify-content: space-between;
          flex-shrink: 0;
          overflow: hidden;

          &-box {
            display: flex;
            width: 30%;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            image {
              width: 96rpx;
              height: 96rpx;
              border-radius: 50%;
            }

            view {
              margin-top: 16rpx;
              font-size: 20rpx;
              color: #333;
            }

            &:nth-child(n + 2) {
              margin-left: auto;
            }
          }
        }
      }
    }

    .goods-btn {
      > view {
        width: 582rpx;
        height: 66rpx;
        margin: 0 auto;
        margin-top: 30rpx;

        &:nth-child(2) {
          margin-top: 12rpx;
        }
      }

      .free {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 85rpx;

        &-img {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          width: 100%;
          height: 100%;
        }

        &-txt {
          position: relative;
          color: #fff;
          font-size: 32rpx;
          font-weight: 700;
          transform: translateY(-12rpx);
          z-index: 10;
        }
      }
    }

    .article {
      margin-top: 30rpx;
      margin-left: -40rpx;
    }
  }
}

.logo-box {
  margin: 30rpx auto 0;
  width: 368rpx;

  .logo-img {
    width: 100%;
    height: 95rpx;
  }
}

.ad_alter {
  position: relative;

  .close_img {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
  }
}

.local-video {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;

  .video {
    width: 100%;
    height: 100%;
  }

  .close-video {
    position: absolute;
    right: 20rpx;
    top: 200rpx;
    padding: 10rpx 30rpx;
    border-radius: 20rpx;
    color: #fff;
    background-color: #00b3ab;
    color: #fff;
    font-size: 24rpx;
    font-weight: 700;
  }
}
</style>
