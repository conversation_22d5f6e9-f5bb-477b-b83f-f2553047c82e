<template>
    <view class="card" @click="go">
        <view class="content">
            <view class="content-box">
                <view class="content-box-img" @click.stop="previewImage(info.url)">
                    <image class="img" :src="info.url" />
                </view>
                <view class="content-box-right">
                    <view class="content-box-right-box">
                        <view>设备编号：</view>
                        <view>{{ info.device_sn }}</view>
                    </view>
                    <view class="content-box-right-box">
                        <view>订单编号：</view>
                        <view>{{ info.order_sn }}</view>
                    </view>
                    <view class="content-box-right-box">
                        <view>广告类型：</view>
                        <view>{{ typeName }}</view>
                    </view>
                    <view class="content-box-right-box">
                        <view>广告链接：</view>
                        <view class="link" @click.stop="gotoWebView(info.url_link)">{{ info.url_link }}</view>
                    </view>
                    <view class="content-box-right-box">
                        <view>广告总价：</view>
                        <view>{{ info.amount.toFixed(2) }}</view>
                    </view>
                    <view class="content-box-right-box">
                        <view>添加时间：</view>
                        <view>{{
                                $u.timeFormat(info.add_time * 1000, "yyyy-mm-dd hh:MM:ss") ||
                                $u.timeFormat(info.add_time * 1000, "yyyy-mm-dd hh:MM:ss")
                        }}</view>
                    </view>
                </view>
            </view>
            <view class="bottom">
                <view class="bottom-status">
                    <view>状态：</view>
                    <view class="status">{{ statusData }}</view>
                </view>
                <view class="bottom-details">查看详情</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "AdvertiseOrderCard",
    props: { info: { type: Object, default: {} } },
    computed: {
        typeName() {
            if (this.info.type == 2) {
                return "插屏广告";
            } else if (this.info.type == 4) {
                return "屏幕广告";
            }
        },
        statusData() {
            if (this.info.pay_status == 0) {
                return "未付款";
            } else if (this.info.pay_status == 1) {
                switch (this.info.status) {
                    case 0:
                        return "待审核";
                    case 1:
                        return "已审核";
                    case 2:
                        return "已上线";
                    default:
                        return "未知";
                }
            }
        },
        // deviceList() {
        //   return JSON.parse(this.info.device_sn || []).join("-");
        // },
    },
    methods: {
        gotoWebView(linkHref) {
            this.goToWebView(linkHref)
        },
        //预览图片
        previewImage(urls) {
            uni.previewImage({
                urls: [urls],
            });
        },
        go() {
            uni.navigateTo({
                url: "/pagesB/advertiseOrder/AdvertiseOrderDetails?order=" + this.info.order_sn,
            });
        },
    },
};
</script>
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style lang="scss" scoped>
.card {
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    background-color: #fff;
}

.content {
    padding: 20rpx;

    &-box {
        &-img {
            width: 100%;
            height: 300rpx;

            .img {
                width: 100%;
                height: 100%;
            }
        }

        &-right {
            margin-top: 20rpx;

            &-box {
                display: flex;
                line-height: 1.6;

                .link {
                    color: rgb(41, 121, 255) !important;
                    border-bottom: 1px solid rgb(41, 121, 255);
                }

                >view {
                    &:first-child {
                        text-align: right;
                        width: 140rpx;
                        color: $textGray;
                        white-space: nowrap;
                        flex-shrink: 0;
                    }

                    &:last-child {
                        color: $textBlack;
                    }
                }
            }
        }
    }
}

.bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;

    &-status {
        display: flex;

        .status {
            color: $themeColor;
        }
    }

    &-details {
        color: $themeColor;
    }
}
</style>
