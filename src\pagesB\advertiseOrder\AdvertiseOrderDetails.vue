<template>
    <view>
        <view class="content">
            <view class="box" v-for="item in listData" :key="item.id">
                <view class="box-item">
                    <view class="box-title">设备编号：</view>
                    <view class="box-txt">{{ item.device_sn }}</view>
                </view>
                <view class="box-item">
                    <view class="box-title">投放时间：</view>
                    <view class="box-txt">{{ item.date }}</view>
                </view>
                <view class="box-item">
                    <view class="box-title">投放价格：</view>
                    <view class="box-txt">{{ item.price }}</view>
                </view>
                <view class="box-item">
                    <view class="box-title">广告类型：</view>
                    <view class="box-txt">{{
                            item.type === 2 ? "弹窗广告" : "插屏广告"
                    }}</view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import { getMemberApplyAdDetail } from "@/common/http/api";
export default {
    data() {
        return { order: "", listData: [] };
    },
    methods: {
        getDetails() {
            let data = {
                order_sn: this.order,
            };
            getMemberApplyAdDetail(data).then((res) => {
                console.log("🚀 ~ res", res);
                this.listData = res;
            });
        },
    },
    onLoad(opt) {
        this.order = opt?.order;
        this.getDetails();
    },
};
</script>
<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style scoped lang="scss">
.content {
    padding: 20rpx;
}

.box {
    background-color: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;

    &-item {
        display: flex;
    }
}
</style>
