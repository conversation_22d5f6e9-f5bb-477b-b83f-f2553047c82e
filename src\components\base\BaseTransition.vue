<template>
    <u-transition :show="newShow" :mode="mode">
        <slot />
    </u-transition>
</template>
<script>
export default {
    name: 'BaseTransition',
    props: {
        show: { type: Boolean, default: false },
        mode: { type: String, default: 'fade' }
    },
    computed: {
        newShow: {
            get: function () {
                return this.show;
            },
            set: (val) => {
                this.$emit("update:show", val)
            }
        }
    }
}
</script>


<style scoped  lang='scss'>
</style>