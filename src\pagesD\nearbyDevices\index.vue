<template>
  <!--附近机器页面-->
  <view class="content">
    <ComList :loadingType="loadingType">
      <view class="deviceItem" v-for="(item, index) in listData" :key="index">
        <PointPlaceCard :info="item" />
      </view>
    </ComList>
  </view>
</template>

<script>
import { getNearbyMachineByProductId } from "@/common/http/api";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import PointPlaceCard from "@/components/PointPlaceCard.vue";
export default {
  name: "index",
  mixins: [myPull()],
  components: { ComList, PointPlaceCard },
  data() {
    return {
      productId: "", //产品类型id，根据此id获取附近设备列表
    };
  },

  methods: {
    //API 获取附近设备
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        lon: this.vCurLocation.longitude,
        lat: this.vCurLocation.latitude,
        /* #ifndef MP-TOUTIAO */
        radius: 1000,
        /* #endif */
        product_id: this.productId,
      };
      getNearbyMachineByProductId(data).then((res) => {
        done(res.data);
      });
    },
  },

  onLoad(opt) {
    this.productId = opt.productId;
    this.refresh();
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.deviceItem {
  margin-bottom: 30rpx;
}
</style>