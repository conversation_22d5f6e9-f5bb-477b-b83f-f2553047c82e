//取URL 域名
function getUrlHttp(url) {
    let urlArry = url.split("/");
    if (urlArry.length > 2) {
        return urlArry[0] + "//" + urlArry[2]
    } else {
        return
    }
}

// 取Url中/的参数
function getUrlDynamicData(url, name) {
    let urlArry = url.split("/");
    for (let i = 0; i < urlArry.length; i++) {
        if (urlArry[i] == name) { return urlArry[i + 1]; }
    }
    return
}

//取URL 中指定参数 
function getUrlParams(url, variable) {
    if (url.split("?").length > 1) {
        let vars = url.split("?")[1].split("&");
        console.log(vars);
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split("=");
            if (pair[0] == variable) { return pair[1]; }
        }
    }
    return false;
}
//将数组规整为长度为n的倍数
function roundArray(arr, len, placeItem) {
    if (arr.length == 0 || arr.length % len == 0)
        return arr;

    let mod = arr.length % len;
    console.log("mod=", mod);

    console.log("规整前，", arr);

    for (let i = 0; i < len - mod; i++) {
        arr.push(placeItem);
    }

    console.log("规整后，", arr);

    return arr;
}

/**
 * 时间戳转日期格式
 * @param {*} time 
 * @param {*} cFormat 
 * @returns 
 */
function parseTime(time, cFormat) {
    if (arguments.length === 0) {
      return null
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
      date = time
    } else {
      if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
        time = parseInt(time)
      }
      if ((typeof time === 'number') && (time.toString().length === 10)) {
        time = time * 1000
      }
      date = new Date(time)
    }
    const formatObj = {
      y: date.getFullYear(),
      m: date.getMonth() + 1,
      d: date.getDate(),
      h: date.getHours(),
      i: date.getMinutes(),
      s: date.getSeconds(),
      a: date.getDay()
    }
    const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
      const value = formatObj[key]
      // Note: getDay() returns 0 on Sunday
      if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
      return value.toString().padStart(2, '0')
    })
    return time_str
}

export default {
    getUrlHttp,
    getUrlParams,
    getUrlDynamicData,
    roundArray,
    parseTime
}