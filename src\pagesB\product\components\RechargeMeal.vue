<template>
  <!--充电套餐-组件-->
  <view class="comContent">
    <view class="rechargeList">
      <view
        class="rechargeItem flexColumnAllCenter"
        v-for="(item, index) in rechargeMealList"
        :key="index"
        :class="{
          placeItem: !item.title,
          selectRechargeItem: curSelectIndex == index,
        }"
        @click="onClickRechargeItem(item, index)"
      >
        <block v-if="item.isOther">
          <view v-show="curSelectIndex == index" class="other">
            <view class="input-box">
              <input class="input" type="text" disabled :value="inputValue" />
              <view class="input-tip">小时</view>
            </view>
            <view class="price"> 售价:{{ item.price }}元 </view>
          </view>
          <view class="name" v-show="!(curSelectIndex == index)">其他</view>
        </block>
        <block v-else>
          <view class="name">{{ item.title }}</view>
          <view class="price"> 售价:{{ item.price }}元 </view>
        </block>
      </view>
    </view>
    <view class="u-picker">
      <u-picker
        :show="isShowKeyDown"
        :columns="pickerList"
        title="选择时长(小时)"
        keyName="label"
        @confirm="confirm"
        @cancel="isShowKeyDown = false"
      ></u-picker>
    </view>
  </view>
</template>

<script>
import util from "@/utils/utils";

export default {
  name: "RechargeMeal",
  props: {
    chargeRule: { type: Object, default: {} },
    isShowKey: { type: Boolean, default: false },
  },
  computed: {
    isShowKeyDown: {
      // getter
      get: function () {
        return this.isShowKey;
      },
      // setter
      set: function (val) {
        this.$emit("update:isShowKey", val);
      },
    },
  },
  watch: {
    chargeRule: {
      handler(value) {
        let rechargeRule = value;
        console.log("rechargeRule", rechargeRule);
        if (rechargeRule) {
          if (rechargeRule.rule_unit == 1) {
            //单价为 元/分钟
            this.rechargeMealList = this.rechargeMealList.map((item) => {
              if (!item.test) {
                item.price = parseFloat(
                  item.time * parseFloat(rechargeRule.rule_money)
                ).toFixed(2);
                item.exchange_integral = parseInt(
                  item.price * rechargeRule.rule_point
                );
              }
              return item;
            });
          } else if (rechargeRule.rule_unit == 2) {
            //单价为 元/小时
            this.rechargeMealList = this.rechargeMealList.map((item) => {
              if (!item.test) {
                item.price = parseFloat(
                  item.hour * parseFloat(rechargeRule.rule_money)
                ).toFixed(2);
                item.exchange_integral = parseInt(
                  item.price * rechargeRule.rule_point
                );
              }
              return item;
            });
          }
        }
        this.rechargeMealList = util.roundArray(this.rechargeMealList, 3, {});
        if (this.rechargeMealList.length) {
          this.$emit("selectRechargeItem", { item: this.rechargeMealList[0] });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      rechargeMealList: [
        // {
        //   title: "充电1分钟",
        //   time: 1,
        //   price: "0.01",
        //   isNewConsumer: false,
        //   oldPrice: "0.01",
        //   no: 1,
        //   hour: 0.01,
        //   bSelect: "",
        //   test: true,
        //   exchange_integral: 1,
        // },
        {
          title: "充电1小时",
          time: 60,
          price: 2,
          isNewConsumer: false,
          oldPrice: "0.01",
          no: 1,
          hour: 1,
          bSelect: "",
          exchange_integral: "10",
        },
        {
          title: "充电2小时",
          time: 120,
          price: 4,
          isNewConsumer: false,
          oldPrice: "0.5",
          no: 2,
          hour: 2,
          bSelect: "",
          exchange_integral: "20",
        },
        {
          title: "其他",
          time: 0,
          hour: 0,
          price: undefined,
          exchange_integral: "20",
          isOther: true,
        },
        // {
        //   title: "充电4小时",
        //   time: 240,
        //   price: 8,
        //   isNewConsumer: false,
        //   oldPrice: "0.03",
        //   no: 3,
        //   hour: 4,
        //   bSelect: "",
        //   exchange_integral: "30",
        // },
        // {
        //   title: "充电8小时",
        //   time: 480,
        //   price: 16,
        //   isNewConsumer: false,
        //   oldPrice: "0.1",
        //   no: 4,
        //   hour: 8,
        //   bSelect: "",
        //   exchange_integral: "40",
        // },
      ],

      curSelectIndex: 0,
      inputValue: "",
      keyDownVal: [],
      pickerList: [
        [0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8],
      ],
    };
  },

  methods: {
    onClickRechargeItem(item, index) {
      if (index === 2) {
        this.isShowKeyDown = true;
      }
      this.curSelectIndex = index;
      this.$emit("selectRechargeItem", { item });
    },
    change(el) {
      let leng = this.keyDownVal?.length ?? 0;
      if (el == ".") {
        //如果输入的第一位是点  那也不行
        if (leng === 0) return;
        //如果已经存在点了 就不能输入了
        if (this.keyDownVal.includes(el)) return;
      }
      //小数点后面有2位数字就不能继续输入
      let index = this.keyDownVal.indexOf(".");
      if (index !== -1 && this.keyDownVal.slice(index)?.length > 2) return;

      this.keyDownVal.push(el);
    },
    backspace() {
      let leng = this.keyDownVal?.length ?? 0;
      if (leng > 0) {
        this.keyDownVal?.splice(leng - 1, 1);
      }
    },
    confirm(e) {
      let value = e?.value[0];
      this.inputValue = value;
      this.handleConfirm(value);
    },
    handleConfirm(e) {
      this.isShowKeyDown = false;
      let value = e;
      if (isNaN(Number(value))) {
        this.$emit("selectRechargeItem", {
          item: this.rechargeMealList[this.curSelectIndex],
        });
        return value;
      }

      this.rechargeMealList[this.curSelectIndex].hour = value;
      this.rechargeMealList[this.curSelectIndex].time = value * 60;
      let rechargeRule = this.chargeRule;
      let rule_unit = rechargeRule.rule_unit;

      if (rule_unit == 1) {
        //单价为 元/分钟
        if (!this.rechargeMealList[this.curSelectIndex].test) {
          this.rechargeMealList[this.curSelectIndex].price = parseFloat(
            this.rechargeMealList[this.curSelectIndex].time *
              parseFloat(rechargeRule.rule_money)
          ).toFixed(2);
          this.rechargeMealList[this.curSelectIndex].exchange_integral =
            parseInt(
              this.rechargeMealList[this.curSelectIndex].price *
                rechargeRule.rule_point
            );
        }
      } else if (rule_unit == 2) {
        //单价为 元/小时
        if (!this.rechargeMealList[this.curSelectIndex].test) {
          this.rechargeMealList[this.curSelectIndex].price = parseFloat(
            this.rechargeMealList[this.curSelectIndex].hour *
              parseFloat(rechargeRule.rule_money)
          ).toFixed(2);
          this.rechargeMealList[this.curSelectIndex].exchange_integral =
            parseInt(
              this.rechargeMealList[this.curSelectIndex].price *
                rechargeRule.rule_point
            );
        }
      }
      this.$emit("selectRechargeItem", {
        item: this.rechargeMealList[this.curSelectIndex],
      });
    },
  },
};
</script>

<style scoped lang="scss">
.comContent {
  .rechargeList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .rechargeItem {
      width: 180rpx;
      height: 120rpx;
      background: #fff;
      border-radius: 10rpx;

      border: 2rpx solid $themeColor;
      box-sizing: border-box;
      color: $themeColor;

      &:nth-child(n + 4) {
        margin-top: 20rpx;
      }

      .other {
        width: 100%;
        height: 100%;
        padding: 16rpx;
        text-align: center;
        box-sizing: border-box;

        .input-box {
          display: flex;
          align-items: center;
          color: #fff;
        }

        .input {
          background-color: #fff;
          color: #333;
        }

        .input-tip {
          font-size: 24rpx;
          line-height: 24rpx;
          white-space: nowrap;
          margin-left: 10rpx;
        }
      }

      .name {
        font-weight: bold;
        font-size: $font-size-xlarge;
      }

      .price {
        // margin-top: 28rpx;
        // color: $mainRed;
        // display: flex;
        // align-items: flex-end;
        font-size: $font-size-xsmall;
        margin-top: 12rpx;

        .unit {
          margin-bottom: 0rpx;
          font-size: $font-size-xsmall;
        }

        .value {
          margin-left: 6rpx;
          font-weight: bold;
          font-size: $font-size-middle;
        }
      }
    }

    .selectRechargeItem {
      background-color: $themeColor;

      .name {
        color: white;
      }

      .price {
        color: white;
      }
    }

    .placeItem {
      visibility: hidden;
    }
  }
}

//适配支付宝
.u-picker {
  ::v-deep .u-picker__view {
    .u-picker__view__column {
      :nth-child(n) {
        display: block !important;
      }
    }
  }
}
</style>
