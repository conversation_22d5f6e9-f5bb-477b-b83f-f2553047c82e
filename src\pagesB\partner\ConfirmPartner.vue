<template>
  <view class="container">
    <view class="content">
      <view class="device">
        <view class="title-box">
          <view class="title">设备编号</view>
          <!-- <view class="select" @click="go">选择设备</view> -->
        </view>
        <view class="device-list">
          <view
            class="device-list-item"
            v-for="(item, index) in deviceSnList"
            :key="item.id"
          >
            <view class="device-list-item-left">{{ item.device_sn }}</view>
            <view class="device-list-item-right" @click="delDevice(index)">
              <BaseIcon name="close-circle" size="20" />
            </view>
          </view>
        </view>
      </view>
      <view class="select-time">
        <view class="title">认养时间</view>
        <view class="input-box" @click="showActionSheet = !showActionSheet">
          <input
            v-model="dateVal"
            type="text"
            :disabled="true"
            placeholder="请选择投放日期"
            :style="{ padding: '10rpx 20rpx' }"
          />
        </view>
        <u-action-sheet
          :actions="list"
          :show="showActionSheet"
          :safeAreaInsetBottom="true"
          :closeOnClickAction="true"
          :closeOnClickOverlay="true"
          @close="showActionSheet = false"
          @select="selectYear"
        ></u-action-sheet>
      </view>
    </view>

    <!-- 占位块 -->
    <SafeBlock height="120" />
    <view class="fixed-btn flexRowBetween">
      <view class="total">
        <view class="total-box">
          <view>合计：</view>
          <view class="total-box-mark">￥</view>
          <view class="total-box-price">{{
            (priceInfo.totalPrice || 0).toFixed(2)
          }}</view>
        </view>
        <view class="total-num"> 共计{{ priceInfo.totalNum }}台设备 </view>
      </view>
      <view class="btn" @click="onClickToPay"> 确认提交 </view>
    </view>
    <BasePopup :show.sync="isShowPopup">
      <PayWayPopup @selectPayWay="selectPayWay" />
    </BasePopup>
  </view>
</template>
<script>
import BaseIcon from "../../components/base/BaseIcon.vue";
import SafeBlock from "../../components/list/SafeBlock.vue";
import { createInvestOrderAndPrepay } from "@/common/http/api";
import { locationMixin } from "@/mixins/locationMixin.js";
import PayWayPopup from "../../components/PayWayPopup.vue";
import BasePopup from "../../components/base/BasePopup.vue";
import { payOrder } from "@/utils/pay";
var app = getApp();
export default {
  components: { BaseIcon, SafeBlock, PayWayPopup, BasePopup },
  mixins: [locationMixin],
  computed: {
    priceInfo() {
      let totalPrice = this.deviceSnList.reduce(
        (total, nowVal) =>
          (parseFloat(total) * 1000 + parseFloat(nowVal.price) * 1000) / 1000,
        0
      );
      totalPrice = totalPrice * this.year;
      let totalNum = this.deviceSnList.length;
      return {
        totalPrice,
        totalNum,
      };
    },
  },
  data() {
    return {
      deviceSnList: [],
      bShowBottomPopup: false,
      phone: "",
      dateVal: "一年",
      list: [
        { name: "一年", num: 1 },
        { name: "二年", num: 2 },
        { name: "三年", num: 3 },
      ],
      showActionSheet: false,
      year: 1,
      isShowPopup: false,
    };
  },
  methods: {
    selectYear(el) {
      this.year = el?.num ?? 1;
      this.dateVal = el?.name ?? "一年";
    },
    delDevice(index) {
      this.deviceSnList?.splice(index, 1);
    },
    //点击了去支付
    onClickToPay() {
      if (this.selectDay == 0)
        return uni.showToast({ title: "请先选择时间", icon: "none" });

      if (!(this.deviceSnList?.length > 0))
        return uni.showToast({ title: "请先选择设备", icon: "none" });
      this.isShowPopup = true;
    },
    go() {
      let url = "/pagesD/CommonPage/NearbyMerchants?from=investment";

      if (this.vCurLocation.longitude && this.vCurLocation.latitude)
        return uni.navigateTo({
          url,
        });
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {
          uni.navigateTo({ url });
        });
      });
      //#endif
      // #ifdef MP-ALIPAY
      uni.showModal({
        title: "温馨提示：",
        content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
        success: ({ confirm }) => {
          if (confirm) {
            this.getCurrentLocation(() => {
              uni.navigateTo({ url });
            });
          }
        },
      });

      //#endif
    },
    onBottomPopupChange(e) {
      this.bShowBottomPopup = e.show;
    },
    //选择了支付方式
    selectPayWay(e) {
      this.isShowPopup = false;
      if (e.id == 2) {
        console.log("开始调用支付");
        //微信支付
        this.requestWxPay();
      }
    },
    requestWxPay() {
      //支付
      let device_sn = this.deviceSnList.map((el) => el.device_sn);
      let params = {
        phone: this.phone,
        device_sn,
        year: this.year,
      };
      let that = this;
      createInvestOrderAndPrepay(params).then((res) => {
        let payInfo = res.data;
        payOrder(this, payInfo, `/pagesB/investOrder/index`);
      });
    },
  },
  onLoad(opt) {
    if (opt?.device_sn && opt?.price)
      this.deviceSnList = [
        { device_sn: opt?.device_sn, price: opt.price },
      ];

    this.phone = opt?.phone;
  },
  onShow() {
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1]; // 当前页
    if (currPage.data.item) {
      this.deviceSnList.push(...currPage.data.item);
      currPage.data.item = "";

      // 对象数组去重复
      const removeDuplicateObj = (arr) => {
        let obj = {};
        arr =arr.length===0?0:arr.reduce((newArr, next) => {
          obj[next.device_sn]
            ? ""
            : (obj[next.device_sn] = true && newArr.push(next));
          return newArr;
        }, []);
        return arr;
      };
      this.deviceSnList = removeDuplicateObj(this.deviceSnList);
    }
  },
};
</script>

<style scoped lang="scss">
.content {
  padding: 20rpx;
}

.device {
  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: $font-size-xlarge;
      font-weight: 700;
    }

    .select {
      color: $themeColor;
      font-size: $font-size-base;
    }
  }

  .device-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    &-item {
      width: 48%;
      display: flex;
      align-items: center;
      height: 76rpx;
      color: $textBlack;
      border: 2rpx solid $dividerColor;
      background-color: #ecf0f1;
      font-size: $font-size-base;
      margin-top: 20rpx;

      &-left {
        display: flex;
        justify-content: center;
        flex-direction: column;
        flex: 0.8;
        border-right: 2rpx solid $dividerColor;
        height: 100%;
        padding-left: 10rpx;
      }

      &-right {
        flex: 0.2;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.select-time {
  margin-top: 20rpx;

  .title {
    font-size: $font-size-xlarge;
    font-weight: 700;
    margin-bottom: 20rpx;
  }

  .input-box {
    border-bottom: 2rpx solid $dividerColor;
  }
}

.fixed-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  z-index: 99;

  .total {
    &-box {
      display: flex;
      align-items: flex-end;
      color: $textBlack;
      font-size: $font-size-base;

      &-mark {
        color: red;
        font-size: $font-size-xsmall;
      }

      &-price {
        color: red;
        font-size: $font-size-middle;
      }
    }

    &-num {
      color: $textDarkGray;
      font-size: $font-size-small;
    }
  }

  .btn {
    padding: 20rpx 50rpx;
    color: #fff;
    font-size: $font-size-xlarge;
    background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
    border-radius: 20rpx;
  }
}
</style>
