/**
 *
 * @param {*} payInfo 订单信息
 * @param {*} url 支付完成跳转的页面
 */
function payOrder(that, payInfo, url, callBack, isRedirect = true) {
  /* #ifdef MP-WEIXIN */
  uni.requestPayment({
    /* #ifdef MP-WEIXIN */
    provider: 'wxpay',
    timeStamp: payInfo.timeStamp,
    nonceStr: payInfo.nonceStr,
    package: payInfo.package,
    signType: payInfo.signType,
    paySign: payInfo.paySign,
    /* #endif */
    /* #ifdef MP-ALIPAY */
    provider: 'alipay',
    orderInfo: payInfo.trade_no,
    /* #endif */
    /* #ifdef MP-TOUTIAO */
    orderInfo: payInfo,
    service: 5, //固定值，拉起小程序收银台
    /* #endif */
    success: (res) => {
      let isSuccess = false
      console.log('支付成功', res)
      /* #ifdef MP-WEIXIN */
      isSuccess = true
      /* #endif */
      /* #ifdef MP-TOUTIAO */
      const { code } = res

      const resCodeStatus = {
        0: '支付成功',
        1: '支付超时',
        2: '支付失败',
        3: '支付关闭',
        4: '支付取消',
      }
      console.log('🚀 ~ code', code)
      console.log('🚀 ~ resCodeStatus[code] ', resCodeStatus[code])
      if (code == 0) {
        console.log('🚀 ~ 抖音走成功')
        isSuccess = true
      } else {
        console.log('🚀 ~ 抖音走失败')

        console.log(uni.$u)
        return that.isShowErr(resCodeStatus[code] ?? '支付失败')
      }
      /* #endif */
      //跳转到订单详情页面
      isSuccess &&
        that.isShowSuccess('支付成功', 0, () => {
          if (callBack) {
            callBack()
          } else {
            if (isRedirect) {
              url &&
                uni.navigateTo({
                  url,
                })
            } else {
              url &&
                uni.navigateTo({
                  url,
                })
            }
          }
        })
    },
    fail: (err) => {
      console.log(uni.$u)
      console.log('支付失败', err)
      /* #ifdef MP-TOUTIAO */
      that.isShowErr(err?.errMsg || '支付失败')
      /* #endif */
      /* #ifndef MP-TOUTIAO */
      that.isShowErr('支付失败')
      /* #endif */
    },
    complete(res) {
      console.log('支付complete', res)
    },
  })
  /* #endif */
  /* #ifdef MP-ALIPAY */
  
  my.tradePay({
    tradeNO: payInfo.trade_no,
    success: (res) => {
      let isSuccess = false
      console.log('支付成功tradePay', res)
      if (res.resultCode != 9000)
        return uni.showToast({
          title: '支付失败',
          icon: 'none',
        })
      isSuccess = true
      //跳转到订单详情页面
      isSuccess &&
        that.isShowSuccess('支付成功', 0, () => {
          if (callBack) {
            callBack()
          } else {
            if (isRedirect) {
              url &&
                uni.redirectTo({
                  url,
                })
            } else {
              url &&
                uni.navigateTo({
                  url,
                })
            }
          }
        })
    },
    fail: (res) => {
      console.log('支付失败', res)
      uni.showToast({
        title: '支付失败~',
        icon: 'none',
      })
    },
    complete: (res) => {
      console.log('支付complete', res)

    },
  })

  /* #endif */
  /* #ifdef H5 */
  if (typeof WeixinJSBridge != 'undefined') {
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest',
      {
        appId: that.vAppId, //公众号ID，由商户传入
        timeStamp: payInfo.timeStamp, //时间戳，自1970年以来的秒数
        nonceStr: payInfo.nonceStr, //随机串
        package: payInfo.package,
        signType: payInfo.signType, //微信签名方式：
        paySign: payInfo.paySign, //微信签名
      },
      function (res) {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          console.log('支付成功', res)
          // 使用以上方式判断前端返回,微信团队郑重提示：
          //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          that.isShowSuccess('支付成功', 0, () => {
            if (callBack) {
              callBack()
            } else {
              if (isRedirect) {
                url &&
                  uni.redirectTo({
                    url,
                  })
              } else {
                url &&
                  uni.navigateTo({
                    url,
                  })
              }
            }
          })
        } else {
          console.log('支付失败', res.err_msg)
          that.isShowErr('您已取消支付，请重新支付')
        }
      },
    )
  } else if (typeof AlipayJSBridge != "undefined") {
    // 3、调用支付宝api唤起支付功能
    AlipayJSBridge.call("tradePay", {
      // tradeNO: "2020111422001434251423057464"
      tradeNO: payInfo.trade_no
    }, function (data) {
      console.log(data)
      //log(JSON.stringify(data));
      if (data.resultCode != 9000) {
        //支付失败
        that.isShowErr('您已取消支付，请重新支付')
        //   alert(data.resultCode+"："+data.memo);
      } else {
        that.isShowSuccess('支付成功', 0, () => {
          if (callBack) {
            callBack()
          } else {
            if (isRedirect) {
              url &&
                uni.redirectTo({
                  url,
                })
            } else {
              url &&
                uni.navigateTo({
                  url,
                })
            }
          }
        })
        //支付成功
      }
    });

  }
  /* #endif */
}
export { payOrder }
