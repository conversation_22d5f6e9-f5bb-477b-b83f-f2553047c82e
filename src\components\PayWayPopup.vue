<template>
  <!--支付方式弹窗-->
  <view class="popupContent">
    <view class="titleLine">
      <view class="title">支付中心</view>
    </view>

    <view class="payWayList">
      <block v-for="item in payWayList" :key="item.id">
        <view v-if="item.id == 1 ? isShowBalanceType : true" class="payWayItem" @click="onSelectPayWay(item)">
          <image class="iconPay" :style="item.style" :src="item.icon"></image>
          <view class="nameWrap">
            <view class="name flex">
              <text>
                {{ item.name }}
              </text>
              <view v-if="item.id == 1 && vCreateOrderInfo.orderInfo.vip_price * 1" class="flex flex_m">
                <image class="vip_stain_img" src="../static/icon/stain.png"></image>
                <view class="vip_price">会员价{{ vCreateOrderInfo.orderInfo.vip_price }}元</view>
              </view>
            </view>
            <view class="remain" v-if="item.id == 1">（剩余{{ balance }}元）</view>
          </view>
          <image v-if="item.id == 2" class="iconRecom" :src="imgRecom" />
          <image class="imgCheck" :src="imgChecked" v-if="curSelectPayWayId == item.id"></image>
          <image class="imgCheck" :src="imgUncheck" v-if="curSelectPayWayId != item.id"></image>
        </view>
      </block>
    </view>

    <view class="btn">
      <BaseButton :text="bt_text" @onClick="comfirmSelectPayWay" />
    </view>
  </view>
</template>

<script>
import BaseButton from './base/BaseButton.vue'
export default {
  name: "PayWayPopup",
  components: { BaseButton },
  props: {
    isShowBalanceType: { type: Boolean, default: false },
    balance: { type: String | Number, default: 0, }
  },
  data() {
    return {
      payWayList: [
        {
          id: 1,
          name: "钱包余额支付",
          icon: require("@/static/public/ic_pay_wallet_theme.png"),
          style: "width:50rpx;height:46rpx",
        },
        /* #ifdef MP-WEIXIN ||MP-TOUTIAO */
        {
          id: 2,
          name: "微信支付",
          icon: require("@/static/public/ic_pay_weixin_theme.png"),
          style: "width:56rpx;height:46rpx",
        },
        /* #endif */
        /* #ifdef MP-ALIPAY */
        {
          id: 2,
          name: "支付宝支付",
          icon: require("@/static/public/ic_pay_ali_theme.png"),
          style: "width:50rpx;height:50rpx",
        },
        /* #endif */
      ],

      imgChecked: require("@/static/public/ic_checked.png"),
      imgUncheck: require("@/static/public/ic_uncheck.png"),
      imgRecom: require("@/static/public/ic_pay_recom.png"),
      curSelectPayWayId: 1,
      gotoWellter:false

    };
  },
  computed: {
    bt_text() {
      if (this.vCreateOrderInfo.orderInfo.vip_price * 1&&this.curSelectPayWayId == 1) {
        if (this.balance*1 < this.vCreateOrderInfo.orderInfo.vip_price*1) {
          this.gotoWellter=true
          return '余额不足,去充值';
        }else{
          this.gotoWellter=false
          return '确认支付';
        }
      }else if (this.curSelectPayWayId == 1 && this.balance*1 < this.vCreateOrderInfo.orderInfo.order_price*1) {
        this.gotoWellter=true
        return '余额不足,去充值';
      } else {
        this.gotoWellter=false
        return '确认支付';
      }
    },
  },
  methods: {
    onSelectPayWay(item) {
      this.curSelectPayWayId = item.id;
    },
    comfirmSelectPayWay() {
      console.log('gotoWellter',this.gotoWellter)
      if (this.gotoWellter) {
        uni.navigateTo({
          url: `/pagesD/wallet/myWallet?from=orider`,
        });
      } else {
        let item = this.payWayList.filter(
          (item) => item?.id == this.curSelectPayWayId
        )?.[0];
        this.$emit("selectPayWay", item);
      }

    },
  },

  mounted() {
    this.curSelectPayWayId = 1
  },
};
</script>

<style scoped lang="scss">
.vip_stain_img {
  width: 10rpx;
  height: 15rpx;
}

.flex {
  display: flex;
  align-items: center;
}

.flex_end {
  margin-left: 5rpx;
}

.vip_price {
  color: white;
  background-color: #dc3d29;
  font-size: 16rpx;
  font-weight: bold;
  padding: 5rpx 20rpx;
  border-radius: 10rpx;

}

.popupContent {
  background-color: white;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  padding: 80rpx 50rpx 100rpx 50rpx;

  .titleLine {
    @include flexRowVertCenter();

    .title {
      font-size: $font-size-xxxlarge;
      color: $textBlack;
      font-weight: bold;
    }

    .close {
      margin-left: auto;
      width: 26rpx;
      height: 27rpx;
    }
  }

  .payWayList {
    margin-top: 40rpx;

    .payWayItem {
      @include flexRowVertCenter();
      padding: 50rpx 0;
      border-bottom: 1rpx solid $dividerColor;

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(2) {
        .icon {
          width: 59rpx;
          height: 48rpx;
        }
      }

      // .iconWeixin {
      //   width: 53rpx;
      //   height: 51rpx;
      // }
      // .iconAlipay {
      //   width: 50rpx;
      //   height: 50rpx;
      // }
      .iconRecom {
        width: 44rpx;
        height: 22rpx;
      }

      .nameWrap {
        // display: flex;
        // align-items: flex-end;

        .name {
          font-size: $font-size-xlarge;
          color: $textBlack;
          margin-left: 30rpx;
        }

        .remain {
          font-size: $font-size-xsmall;
          margin-left: 10rpx;
          color: $textGray;
        }
      }

      .imgCheck {
        margin-left: auto;
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

}

.btn {
  margin: 20rpx 0;
}
</style>
