import { formatDiff } from "../utils/DateUtil";
export const chargeStatus = {
    data() {
        return {
            orderInfo: {}, //订单信息
            oldChargeStatus: 3,
            chargeStatus: "3", //订单状态
            momentTime: ["00", "00", "00"], //充电时长
            oldTime: "", //订单创建时间
            chargeTimer: null, //充电计时器的线程号
            checkType: 0, //1(充电中=》结束  未充电=》返回)   2(充电中=》返回  未充电=》执行充电)
            remainTime: "00:00:00",
            remainTimeData: 0,//剩余时间 秒
        }
    },
    watch: {
        chargeStatus: {
            handler(newValue) {
                //此函数名是vue提供的
                console.log("🚀 ~ newValue", newValue);
                if (newValue != 3 && newValue != 4) {
                    this.getTimerCharge();//启动定时器
                    this.getChargeTime(newValue);//先执行一遍逻辑
                }
            },
            deep: true,
        },
    },
    methods: {
        //分钟转换成 00:00:00格式
        toHourMinute(minutes) {
            if (minutes) {
                let h = Math.floor(minutes / 60);
                if (h.toString().length < 2) h = "0" + h;
                let m = minutes % 60;
                if (m.toString().length < 2) m = "0" + m;
                return h + ":" + m + ":" + "00";
            } else {
                return "00:00:00";
            }
        },
        getChargeTime(newValue) {

            if (newValue != 3 && newValue != 4) {
                let nowDate = new Date().getTime();
                let formatDiffData = formatDiff(
                    nowDate,
                    this.oldTime,
                    this.chargeStatus,
                    Math.round(this.orderInfo.rule_unit == 2
                        ? this.orderInfo.duration * 60
                        : this.orderInfo.duration) * 60,
                );
                if (formatDiffData?.length >= 2) {


                    this.momentTime = formatDiffData?.[0]?.split(":") || ["00", "00", '00'];
                    this.remainTime = formatDiffData?.[1] || '00:00:00';
                    this.remainTimeData = formatDiffData?.[2] || 0;
                } else {

                    this.momentTime = ["00", "00", "00"];
                    this.remainTime = "00:00:00"
                    this.remainTimeData = 0
                    this.chargeStatus = this.oldChargeStatus;
                    this.clearChargeTimer();
                }
                // this.$forceUpdate();
                // console.log(" this.momentTim", this.momentTime);
            } else {
                this.momentTime = ["00", "00", "00"];
                this.clearChargeTimer(); //清理定时器
            }
        },
        getTimerCharge() {
            console.log("这里启动定时器");
            this.chargeTimer = setInterval(() => {
                this.getChargeTime();
            }, 1000);
        },
        clearChargeTimer() {
            if (this.chargeTimer) {
                clearInterval(this.chargeTimer);
                this.chargeTimer = null;
                console.log("定时器清理完成");
            }
        },
        //处理读取回调
        readChargeStatusCallback(flag) {
            console.log("🚀 ~ flag", flag)

            console.log("🚀 ~ this.checkType ", this.checkType)
            //充电已经结束 查询到设备还在充电  写入结束充电指令
            if (flag && this.checkType === 1) return this.onRecharge(0, true);

            //查询订单未结束
            if (this.checkType === 2) {
                //设备正常充电中
                if (flag) {
                    uni.showToast({
                        title: '设备正在充电中~',
                        icon: 'none',
                        mask: true,
                        duration: 3000,
                    })
                    // uni.showModal({
                    //     title: '温馨提示',
                    //     content: '设备正在充电中~'
                    // })
                } else {
                    //设备未充电
                    console.log("🚀 ~ 设备未充电  开始充电》》》》》》》》")
                    let nowTime = new Date().getTime(); //现在时间
                    console.log("🚀 ~ nowTime", nowTime)
                    let chargeTime = this.orderInfo.duration * (this.orderInfo?.rule_unit == 2 ? 60 : 1) * 60 * 1000;//套餐时间
                    console.log("🚀 ~ chargeTime", chargeTime)
                    //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间


                    let remainTime = Math.ceil((chargeTime - (nowTime - this.oldTime)) / 1000 / 60);
                    console.log("🚀 ~ this.oldTime", this.oldTime)
                    console.log("🚀 ~ remainTime", remainTime)
                    if (remainTime > 0) {
                        return this.onRecharge(remainTime, true)
                    }
                }


            }
        },
        // 判断订单
        isCheckOrder(chargeOrderStatus, prom_type, pay_status) {


            if (chargeOrderStatus === 3 || chargeOrderStatus === 4) {
                //充电已经结束了  查询设备充电状态  充电中=》结束  未充电=》返回
                this.checkType = 1;
            } else {
                this.checkType = 2;
                /**
                 * 因为订单已经判断了状态 所以下面代码实际无意义
                 */


                /*   if (prom_type === 1) {
                      //付费订单
                      if (pay_status === 1) {
                          //未支付  查询设备充电状态  充电中=》结束  未充电=》返回
                          this.checkType = 1;
                          // 这里无意义
                      } else if (pay_status === 2) {//已支付  查询设备充电状态  充电中=》返回  未充电=》执行充电
                          this.checkType = 2;
                      }
  
  
                  } else if (prom_type === 2) {
                      //免费订单
                      //查询设备充电状态  充电中=》返回  未充电=》执行充电
                      this.checkType = 2;
                  }  
                  
                  **/
            }
            this.readChargeStatus();//查询充电状态 这个函数在bleMixin.js
        }
    },
    // onUnload() {
    //     this.clearChargeTimer();
    //     console.log("执行了清理定时器");
    // },
}
