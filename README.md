# wrls

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

用到的框架 uview2.0 https://www.uviewui.com/components/install.html

全局变量全部存在store/index.js 

全局函数全部存在store/$u.mixin.js  methods

抖音小程序真机调试会提示主包过大。可加上打包压缩--minimize 
 "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch --minimize"

 
全局登录组件/components/LoginPopup 需要验证身份的页面都需要引入该组件(不同平台，新增即可)

抖音客服账号  19972088349


