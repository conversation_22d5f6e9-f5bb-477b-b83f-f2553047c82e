<template>
  <view class="card" @click="go">
    <view class="card-info">
      <view class="card-info-img">
        <image class="img" :src="info.img || '/static/icon/logo.png'" />
      </view>
      <view class="card-info-main">
        <view class="main-title textMaxOneLine">
          {{ info.hotelName||"" }}
        </view>
        <view class="main-box">
          <view class="main-box-score" v-if="info">
            <u-rate count="5" v-model="info.score" :readonly="true"></u-rate>
            <view class="main-box-score-txt">
              {{ info.score.toFixed(1) }}分
            </view>
          </view>
          <view class="main-box-distance">{{ distance }}</view>
        </view>
        <view class="main-address textMaxOneLine"
          >地址：{{ info.receiverAddres||"" }}</view
        >
      </view>
    </view>
    <view class="card-line"></view>
    <view class="card-rights">
      <view class="card-rights-item">
        <image
          class="card-rights-item-img"
          src="/pagesD/static/nearby_merchants/icon_1.png"
        />
        <text>免费券</text>
      </view>
      <view class="card-rights-item">
        <image
          class="card-rights-item-img"
          src="/pagesD/static/nearby_merchants/icon_2.png"
        />
        <text>停车位 </text>
      </view>
      <view class="card-rights-item">
        <image
          class="card-rights-item-img"
          src="/pagesD/static/nearby_merchants/icon_3.png"
        />
        <text>WiFi</text>
      </view>
      <view class="card-rights-item">
        <image
          class="card-rights-item-img"
          src="/pagesD/static/nearby_merchants/icon_4.png"
        />
        <text>支付宝</text>
      </view>
      <view class="card-rights-item">
        <image
          class="card-rights-item-img"
          src="/pagesD/static/nearby_merchants/icon_5.png"
        />
        <text>微信支付</text>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    distance() {
      let nowDistance=""
      if(this.info&&this.info.lon&&this.info.lat){
        nowDistance = this.distanceLength(this.info.lon, this.info.lat);
      }
      // console.log("🚀 ~ nowDistance", nowDistance);

      return nowDistance ? `${nowDistance}km` : "";
    },
  },
  data() {
    return {};
  },

  methods: {
    // 计算两点的距离
    distanceLength(la2, lo2) {
      let la1 = this.vCurLocation.latitude;
      let lo1 = this.vCurLocation.longitude;
      if (!la1 || !lo1 || !la2 || !lo2) return false;
      let La1 = (la1 * Math.PI) / 180.0;
      let La2 = (la2 * Math.PI) / 180.0;
      let La3 = La1 - La2;
      let Lb3 = (lo1 * Math.PI) / 180.0 - (lo2 * Math.PI) / 180.0;
      let s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(La3 / 2), 2) +
              Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)
          )
        );
      s = s * 6378.137;
      s = Math.round(s * 10000) / 10000;
      s = s.toFixed(2);
      return s;
    },
    go() {
      uni.navigateTo({
        url: `/pagesD/nearbyMerchants/merchantsDetails?hotel_id=${this.info.id}`,
      });
    },
  },
};
</script>


<style scoped  lang='scss'>
.card {
  height: 254rpx;
  padding: 26rpx 44rpx;
  margin-bottom: 10rpx;
  border-radius: 20rpx;
  background-color: #fff;
  &-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-img {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      flex-shrink: 0;
      .img {
        width: 100%;
        height: 100%;
        border-radius: inherit;
      }
    }
    &-main {
      flex: 1;
      margin-left: 16rpx;
      .main-title {
        color: #464646;
        font-size: 30rpx;
        font-weight: 700;
      }
      .main-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20rpx 0;
        &-score {
          display: flex;
          justify-content: center;
          align-items: center;
          color: #5a5a5a;
          font-size: 24rpx;
          &-txt {
            margin-left: 20rpx;
          }
        }
        &-distance {
          color: #303030;
          font-size: 28rpx;
        }
      }
      .main-address {
        color: #5a5a5a;
        font-size: 22rpx;
      }
    }
  }
  &-line {
    width: 100%;
    height: 2rpx;
    margin: 30rpx 0;
    background-color: #d2d2d2;
  }
  &-rights {
    display: flex;
    justify-content: space-around;
    align-items: center;
    &-item {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #5a5a5a;
      font-size: 22rpx;
      &-img {
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
      }
    }
  }
}
</style>