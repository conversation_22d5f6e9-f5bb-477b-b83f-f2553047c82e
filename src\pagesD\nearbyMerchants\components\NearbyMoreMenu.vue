<template>
  <view class="list-card">
    <UniSwiperDot
      :info="moreList"
      :current="current"
      :dotsStyles="{
        backgroundColor: 'rgba(83, 200, 249, 0.3)',
        selectedBackgroundColor: 'rgba(83, 200, 249, 0.9)',
        bottom: '0',
      }"
      v-if="moreList.length > 0"
    >
      <swiper
        class="swiper-box"
        :style="{ height: moreList[0].length > 4 ? '265rpx' : '130rpx' }"
        @change="change"
      >
        <swiper-item v-for="(list, index) in moreList" :key="index">
          <view class="list">
            <view
              class="list-item"
              v-for="item in list"
              :key="item.id"
              @click="handleLink(item)"
            >
              <view class="list-item-box">
                <image class="list-item-box-img" :src="item.img" />
                <view class="list-item-box-name">{{ item.categoryName }}</view>
              </view>
            </view>
            <view style="width: 25%"></view>
            <view style="width: 25%"></view>
          </view>
        </swiper-item>
      </swiper>
    </UniSwiperDot>
  </view>
</template>
<script>
import { getHotelCategoryOneLevel } from "@/common/http/api";
import UniSwiperDot from "@/components/uni/uni-swiper-dot/uni-swiper-dot.vue";
export default {
  name: "NearbyMoreMenu",
  props: {
    deviceSn: { type: String, default: "" },
    miniFoodInfo: { type: Object, default: {} },
  },
  components: { UniSwiperDot },
  data() {
    return {
      moreList: [],
      current: 0,
    };
  },
  methods: {
    change(e) {
      this.current = e.detail.current;
    },
    handleLink(item) {
      uni.navigateTo({
        url: `/pagesD/nearbyMerchants/merchantsType?title=${item.categoryName}&id=${item.id}`,
      });
    },
    async getMiniMenuHandle() {
      let res = await getHotelCategoryOneLevel();
      let itemIndex = 0,
        colNum = 8; //每页显示数量
      let funcsList = res;
      while (itemIndex < funcsList.length) {
        this.moreList.push(funcsList.slice(itemIndex, itemIndex + colNum));
        itemIndex += colNum;
      }
    },
  },
  created() {
    this.getMiniMenuHandle();
  },
};
</script>


<style scoped  lang='scss'>
.list-card {
  // padding-bottom: 30rpx;
}

.list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;

  &-item {
    width: 25%;

    &-box {
      display: flex;
      flex-direction: column;

      align-items: center;

      &-img {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.1);
      }

      &-name {
        font-size: 20rpx;
        color: #333333;
        margin-top: 10rpx;
      }
    }

    &:nth-child(n + 5) {
      margin-top: 24rpx;
    }
  }
}
</style>