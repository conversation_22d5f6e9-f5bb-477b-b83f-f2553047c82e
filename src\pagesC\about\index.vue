<template>
    <!--关于我们-->
    <view class="content">
        <!-- #ifndef H5 -->
        <BaseNavbar title="关于我们" bgColor="#fff"  color="#333" />
        <!-- #endif -->
        <view class="infoWrap flexColumnHorzCenter">
            <image class="logo" :src="vBaseInfo.site_logo"></image>
            <view class="xhkj">{{vAppName}}</view>
            <view class="app">{{vBaseInfo.site_version}}</view>
        </view>
        <view class="section">
            {{vBaseInfo.site_seo_description}}
        </view>
        <!-- <view class="protocol" @click="onClickProtocol">
            《用户服务协议》
            <SafeBlock />
        </view> -->

    </view>
</template>

<script>
import SafeBlock from '../../components/list/SafeBlock.vue';
import BaseNavbar from '@/components/base/BaseNavbar.vue'
export default {
    components: { SafeBlock,BaseNavbar },
    name: "index",
    data() {
        return {
            appVersion: "",
        };
    },
    methods: {
        onClickProtocol() {
            uni.navigateTo({
                url: `/pagesC/userServiceNotice/index`,
            });
        },
    },
    onLoad() {

    },
};
</script>

<style lang="scss">
page {
    background-color: #fff;
}

.content {
    position: relative;

    .infoWrap {
        padding-top: 126rpx;

        .logo {
            width: 210rpx;
            height: 210rpx;
        }

        .xhkj {
            margin-top: 32rpx;
            color: $textBlack;
            font-weight: bold;
            font-size: $font-size-xlarge;
        }

        .app {
            margin-top: 20rpx;
            font-size: $font-size-xsmall;
            color: $textGray;
        }
    }

    .section {
        margin-top: 78rpx;
        padding: 0 35rpx;
        color: $textBlack;
        font-size: $font-size-small;
    }

    .protocol {
        position: fixed;
        left: 50%;
        bottom: 20rpx;
        transform: translateX(-50%);
        font-size: $font-size-small;
        color: $themeColor;
    }
}
</style>