/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
@import 'uview-ui/theme.scss';
/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title: 40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36rpx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph: 30rpx;


//颜色
//主题调色板
$mainBlue: #00449B;
$mainLightBlue: #0568E6;
$mainOrange: #F05A00;
$mainLightOrange: #F08300;
$mainRed: #FF2121;

//字体颜色
$textWhite: #fff;
$textBlack: #333333;
$textDarkGray: #666666;
$textGray: #999999;

//分隔线颜色
$dividerColor: #eeeeee;
$dividerLightColor: #F9F9F9;

//灰色按钮颜色
$btnGray: #cccccc;

$themeColor: #0DACE0;

//页面背景颜色
$pageBgColor: #f2f2f2;

//自定义的文字尺寸
$font-size-xxsmall: 20rpx;
$font-size-xsmall: 23rpx;
$font-size-small: 24rpx;
$font-size-base: 26rpx;
$font-size-middle: 28rpx;
$font-size-large: 30rpx;
$font-size-xlarge: 32rpx;
$font-size-xxlarge: 34rpx;
$font-size-xxxlarge: 30rpx;

//间距
$mainMargin: 30rpx; //主内容区外边距

//全居中
@mixin flexAllcenter {
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
}

//左右对齐
@mixin flexRowBetween {
    display: flex;
    justify-content: space-between;
}

//横向排列，垂直居中
@mixin flexRowVertCenter {
    display: flex;
    flex-direction: row;
    align-items: center;
}

//左右对齐 行居中
@mixin flexRowBetweenColCenter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

//竖向排列全居中
@mixin flexColumnAllCenter {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;
}

//竖向排列上下对齐
@mixin flexColumnBetween {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}


//最多显示1行
@mixin textMaxOneLine {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

//最多显示两行
@mixin textMaxTwoLine {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}


//页面最下面fixed定位
@mixin fixedBottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
}


//FLEX
//横向排列 左右对齐
.flexRowBetween {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

//横向排列，全居中
.flexRowAllCenter {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

//横向排列，水平居中
.flexRowHorzCenter {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

//横向排列，垂直居中
.flexRowVertCenter {
    display: flex;
    flex-direction: row;
    align-items: center;
}

//纵向排列，全居中
.flexColumnAllCenter {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

//纵向排列，水平居中
.flexColumnHorzCenter {
    display: flex;
    flex-direction: column;
    align-items: center;
}

//纵向排列，垂直居中
.flexColumnVertCenter {
    display: flex;
    flex-direction: column;
    justify-content: center;
}



//最多显示1行
.textMaxOneLine {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

//最多显示两行
.textMaxTwoLine {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}



.checkbox {
    transform: scale(0.6);
}

.checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    border: 1px solid $themeColor;
    background: $themeColor;
    color: #FFF !important;
}