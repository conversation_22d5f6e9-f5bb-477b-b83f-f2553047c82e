<template>
    <view class="content">
        <view class="flex">
            <TemplateCard v-for="item in listData" :key="item.id" :info="item" />
        </view>
        <LoginPopup />
    </view>
</template>
  
<script>
import TemplateCard from "./components/TemplateCart";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import { getMemberCashLog } from "@/common/http/api";
import LoginPopup from "@/components/LoginPopup.vue";
export default {
    name: "index",
    components: { TemplateCard, ComList, LoginPopup },
    mixins: [myPull()],
    data() {
        return {
            consumeType: 1, //1-余额明细 2-环保豆明细

            tablist: [
            ],
            current: 0,
            listData: [{ url: '@/pagesD/static/mbg.png' }, { url: '@/pagesD/static/mbg.png' }, { url: '@/pagesD/static/mbg.png' },
            { url: '@/pagesD/static/mbg.png' }, { url: '@/pagesD/static/mbg.png' }]
        };
    },
    methods: {
        // getList(page, done) {
        //     let data = {
        //         type: this.tablist[this.current]?.status,
        //         page,
        //         limit: 10,
        //     };
        //     getMemberCashLog(data).then((res) => {
        //         done(res);
        //     });
        // },
    },
    onLoad(opt) {

    },
};
</script>
  
<style lang="scss">
page {
    background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.content {
    // width: 100%;
    padding: 20rpx;
    background-color: #fff;
    // display: flex;
}

.flex {
    // width: 100%;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    // border: 2rpx solid red;

}
</style>