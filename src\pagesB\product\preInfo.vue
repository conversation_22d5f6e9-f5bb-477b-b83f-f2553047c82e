<template>
    <view class="pre_btn">

        <view class="pre_btn_item">
            <view class="pre_btn_item_line">
                <image class="image" src="../static/点.png" />
            </view>
            <view class="pre_btn_item_left">
                预付金
            </view>
            <view class="pre_btn_item_green">
                ￥{{
                    orderInfo.total_amount||0
                }}元
            </view>
            <view class="bubble-box">已缴纳</view>
        </view>
        <view class="pre_btn_item">
            <view class="pre_btn_item_line">
                <image class="image" src="../static/点.png" />
            </view>
            <view class="pre_btn_item_left">
                起步时长
            </view>
            <view class="pre_btn_item_green">
                {{
                        orderInfo.pre_min_time||0
                    }}分钟
            </view>
        </view>
        <view class="pre_btn_item">
            <view class="pre_btn_item_line">
                <image class="image" src="../static/点.png" />
            </view>
            <view class="pre_btn_item_left">
                起步价
            </view>
            <view class="pre_btn_item_pre">
                {{
                        orderInfo.pre_min_price||0
                    }}（含{{ orderInfo.pre_min_time||0 }}分钟）
            </view>
        </view>
        <view class="pre_btn_item">
            <view class="pre_btn_item_line">
                <image class="image" src="../static/点.png" />
            </view>
            <view class="pre_btn_item_left">
                超起步价后
            </view>

            <span class="pre_btn_item_top">
                <span>
                    <view>
                        <span class="pre_btn_item_pre">
                            {{
                    orderInfo.pre_per_minute||0
                }}元/1分钟
                        </span>
                        <span class="pre_btn_item_green">
                            （超出起步价后）
                        </span>
                    </view>
                    <view class="pre_btn_item_small">
                        不足1分钟按1分钟算
                    </view>
                </span>

            </span>
        </view>
        <view class="pre_btn_item">
          
        </view>
        <view class="pre_btn_item">
            <view class="pre_btn_item_line">
                <image class="image" src="../static/点.png" />
            </view>
            <view class="pre_btn_item_left">
                支付订单后
            </view>
            <view class="pre_btn_item_blod">
                自动退回剩余预付金
            </view>
        </view>

    </view>
</template>
<script>

import { locationMixin } from "@/mixins/locationMixin";
import BaseIcon from "@/components/base/BaseIcon.vue";
export default {
    name: 'index',
    components: {
        BaseIcon
    },
    data() {
        return {
            orderInfo: {
            },

        }
    },
    mixins: [locationMixin],
    onLoad(opt) {
        // 解码 URL 编码的字符串
        console.log('infoOpt',opt)
        if (opt.orderInfo) {
            const decodedOrderInfoString = decodeURIComponent(opt.orderInfo);
            // 将解码后的字符串解析为 JSON 对象
         
            this.orderInfo = JSON.parse(decodedOrderInfoString);
            console.log('解析字符串', this.orderInfo,JSON.parse(decodedOrderInfoString))
        }

    }


}
</script>
<style lang="scss">
page {
    background-color: #fff;
}
</style>
<style scoped lang="scss">
.bubble-box {
    margin-left: 10rpx;
    border: 1rpx solid #60fe4c;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    font-size: 23rpx;
    color: #05d06b;
    background-color: #c6ffbe;
    padding: 5rpx 10rpx;

}


.pre_btn {
    margin-top: 20rpx;
    padding: 0 20rpx 20rpx 20rpx;

    // color: #ff852f;
    &_item {
        display: flex;
        margin: 10rpx 0;
        align-items: center;

        &_blod {
            font-size: 28rpx;
            font-weight: bold;
        }

        &_line {
            width: 25rpx;
            height: 25rpx;
            margin-right: 10rpx;
            display: flex;
            align-items: center;

            .image {
                width: 100%;
                height: 100%;
            }

        }

        &_green {
            font-size: 25rpx;
            color: #666;
        }

        &_top {
            vertical-align: top;
        }

        &_small {
            font-size: 25rpx;
            color: #666;
        }

        &_pre {
            color: #ff3725;
            font-size: 25rpx;
            font-weight: bold;
        }

        &_left {
            width: 160rpx;
            margin-right: 20rpx;
            font-size: 30rpx;
            font-weight: bold;
        }
    }

    &_icon {
        margin-right: 10rpx;
    }

}
</style>