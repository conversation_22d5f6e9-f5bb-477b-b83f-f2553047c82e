<template>
  <view>
    <view class="content">
      <view class="title"> 完成以下4步,就可以解锁附近WiFi了 </view>
      <view class="step">
        <view class="step-item" v-for="item in stepList" :key="item.id">
          <image class="step-item-img" :src="item.src" />
        </view>
      </view>
      <view class="btn flexRowAllCenter">
        <BaseButton
          @onClick="back"
          text="看懂了,去连接"
          color="#09a2e3"
          shape="circle"
          width="400rpx"
        />
      </view>
    </view>
  </view>
</template>
<script>
import BaseButton from "../../components/base/BaseButton.vue";
export default {
  data() {
    return {
      stepList: [
        {
          id: 1,
          src: "../static/step/step_1.png",
        },
        {
          id: 2,
          src: "../static/step/step_2.png",
        },
        {
          id: 3,
          src: "../static/step/step_3.png",
        },
        {
          id: 4,
          src: "../static/step/step_4.png",
        },
      ],
    };
  },
  methods: {
    back() {
      uni.navigateBack();
    },
  },
  onLoad() {},
  components: { BaseButton },
};
</script>


<style scoped  lang='scss'>
.content {
  padding: 0 20rpx;

  .title {
    color: #333;
    font-size: 30rpx;
    text-align: center;
    margin-top: 94rpx;
  }

  .step {
    .step-item {
      width: 100%;
      height: 188rpx;
      margin-top: 20rpx;

      &-img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .btn {
    margin-top: 40rpx;
  }
}
</style>