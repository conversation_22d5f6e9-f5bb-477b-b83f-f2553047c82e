<template>
    <view>
        <view class="content">
            <view class="content-box">
                <view class="device">
                    <view class="title-box">
                        <view class="title">投放设备</view>
                        <!-- <view class="select" @click="go">选择设备</view> -->
                    </view>
                    <view class="device-list">
                        <view class="device-list-item" v-for="(item, index) in deviceSnList" :key="index">
                            <view class="device-list-item-left">{{ item.device_sn }}</view>
                            <view class="device-list-item-right" @click="delDevice(index)">
                                <BaseIcon name="close-circle" size="20" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投放方式：轮播广告</view>
            </view>
            <view class="content-box">
                <view class="content-box-title">投放开始日期</view>
                <view class="input-box" @click="showCalendar = !showCalendar">
                    <input v-model="dateVal" type="text" :disabled="true" placeholder="请选择投放日期" />
                </view>
                <!-- 日期组件 -->
                <u-calendar :show="showCalendar" mode="range" :min-date="minDate" :defaultDate="[minDate]"
                    @confirm="changeCalendar" @close="showCalendar = false">
                </u-calendar>
            </view>
            <view class="content-box">
                <view class="content-box-title">广告图片</view>
                <view class="input-box">
                    <BaseUpload width="150" :maxCount="1" :fileListUpload="uploadUrlList" @onUpload="onUpload" >
                        点击上传图片
                    </BaseUpload>
                </view>
            </view>
            <view class="content-box">
                <view class="content-box-title">广告链接</view>
                <view class="input-box">
                    <input v-model="url_link" type="text" :disabled="false" placeholder="请输入广告链接" />
                </view>
            </view>
            <view class="content-box">
                <view class="content-box-title">广告名称</view>
                <view class="input-box">
                    <input v-model="advertise_name" type="text" :disabled="false" placeholder="请输入广告名称" />
                </view>
            </view>
        </view>

        <view class="fixed-btn flexRowBetween" style="
        paddingBottom: calc(20rpx + constant(safe-area-inset-bottom)); 
        paddingBottom: calc(20rpx + env(safe-area-inset-bottom)); 
        ">
            <view class="total">
                <view class="total-box">
                    <view>合计：</view>
                    <view class="total-box-mark">￥</view>
                    <view class="total-box-price">{{
                            (priceInfo.totalPrice || 0).toFixed(2)
                    }}</view>
                </view>
                <view class="total-num"> 共计{{ priceInfo.totalNum }}台设备 </view>
            </view>
            <view class="btn" @click="LaunchAd"> 确认提交 </view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <CommonAd :ad="vAd.advertiseVideoAd||''" type="video" />
        <!-- #endif -->
        <!-- 占位块 -->
        <SafeBlock height="140" />
        <LoginPopup />
    </view>
</template>

<script>
import BaseUpload from "@/components/base/BaseUpload";
import BaseButton from "@/components/base/BaseButton";
import { getAdPrice, decideAdTime } from "@/common/http/api";
import SafeBlock from "@/components/list/SafeBlock.vue";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { locationMixin } from "@/mixins/locationMixin"
import CommonAd from '../../components/WxAd/CommonAd.vue';
import LoginPopup from '../../components/LoginPopup.vue';

export default {
    components: { BaseUpload, BaseButton, SafeBlock, BaseIcon, CommonAd, LoginPopup },
    mixins: [locationMixin],
    data() {
        return {
            device_sn: "",
            showCalendar: false,
            dateInfo: [],
            dateVal: "",
            advertise_name: "",
            minDate: "",
            maxDate: "",
            uploadUrlList: [],
            selectDay: 0,
            selectRadioIndex: 0,
            adTotal: 0, //投放总价
            startDate: "", //开始时间戳
            endDate: "", //结束时间戳
            url_link: "", //广告连接
            deviceSnList: [],
        };
    },
    computed: {
        priceInfo() {
            let totalPrice = this.deviceSnList.reduce(
                (total, nowVal) =>
                    (parseFloat(total) * 1000 + parseFloat(nowVal.price) * 1000) / 1000,
                0
            );
            totalPrice = totalPrice * this.selectDay;
            let totalNum = this.deviceSnList.length;
            return {
                totalPrice,
                totalNum,
            };
        },
    },

    methods: {
        delDevice(index) {
            this.deviceSnList?.splice(index, 1);
        },
        go() {
            let url = "/pagesD/CommonPage/NearbyMerchants?from=ad"
            if (this.vCurLocation.longitude && this.vCurLocation.latitude) return uni.navigateTo({
                url,
            });
            // #ifdef MP-WEIXIN || MP-TOUTIAO
            this.initLocPermission(() => {
                this.getCurrentLocation(() => {
                    uni.navigateTo({
                        url,
                    });
                });
            });
            //#endif
            // #ifdef MP-ALIPAY
            uni.showModal({
                title: "温馨提示：",
                content: "需要授权您的位置信息,为您展示附近机器,是否授权?",
                success: ({ confirm }) => {
                    if (confirm) {
                        this.getCurrentLocation(() => {
                            uni.navigateTo({
                                url,
                            });
                        });
                    }
                },
            });

            //#endif

        },
        changeCalendar(e) {
            this.showCalendar = false;
            console.log("🚀 ~ e", e);
            this.dateInfo = e;

            this.startDate = new Date(e[0]).getTime();
            this.endDate = new Date(e[(e?.length || 1) - 1]).getTime();

            this.selectDay = (this.endDate - this.startDate) / 1000 / 60 / 60 / 24;
            this.dateVal =
                e[0] +
                " 至 " +
                e[(e?.length || 1) - 1] +
                " 共 " +
                this.selectDay +
                " 天";
            this.adTotal =
                this.selectDay *
                parseFloat(this.radioList?.[this.selectRadioIndex].price);
        },
        //投放广告

        LaunchAd() {


            if (!(this.deviceSnList?.length) > 0) return uni.showToast({ title: "请先选择设备~", icon: "none" });
            if (!this.uploadUrlList[0]?.url)
                return uni.showToast({ title: "请上传投放广告的图片~", icon: "none" });
            if (!this.advertise_name)
                return uni.showToast({ title: "请填写投放广告的名称~", icon: "none" });
            if (this.url_link && !uni.$u.test.url(this.url_link)) return this.isShowErr('请填写正确的链接或者不填写~')
            let list = this.deviceSnList?.map((el) => el.device_sn);
            let data = {
                start_date: this.dateInfo[0],
                end_date: this.dateInfo[(this.dateInfo?.length || 1) - 1],
                device_sn: list,
                type: 1,
            };
            decideAdTime(data).then((res) => {
                uni.setStorageSync("sub_use_time", JSON.stringify(res.date));

                uni.setStorageSync(
                    "sub_use_form",
                    JSON.stringify({
                        url: this.uploadUrlList[0]?.url,
                        url_link: this.url_link,
                        type: 1,
                        ad_name: this.advertise_name,
                        date_key: res.date_key,
                    })
                );
                uni.navigateTo({ url: `./AdConfirmTime` });
            });
        },
        onUpload(e) {
            this.uploadUrlList = e;
        },
        getAdPrice() {
            let data = { device_sn: this.device_sn };

            getAdPrice(data).then((res) => {
                let rtn = res.data?.[0];
                this.deviceSnList = [{ device_sn: this.device_sn, price: rtn?.price ?? 0 }];
            });
        },
        getLaterDay(day) {
            let minTimestamp = new Date().getTime();
            let maxTimestamp = minTimestamp + 1000 * 60 * 60 * 24 * day; //当前时间往后的1天
            this.minDate = this.$u.timeFormat(maxTimestamp, "yyyy-mm-dd");
            // console.log("🚀 ~ this.minDate", this.minDate);
            return this.minDate

        }
    },
    onLoad(opt) {
        this.device_sn = opt?.device_sn || this.vDeviceSn || '';
        this.device_sn && this.getAdPrice();
        this.getLaterDay(0)
    },
    onShow() {
        let pages = getCurrentPages();
        let currPage = pages[pages.length - 1]; // 当前页
        if (currPage.data.item) {
            this.deviceSnList.push(...currPage.data.item);
            currPage.data.item = "";
            console.log("🚀 ~  currPage.data.item", currPage.data.item);
        }
    },
};
</script>

<style lang="scss">
page {
    background-color: #fff;
}
</style>

<style lang="scss" scoped>
.content {
    padding: 30rpx;

    &-box {
        margin-bottom: 20rpx;

        &-title {
            color: $textBlack;
            font-size: $font-size-middle;
            font-weight: 700;
            margin-bottom: 20rpx;
        }

        .input-box {
            border-bottom: 2rpx solid $dividerColor;
        }
    }
}

.device {
    .title-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            color: $textBlack;
            font-size: $font-size-middle;
            font-weight: 700;
        }

        .select {
            color: $themeColor;
            font-size: $font-size-base;
        }
    }

    .device-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        &-item {
            width: 48%;
            display: flex;
            align-items: center;
            height: 76rpx;
            color: $textBlack;
            border: 2rpx solid $dividerColor;
            background-color: #ecf0f1;
            font-size: $font-size-base;
            margin-top: 20rpx;

            &-left {
                display: flex;
                justify-content: center;
                flex-direction: column;
                flex: 0.8;
                border-right: 2rpx solid $dividerColor;
                height: 100%;
                padding-left: 10rpx;
            }

            &-right {
                flex: 0.2;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}

.footer {
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: $uni-bg-color;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10000;
    display: flex;
    justify-content: space-between;

    .left {
        .total {
            color: $textBlack;
            font-size: $font-size-middle;
        }

        .price-box {
            display: flex;
            align-items: flex-end;
            color: red;
            font-weight: 700;

            :first-child {
                font-size: $font-size-small;
            }

            :last-child {
                font-size: $font-size-middle;
                margin-top: 10rpx;
            }
        }
    }

    .right {}
}

.fixed-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: $uni-bg-color;
    z-index: 99;

    .total {
        &-box {
            display: flex;
            align-items: flex-end;
            color: $textBlack;
            font-size: $font-size-base;

            &-mark {
                color: red;
                font-size: $font-size-xsmall;
            }

            &-price {
                color: red;
                font-size: $font-size-middle;
            }
        }

        &-num {
            color: $textDarkGray;
            font-size: $font-size-small;
        }
    }

    .btn {
        padding: 20rpx 50rpx;
        color: #fff;
        font-size: $font-size-xlarge;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
        border-radius: 20rpx;
    }
}

.red {
    color: red;
}
</style>
