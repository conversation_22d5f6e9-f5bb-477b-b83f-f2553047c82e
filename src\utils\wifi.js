

//BSSID: "f4:a5:9d:f0:f1:5a"
//SSID: "小汉科技HW—WIFI_Wi-Fi5"

const app = getApp().globalData

// const wifiInfo = {
//     SSID: "小汉科技HW—WIFI_Wi-Fi5",
//     password: "xhkjwtlwifi"
// }

/**
 * 操作步骤
 * 1.初始化wifi模块
 * 2.监听wifi连接 同时 获取wifi列表
 * 3.监听获取wifi列表数据事件回调
 * 4.查询wifi是否连接（是否连接到商户的wifi）
 * 5.已经连接商户wifi，则直接返回。否则开始连接商户wifi
 * 6.连接商户wifi
 */

var linkWifiInfo = undefined;
//初始化失败信息

const wifiErr = {
    12001: '当前系统不支持相关能力',
    12002: '密码错误',
    12003: '连接超时',
    12004: '重复连接 Wi-Fi',
    12005: '未打开 Wi-Fi 开关',
    12006: '未打开 GPS 定位开关',
    12007: '用户拒绝授权链接 Wi-Fi',
    12008: '无效 SSID',
    12009: '系统运营商配置拒绝连接 Wi-Fi',
    12010: '系统其他错误',
    12011: '应用在后台无法配置 Wi-Fi',
    12013: '系统保存的 Wi-Fi 配置过期，建议忘记 Wi-Fi 后重试',
    12014: '无效的 WEP / WPA 密码',
}
const showToast = (title, isSuccess) => {
    uni.showToast({
        title,
        icon: isSuccess ? 'success' : 'none',
        duration: 2000
    })
}
const loading = (title, isShowLoading) => {
    if (isShowLoading) {
        uni.showLoading({
            title,
            mask: true
        })
    } else {
        uni.hideLoading()
    }

}
const setEmit = (connected) => {
    getApp().globalData.wifiConnected = connected
    uni.$emit('event_wifi_connected', {
        connected
    })
}
//初始化wifi模块
const startWifi = () => {
    loading("正在初始化", true)
    stopWifi();
    wx.startWifi({
        success: async (res) => {
            loading("初始化成功", true)
            console.log("🚀 ~ res 初始化模块成功", res)
            onGetWifiList();//监听wifi连接
            getWifiList();//获取wifi列表
        },
        fail: (err) => {
            loading();
            console.log("🚀 ~ err 初始化模块失败。。", err)
            showToast(wifiErr[err.errCode] || '初始化wifi模块失败', false)
        },

    })
}
//关闭 Wi-Fi 模块。
const stopWifi = () => {
    wx.stopWifi({
        success: (res) => {
            console.log("🚀 ~ res 关闭wifi模块成功", res)
        },
        fail: (err) => {
            console.log("🚀 ~ err 关闭wifi模块失败。。", err)
        }
    })
}
//监听连接上 Wi-Fi 的事件
const onWifiConnected = () => {
    // return new Promise((resolve, reject) => {
    //     wx.onWifiConnected((res) => {
    //         if (res.wifi.SSID === app.wifiInfo.wifi_name) {
    //             loading()
    //             showToast('Wi-Fi已连接', true)
    //             resolve(true)
    //             offWifiConnectedHandle();
    //         } else {
    //             reject(false)
    //         }
    //     })
    // })

    wx.onWifiConnected((res) => {
        if (res.wifi.SSID === app.wifiInfo.wifi_name) {
            loading()
            showToast('Wi-Fi已连接', true)
            offWifiConnectedHandle();
        }
    })
}
//取消监听连接上 Wi-Fi 的事件
const offWifiConnected = () => {
    wx.offWifiConnected()
}
//监听获取到 Wi-Fi 列表数据事件
const onGetWifiList = () => {
    const wifiInfo = {
        SSID: app.wifiInfo.wifi_name,
        password: app.wifiInfo.wifi_psd
    }
    loading("正在连接", true)
    wx.onGetWifiList(async (res) => {
        console.log("🚀 ~ wifiList  监听获取到 Wi-Fi回调", res)
        if (res.wifiList.length > 0) {
            await getConnectedWifi();
            if (linkWifiInfo?.SSID === wifiInfo?.SSID) {
                offWifiConnectedHandle();//关闭监听wifi
                console.log("🚀 ~ wifiList  读取wifi连接,已经连接到对应的wifi,无需重复连接", linkWifiInfo)
                showToast("wifi已连接", true)
                setEmit(true)
            } else {
                console.log("🚀 ~ wifiList  读取wifi连接,未连接wifi,开始连接", linkWifiInfo)
                let findWifiInfo = res?.wifiList?.find(item => item.SSID === wifiInfo?.SSID);
                console.log("🚀 ~ findWifiInfo", findWifiInfo)
                if (findWifiInfo?.SSID) {
                    loading("寻找到wifi,正在连接", true)
                    console.log("🚀 ~ wifiList  获取到需要连接的wifi", findWifiInfo)
                    const { SSID, BSSID } = findWifiInfo;
                    connectWifi(SSID, BSSID, wifiInfo?.password)
                } else {
                    loading();
                    showToast("未找到wifi", false)
                    setEmit(false)

                }
                // res?.wifiList?.forEach(item => {
                //     console.log("🚀 ~ SSID", SSID, BSSID)
                //     if (SSID === wifiInfo?.SSID) {
                //         console.log("🚀 ~ wifiList  获取到需要连接的wifi", item)
                //         connectWifi(SSID, BSSID, wifiInfo?.password)
                //     }
                // })
            }
        } else {
            loading()
            showToast("附近暂未检测到wifi", false)
            setEmit(false)
        }
    })
}
//取消监听获取到 Wi-Fi 列表数据事件。
const offGetWifiList = () => {
    wx.offGetWifiList()
}
//请求获取 Wi-Fi 列表。wifiList 数据会在 onGetWifiList 注册的回调中返回。 Android 调用前需要 用户授权 scope.userLocation。
//iOS 上将跳转到系统设置中的微信设置页，需要用户手动进入「无线局域网」设置页，并在系统扫描到设备后，小程序才能收到 onGetWifiList 回调。Android 不会跳转。
const getWifiList = () => {
    loading("正在获取wifi列表", true)
    wx.getWifiList({
        success: (res) => {
            loading("获取wifi列表成功", true)
            console.log("🚀 ~ res 获取wifi列表成功", res)
        },
        fail: (err) => {
            console.log("🚀 ~ err 获取wifi列表失败", err)
            loading()
            showToast(wifiErr[err.errCode] || '获取wifi列表失败', false)
        },
        complete: () => {
        }
    })
}
//获取已连接中的 Wi-Fi 信息
const getConnectedWifi = async () => {
    wx.getConnectedWifi({
        success: (res) => { },
        fail: (err) => { },
        complete: (res) => {
            linkWifiInfo = res?.wifi
            console.log("🚀 ~ res 获取已经连接的wifi信息成功", res)
        }
    })

    // let res = await wx.getConnectedWifi()
    // console.log("🚀 ~ res 获取已经连接的wifi信息成功", res)
    // return res?.wifi
}
//连接 Wi-Fi。若已知 Wi-Fi 信息，可以直接利用该接口连接。仅 Android 与 iOS 11 以上版本支持
const connectWifi = (SSID, BSSID, password) => {
    wx.connectWifi({
        SSID,
        BSSID,
        password,
        forceNewApi:true,
        success: (res) => {
            loading()
            console.log("🚀 ~ res 连接wifi成功", res)
            showToast("wifi连接成功", true)
            setEmit(true)
        },
        fail: (err) => {
            loading()
            console.log("🚀 ~ err 连接wifi失败。。。", err)
            showToast(wifiErr[err.errCode] || 'wifi连接失败', false)

            setEmit(false)
        },
        complete: () => {
            offWifiConnectedHandle()
        }
    })
}

//取消wifi监听
const offWifiConnectedHandle = () => {
    offGetWifiList();//取消监听获取到 Wi-Fi 列表数据事件
    offWifiConnected();//取消监听连接上 Wi-Fi 的事件
    stopWifi();//关闭 Wi-Fi 模块。
    console.log("🚀 ~ stopWifi  关闭wifi模块")
}


export { startWifi }

