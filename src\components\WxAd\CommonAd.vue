<template>
  <view class="common" v-if="ad">
    <!-- #ifdef MP-WEIXIN -->

    <!--banner广告位-->
    <ad v-if="type == 'banner'" :unit-id="ad" />
    <!--原生广告位-->
    <ad-custom v-else-if="type == 'custom'" :unit-id="ad" />
    <!--视频广告位-->
    <ad
      v-else-if="type == 'video'"
      :unit-id="ad"
      ad-type="video"
      ad-theme="white"
    ></ad>

    <!-- #endif -->
  </view>
</template>
<script>
export default {
  props: {
    type: { type: String, default: "" },
    ad: { type: String, default: "" },
  },
  data() {
    return {
      interstitialAd: null,
    }
  },

  methods: {},
  created() {
    /* #ifdef MP-WEIXIN */
    if (wx.createInterstitialAd && this.ad && this.type == "inter") {
      this.interstitialAd = wx.createInterstitialAd({
        adUnitId: this.ad,
      })
      this.interstitialAd.onLoad(() => {})
      this.interstitialAd.onError((err) => {})
      this.interstitialAd.onClose(() => {})
    }
    /* #endif */
  },
  mounted() {
    /* #ifdef MP-WEIXIN */
    if (this.interstitialAd && this.type == "inter") {
      this.interstitialAd.show().catch((err) => {
        // console.error(err)
      })
    }
    /* #endif */
  },
}
</script>

<style scoped lang="scss">
.common {
  background-color: white;
  padding: 0;
  width: 100%;
  /* #ifndef H5 */
  // height: 100%;
  /* #endif */
}
</style>
