<template>
  <view>
    <BaseTabs :list="typeNav" @change="onTabChangeType" />
    <ComList :loadingType="loadingType">
      <UserCouponCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
    <LoginPopup />
  </view>
</template>
<script>
import ComList from "../../components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import { getUserCoupons } from "@/common/http/api";
import UserCouponCard from "./UserCouponCard.vue";
import BaseTabs from "../../components/base/BaseTabs.vue";
import LoginPopup from "../../components/LoginPopup.vue";
export default {
  components: { ComList, UserCouponCard, BaseTabs, LoginPopup },
  mixins: [myPull()],
  data() {
    return {
      typeNav: [
        {
          name: "全部",
          status: -1,
        },
        {
          name: "未核销",
          status: 0,
        },
        {
          name: "已核销",
          status: 1,
        },
      ],
      curStatus: -1,
    };
  },

  methods: {
    onTabChangeType({ status }) {
      this.curStatus = status;
      this.refresh();
    },
    async getList(page, done) {
      let data = {
        page,
        limit: 10,
        is_write_off: this.curStatus,
      };
      const res = await getUserCoupons(data);
      done(res);
    },
  },
  onLoad() {
    this.refresh();
  },
};
</script>

<style   lang='scss'>
page {
  background-color: $pageBgColor;
}
</style>
<style scoped  lang='scss'>
</style>