<template>
  <!--支付方式弹窗-->
  <view class="popupContent">
    <view class="titleLine">
      <view class="title">订单列表</view>
    </view>

    <view class="payWayList">
      <block v-for="(item, i) in payWayList" :key="i">
        <view class="payWayItem" @click="onSelectPayWay(item, i)">
          <view class="nameWrap">
            <view class="sn_list ,sn_text">
              <text class="sn_line">{{ '订单编号：' }}{{ item.order_sn }}</text>
              <text class="sn_item">{{ '订单状态：' }}{{ fromOrderListTxt[item.this_status] }}</text>
              <text class="sn_line">{{ '订单时间：' }}{{ item.add_time }}</text>
              <text class="sn_item">{{ '订单金额：' }}{{ item.order_amount }}元</text>
            </view>
          </view>
          <image class="imgCheck" :src="imgChecked" v-if="curSelectPayWayId == i"></image>
          <image class="imgCheck" :src="imgUncheck" v-if="curSelectPayWayId != i"></image>
        </view>
      </block>
    </view>

    <view class="btn">
      <BaseButton :text="bt_text" @onClick="comfirmSelectPayWay" />
    </view>
  </view>
</template>
  
<script>
import BaseButton from './base/BaseButton.vue'
export default {
  name: "PayWayPopup",
  components: { BaseButton },
  props: {
    isShowBalanceType: { type: Boolean, default: false },
    balance: { type: String | Number, default: 0, },
    payWayList: { type: Array, default: [] }
  },
  data() {
    return {
      imgChecked: require("@/static/public/ic_checked.png"),
      imgUncheck: require("@/static/public/ic_uncheck.png"),
      imgRecom: require("@/static/public/ic_pay_recom.png"),
      curSelectPayWayId: 0,
      gotoWellter: false,
      fromOrderListTxt: {
        1: '已完成',
        2: '订单异常',
        3: '订单已取消',
        4: '订单异常',
        50: '退款中',
        0: '未付款',
        60: '已出货',
        70: '已退款',
        60: '出货失败',
      },

    };
  },
  computed: {
    bt_text() {
      return '确认'
    },
  },
  methods: {
    onSelectPayWay(item, i) {
      this.curSelectPayWayId = i;
    },
    comfirmSelectPayWay() {
      if (this.payWayList[this.curSelectPayWayId].this_status == 0) {
        //
        return uni.showModal({
          title: '提示',
          content: '订单未完成，是否去购买',
          showCancel: true, // 是否显示取消按钮
          cancelText: '取消', // 取消按钮的文字
          confirmText: '确定', // 确定按钮的文字
          success: function (res) {
            if (res.confirm) {
              uni.navigateBack({
                delta: 1, // 后退的页面数，1表示后退一页，2表示后退两页，以此类推
                success: function () {
                  console.log('后退成功');
                },
                fail: function (err) {
                  console.log('后退失败', err);
                }
              });
            } else if (res.cancel) {

            }
          }
        });
        // 在某个事件触发时执行后退操作，比如点击返回按钮
        // uni.navigateBack({
        //   delta: 1, // 后退的页面数，1表示后退一页，2表示后退两页，以此类推
        //   success: function () {
        //     console.log('后退成功');
        //   },
        //   fail: function (err) {
        //     console.log('后退失败', err);
        //   }
        // });
      } else {
        this.$emit('changeItem', this.payWayList[this.curSelectPayWayId])
      }
    },
  },

  mounted() {
    this.curSelectPayWayId = 0
  },
};
</script>
  
<style scoped lang="scss">
.vip_stain_img {
  width: 10rpx;
  height: 15rpx;
}

.sn_text {
  font-size: 20rpx;
}

.flex {
  display: flex;
  align-items: center;
}

.sn_list {
  // display: flex;
  // flex-direction: column;
  display: flex;
  flex-wrap: wrap;
}

.sn_item {
  width: 180rpx;
  // margin: 10rpx 0;
}

.sn_line {
  width: 50%;
}

.flex_end {
  margin-left: 5rpx;
}

.vip_price {
  color: white;
  background-color: #dc3d29;
  font-size: 16rpx;
  font-weight: bold;
  padding: 5rpx 20rpx;
  border-radius: 10rpx;

}

.popupContent {
  background-color: white;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  padding: 80rpx 50rpx 50rpx 50rpx;

  .titleLine {
    @include flexRowVertCenter();

    .title {
      font-size: $font-size-xxxlarge;
      color: $textBlack;
      font-weight: bold;
    }

    .close {
      margin-left: auto;
      width: 26rpx;
      height: 27rpx;
    }
  }

  .payWayList {
    margin-top: 40rpx;
    height: 400rpx;
    overflow-y: scroll;

    .payWayItem {
      @include flexRowVertCenter();
      padding: 20rpx 0;
      border-bottom: 1rpx solid $dividerColor;

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(2) {
        .icon {
          width: 59rpx;
          height: 48rpx;
        }
      }

      // .iconWeixin {
      //   width: 53rpx;
      //   height: 51rpx;
      // }
      // .iconAlipay {
      //   width: 50rpx;
      //   height: 50rpx;
      // }
      .iconRecom {
        width: 44rpx;
        height: 22rpx;
      }

      .nameWrap {
        // display: flex;
        // align-items: flex-end;

        .name {
          font-size: $font-size-xlarge;
          color: $textBlack;
          margin-left: 30rpx;
        }

        .remain {
          font-size: $font-size-xsmall;
          margin-left: 10rpx;
          color: $textGray;
        }
      }

      .imgCheck {
        margin-left: auto;
        width: 50rpx;
        height: 36rpx;
      }
    }
  }

}

.btn {
  margin: 40rpx 0;
}
</style>
  