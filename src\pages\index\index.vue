<template>
  <view class="container">
    <!-- #ifndef H5 -->
    <BaseNavbar :title="vAppName || ''" :isShowSlot="true" :bold="true">
      <image class="user-info-img" :src="vMemberInfo.headimgurl || '/static/icon/logo.png'" @click="gpProfile" />
    </BaseNavbar>
    <!-- #endif -->
    <!-- 充电中的订单悬浮窗 -->
    <view class="chargeOrder" :style="{ bottom: 250 * 2 + 190 + 'rpx' }"
      v-if="isChargeOrderShow">
      <view class="chargeBt flexRowVertCenter">
        <view class="left flexRowVertCenter">
          <image class="chargeIcon" src="../../static/icon/public/running.gif" />
          <view class="Timing">
            <u-count-down :time="remain*60*1000" @finish="finish" format="HH:mm:ss" ></u-count-down>
          </view>
        </view>
        <view class="right">
          总时长：{{
        toHourMinute(
          Math.round(
            orderInfo.length_time
          )
        )
      }}
        </view>
      </view>
      <view class="chargeTop flexRowVertCenter">
        <view class="left flexRowVertCenter">
          <view class="title">正在使用中</view>
          <image class="chargeLoading" src="../../static/icon/public/chargeOrder.gif" />
        </view>
        <view class="right flexRowAllCenter" @click="gotoChargeDetails">查看详情</view>
      </view>
      <image class="chargeClose" src="../../static/icon/public/ic_card_close.png" @click="onMapTap" />
    </view>
    <BaseTabs :list="productArrTab" :isScroll="true" @change="changeTabs" />
    <MapBox :selectProductTab="selectProductTab" @onMark="onMark" />
    <view class="bottom" style="
        bottom: calc(40rpx + constant(safe-area-inset-bottom));
        bottom: calc(40rpx + env(safe-area-inset-bottom));
      " v-if="isShowTabbar">
      <Tabbar />

      <!-- <BaseTransition :show.sync="isShowPointPlace">
        <PointPlaceCard :info="selectPointPlaceInfo" />
      </BaseTransition> -->
    </view>
    <view class="bottom" style="
        bottom: calc(240rpx + constant(safe-area-inset-bottom));
        bottom: calc(240rpx + env(safe-area-inset-bottom));
      " v-if="isShowPointPlace">
      <!-- <BaseTransition :show.sync="isShowTabbar">
        <Tabbar />
      </BaseTransition> -->

      <PointPlaceCard :info="selectPointPlaceInfo" />
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <CommonAd :ad="vAd.ScreenAdHomeEarth || ''" type="inter" />
    <!-- #endif -->
    <!-- <button @click="go()">跳转</button> -->
  </view>
</template>

<script>
import BaseNavbar from '../../components/base/BaseNavbar.vue'
import MapBox from './components/MapBox.vue'
import { getProduct, getUserRechargeOrderList } from '@/common/http/api'
import BaseTabs from '../../components/base/BaseTabs.vue'
import Tabbar from './components/Tabbar.vue'
import PointPlaceCard from '../../components/PointPlaceCard.vue'
import BaseTransition from '../../components/base/BaseTransition.vue'
import CommonAd from '../../components/WxAd/CommonAd.vue'
import { chargeStatus } from "@/mixins/chargeStatus.js";


export default {
  components: {
    BaseNavbar,
    MapBox,
    BaseTabs,
    Tabbar,
    PointPlaceCard,
    BaseTransition,
    CommonAd,
  },
  data() {
    return {
      title: 'Hello',
      productList: [], //产品列表
      productArrTab: [],
      selectProductTab: {}, //选中的tab
      isShowPointPlace: false, //是否显示位置信息card
      isShowTabbar: true, //是否显示底部tabbar
      selectPointPlaceInfo: {}, //选择的位置信息
      bShowPointPlaceInfo: false, //是否显示位置信息card
      isChargeOrderShow: false, //是否显示订单
      momentTime: ['11', '10', '10'], //时间
      // orderInfo: { charge_sn: "6337008562858524", prom_type: 8, rule_unit: 2, duration: 5, create_time: '12345', charge_status: 2 }, //订单信息
      orderInfo: {}, //订单信息
      chargeStatus: 3, //订单状态
      remain: 0, //剩余时间
      timeData: {}, //倒计时时间


    }
  },
  mixins: [chargeStatus],
  methods: {
    finish(){
      this.isChargeOrderShow=false
    },
    gotoChargeDetails() {
      console.log("🚀 ~ this.orderInfo.charge_sn", this.orderInfo);
      if (this.orderInfo.prom_type == 8) {
        uni.navigateTo({
          url: `/pagesB/product/perGame?order_sn=${this.orderInfo.order_sn}`,
        });
      } else {
        uni.navigateTo({
          url: `/pagesB/chargeDetails/gaming?order_sn=${this.orderInfo.order_sn}`,
        });
      }

    },
    //获取充电订单
    doGetUserRechargeOrderList() {
      let params = {
        page: 1,
        limit: 1,
        status: 2,
      };
      getUserRechargeOrderList(params).then((res) => {
        // console.log("🚀 ~ res", res,res?.length > 0);
        if (res?.length > 0) {
          this.orderInfo = res[0];
          // console.log('🚀 ~ this.orderInfo', this.orderInfo);
          let time = Date.now()
          time = time / 1000
          // this.orderInfo = { charge_sn: "6337008562858524", rule_unit: 2, duration: 5, create_time: time, charge_status: 4 },
          // this.oldTime = this.orderInfo.create_time * 1000;
          this.oldTime = this.orderInfo.start_time * 1000;
          let endTime = this.orderInfo.end_time * 1000;
          let nowTime = new Date().getTime(); //现在时间
          //套餐时间 - （ 现在时间 - 开始时间） = 剩余充电时间
          this.remain = ((endTime - nowTime) / 1000 / 60).toFixed(2);
          if(this.remain>0){
            this.isChargeOrderShow = true
          }else{
            this.isChargeOrderShow = false
          }
          // this.remainTime=22
          // console.log('时间',this.remain)
          this.chargeStatus = this.orderInfo.charge_status;
        }else{
          this.isChargeOrderShow = false
        }
      });
    },
    gpProfile() {
      uni.navigateTo({ url: '/pagesC/profile/Profile' })
    },
    onMark(flag, item) {
      this.isShowPointPlace = flag
      this.isShowTabbar = !flag
      this.selectPointPlaceInfo = item
    },
    changeTabs(i) {
      // console.log('🚀 ~ i', i)
      this.selectProductTab = i
    },
    //获取产品
    doGetProduct() {
      getProduct().then((res) => {
        this.productList = res || []
        this.productArrTab = res || []
        this.productArrTab.unshift({
          name: '全部',
          machine_type: '',
          id: '',
        })
      })
    },
    go() {
      const http = location.href.split('?')[1]
      uni.navigateTo({ url: `/pagesB/product/index?${http}` })
    }
  },
  onLoad(opt) {
    // console.log('pages/index/index onLoad()', opt)
    this.doGetProduct() //获取产品
    if (opt?.scanCodeStatus == 0) {
      uni.showModal({
        title: '提示',
        content: '您扫描的二维码不正确，请重新扫描',
        showCancel: true,
        success: ({ confirm, cancel }) => { },
      })
    } else {
    }

    /* #ifdef H5*/
    if (opt.from == 'H5') {
      return
    }
    let url1 = '/response/buy/index/device_sn/'
    let url2 = '/mini/index?vscode='
    let url3 = '/mini/index/?vscode='
    let text = location.href
    let match1 = text.includes(url1)
    let match2 = text.includes(url2)
    let match3 = text.includes(url3)
    // console.log('找到匹配的URL：', match2, match2, match3, text)
    if (match2 || match3) {
      // console.log('找到匹配的URL：')
      const http = location.href.split('?')[1].split('#/')[0]
      uni.navigateTo({ url: `/pagesB/product/index?${http}` })
    } else if (match1) {
      const http = location.href.split('/device_sn/')[1].split('#/')[0]
      uni.navigateTo({ url: `/pagesB/product/index?${http}` })
    } else {
      // console.log('未找到匹配的URL。')
    }
    /* #endif */

  },
  onShow() {
    this.userInfo = getApp().globalData.userInfo;
    this.doGetUserRechargeOrderList();
  },
  onHide() {
    this.clearChargeTimer(); //清理定时器
    this.chargeStatus = 3;
    // console.log("执行了清理定时器 首页");
  },
}
</script>

<style lang="scss">
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
::v-deep .u-count-down__text{
  font-size: 35rpx !important;
  color: #00b7ee !important;
}
.chargeOrder {
  position: fixed;
  width: 724rpx;
  height: 232rpx;
  left: 10rpx;
  right: 10rpx;
  top: 300rpx;
  background: #ffffff;
  box-shadow: 0px 3px 7px 0px rgba(32, 32, 32, 0.15);
  border-radius: 10px;
  box-sizing: border-box;
  padding: 0 27rpx;
  z-index: 9999;

  .chargeTop {
    justify-content: space-between;
    height: 116rpx;
    border-bottom: 2rpx solid #e5e5e5;

    .left {
      .title {
        color: $textBlack;
        font-size: $font-size-large;
        font-weight: 700;
      }

      .chargeLoading {
        width: 60rpx;
        height: 60rpx;
        margin-left: 20rpx;
      }
    }

    .right {
      box-sizing: border-box;
      width: 320rpx;
      height: 80rpx;
      border: 2rpx solid #00b7ee;
      border-radius: 40px;
      color: $textBlack;
    }
  }

  .chargeBt {
    height: 116rpx;
    justify-content: space-between;

    .left {
      .chargeIcon {
        width: 52rpx;
        height: 52rpx;
      }

      .Timing {
        color: #0eade2;
        font-size: $font-size-large;
        font-weight: 700;
        margin-left: 20rpx;
      }
    }

    .right {
      color: $textBlack;
      font-size: $font-size-small;
      font-weight: 700;
      margin-right: 120rpx;
    }
  }

  .chargeClose {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    width: 34rpx;
    height: 34rpx;
  }
}

.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.user-info-img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}

.bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  z-index: 99;
  margin: 0 30rpx;
}
</style>
