<template>
  <!--充值中心页面-->
  <view class="content">
    <RechargeCenterCard
      title="充值套餐"
      :valueList="rechargeBeanList"
      @onClickValueItem="onClickValueBean"
    />
    <view class="tipWrap" v-if="tips.length > 0">
      <view class="tipLine flexRowAllCenter">
        <view class="leftLine"></view>
        <view class="tip">温馨提示</view>
        <view class="rightLine"></view>
      </view>

      <view class="tipContent">
        <view v-for="item in tips" :key="item">{{ item }}</view>
      </view>
    </view>

    <view
      class="btn"
      style="
        bottom: calc(20rpx + constant(safe-area-inset-bottom));
        bottom: calc(20rpx + env(safe-area-inset-bottom));
      "
    >
      <BaseButton text="确认充值" shape="circle" @onClick="confirm" />
    </view>
    <LoginPopup />
  </view>
</template>

<script>
import RechargeCenterCard from '@/pagesD/wallet/components/RechargeCenterCard'
import { creatRechargeOrderAndPrepay, getPackage } from '@/common/http/api'
import BaseButton from '@/components/base/BaseButton.vue'
import LoginPopup from '@/components/LoginPopup.vue'
/* #ifdef H5 */
import wx from 'weixin-js-sdk'
/* #endif */
export default {
  name: 'index',
  components: { RechargeCenterCard, BaseButton, LoginPopup },
  data() {
    return {
      rechargeBeanList: [],
      selectBeanInfo: {}, //选中的充值金额
      tips: [],
    }
  },

  methods: {
    onClickValuePrice(e) {
      console.log('选择了充值金额：', e)
    },
    onClickValueBean(e) {
      console.log('选择了充值汉币：', e)
      this.selectBeanInfo = e
    },
    confirm() {
      this.requestWxPay()
    },
    requestWxPay() {
      let params = {
        amount: this.selectBeanInfo.money,
        device_sn: this.vDeviceSn, // 扫码的设备
      }
      creatRechargeOrderAndPrepay(params).then((res) => {
        console.log('🚀 ~ res', res)
        let payInfo = res.data
        //请求微信支付
        /* #ifndef H5 */
        uni.requestPayment({
          /* #ifdef MP-WEIXIN */
          provider: 'wxpay',
          timeStamp: payInfo.timeStamp,
          nonceStr: payInfo.nonceStr,
          package: payInfo.package,
          signType: payInfo.signType,
          paySign: payInfo.paySign,
          /* #endif */
          /* #ifdef MP-ALIPAY */
          provider: 'alipay',
          orderInfo: payInfo.trade_no,
          /* #endif */
          success(res) {
            console.log('支付成功', res)
            /* #ifdef MP-ALIPAY */
            if (res.resultCode != 9000)
              return uni.showToast({
                title: '支付失败',
                icon: 'none',
              })
            /* #endif */
            //跳转到订单列表页面
            uni.redirectTo({
              url: `/pagesD/wallet/consumeList`,
            })
          },
          fail(res) {
            console.log('支付失败', res)
            uni.showToast({
              title: '支付失败~',
              icon: 'none',
            })
          },
          complete(res) {
            console.log('支付complete', res)
            //clearInterval(that.sti);
            uni.$u.vuex('vMemberInfo', null) // 清空，重新获取用户信息，重新获取汉币
          },
        })
        /* #endif */
        /* #ifdef H5 */
        let ua = window.navigator.userAgent.toLowerCase()
        //判断是不是微信
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          // 微信
          console.log('微信浏览器')
          WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          {
            appId: this.vAppId, //公众号ID，由商户传入
            timeStamp: payInfo.timeStamp, //时间戳，自1970年以来的秒数
            nonceStr: payInfo.nonceStr, //随机串
            package: payInfo.package,
            signType: payInfo.signType, //微信签名方式：
            paySign: payInfo.paySign, //微信签名
          },
          function (res) {
            if (res.err_msg == 'get_brand_wcpay_request:ok') {
              console.log('支付成功', res)
              uni.redirectTo({
                url: `/pagesD/wallet/consumeList`,
              })
              // 使用以上方式判断前端返回,微信团队郑重提示：
              //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
            } else {
              uni.showToast({
                title: '支付失败~',
                icon: 'none',
              })
            }
          },
        )
        }//判断是不是支付宝
        else if (ua.match(/AlipayClient/i) == 'alipayclient') {
          //支付宝
          
        
        }
        
       
        /* #endif */
      })
    },
    doGetPackage() {
      let data = {
        type: 'recharge',
      }
      getPackage(data).then((res) => {
        this.rechargeBeanList = res?.data || []
        this.tips = res?.tips?.split('|') || []
        this.selectBeanInfo = this.rechargeBeanList[0]
      })
    },
  },
  onLoad() {
    this.doGetPackage()
  },
}
</script>

<style scoped lang="scss">
.content {
  position: relative;

  .adContainer {
    margin-top: 30rpx;

    wx-ad {
    }
  }

  .tipWrap {
    margin: 0 30rpx;
    margin-top: 20rpx;

    .tipLine {
      margin-bottom: 40rpx;

      .leftLine {
        width: 200rpx;
        height: 1rpx;
        background-color: $btnGray;
      }

      .tip {
        font-size: $font-size-middle;
        color: $textBlack;
        margin: 0 20rpx;
      }

      .rightLine {
        width: 200rpx;
        height: 1rpx;
        background-color: $btnGray;
      }
    }

    .tipContent {
      line-height: 2;
      color: $textDarkGray;
      font-size: $font-size-xsmall;
      white-space: pre-line;
    }
  }

  .btn {
    left: 30rpx;
    right: 30rpx;
    position: fixed;
    margin-top: 100rpx;
  }
}
</style>
