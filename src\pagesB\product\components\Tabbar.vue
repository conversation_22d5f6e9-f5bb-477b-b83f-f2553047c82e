<template>
  <view class="container" style="
      paddingbottom: constant(safe-area-inset-bottom);
      paddingbottom: env(safe-area-inset-bottom);
    ">
    <view class="tabbar">
      <view v-for="item in tabbar" :key="item.name" class="tabbar-item" @click="go(item.path)">
        <!-- <button
          open-type="contact"
          class="tabbar-item"
          v-if="item.name == '客服'"
        >
          <BaseIcon :name="item.icon" size="26" color="#333333" />
          <view class="name">{{ item.name }}</view>
        </button> -->
        <block>
          <BaseIcon :name="item.icon" size="26" color="#333333" />
          <view class="name">{{ item.name }}</view>
        </block>
      </view>
    </view>
  </view>
</template>
<script>
import BaseIcon from '@/components/base/BaseIcon.vue'
import { locationMixin } from "@/mixins/locationMixin";
export default {
  components: { BaseIcon },
  name: 'Tabbar',
  mixins: [locationMixin],
  data() {
    return {
      tabbar: [
        /* #ifndef H5 */
        { name: '首页', icon: 'home', path: '/pages/index/index' },
        /* #endif */
        /* #ifdef H5 */
        { name: '首页', icon: 'home', path: '/pages/index/index?from=H5' },
        /* #endif */

        // #ifdef MP-WEIXIN
        { name: '纪念时刻', icon: 'camera', path: '/pagesD/updataImge/updataImge' },
        // #endif
        {
          name: '附近商家',
          icon: 'map',
          path: '/pagesD/nearbyMerchants/index',
        },
        { name: '订单', icon: 'order', path: '/pagesB/order/Order' },
        { name: '我的', icon: 'account', path: '/pagesC/profile/Profile' },
      ],
    }
  },

  methods: {
    go(url) {
      if (!url) return
      if (url == '/pages/index/index') {
        uni.reLaunch({ url })
      } else if (url == '/pagesD/nearbyMerchants/index') {
        if (this.vCurLocation.longitude && this.vCurLocation.latitude)
          return uni.navigateTo({
            url,
          })
        // #ifdef MP-WEIXIN || MP-TOUTIAO
        this.initLocPermission(() => {
          this.getCurrentLocation(() => {
            uni.navigateTo({
              url,
            })
          })
        })
        //#endif
        // #ifdef MP-ALIPAY
        uni.showModal({
          title: '温馨提示：',
          content: '需要授权您的位置信息,为您展示附近机器,是否授权?',
          success: ({ confirm }) => {
            if (confirm) {
              this.getCurrentLocation(() => {
                uni.navigateTo({
                  url,
                })
              })
            }
          },
        })

        //#endif
      } else {
        uni.navigateTo({ url })
      }
    },
  },
}
</script>

<style lang="scss">
/*当button里面包含图文时，图文之间会有间距，消除间距的方式是首先对button采用flex竖向布局，然后将子元素的margin和padding设置为0*/
button {
  &:after {
    border: none;
  }

  .button[plain] {
    border: none;
  }

  &:first-child {
    margin-top: 0;
  }

  &:active {
    background-color: white;
  }

  // background-color: white;

  border-radius: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
<style scoped lang="scss">
.container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #efefef;
  border-top: 2rpx solid #a2a2a2;
  z-index: 10;
}

.tabbar {
  display: flex;
  justify-content: space-between;
  height: 100rpx;

  &-item {
    flex: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .name {
      font-size: $font-size-base;
      line-height: 1.2 !important;
    }
  }
}
</style>
