<template>
  <view
    class="control-panel"
    :class="{ 'speed-only': showOnlySpeed, 'steering-only': showOnlySteering }"
  >
    <view class="content">
      <view>
        <text class="left">总时长：{{ formatTime(totalTime) }}</text>
        <text class="right">剩余时长：{{ formatTime(remainingTime) }}</text>
      </view>
    </view>
    <!-- 挡位控制区 -->
    <view class="gear-panel">
      <view
        v-for="(gear, index) in gearLabels"
        :key="index"
        class="gear-btn"
        :class="{ active: currentGear === index + 1 }"
        @touchstart="gearPress(index + 1)"
        @touchend="gearRelease(index + 1)"
      >
        <view class="gear-label">{{ gear }}</view>
      </view>
    </view>
    <!-- <view class="speed-control-wrapper">
      <text>正在前进</text>
      <text>松开停止</text>
    </view> -->
  </view>
</template>

<script>
export default {
  props: {
    disabled: Boolean,
    totalTime: {
      type: Number,
      default: 0, // 总时长（秒）
    },
    remainingTime: {
      type: Number,
      default: 0, // 剩余时长（秒）
    },
  },
  data() {
    return {
      currentGear: 1, // 默认选中1档
      previousGear: 1, // 之前的档位，用于恢复
      testSpeed: 50, // 测试速度
      gearLabels: ["低速", "中速", "高速"], // 档位标签
      wheelAngle: 0,
      startAngle: 0,
      wheelCenter: { x: 0, y: 0 },
      isTouching: false,
      animationTimer: null,
    }
  },
  methods: {
    // 时间格式化方法
    formatTime(seconds) {
      console.log("🕐 formatTime 输入参数:", seconds, "类型:", typeof seconds)

      // 🎯 处理异常情况（null、undefined、NaN）
      if (seconds == null || isNaN(seconds)) {
        console.log("⚠️ 时间参数无效，返回默认值")
        return "00:00"
      }

      // 🎯 处理负数时间（显示为 00:00）
      if (seconds < 0) {
        console.log("⚠️ 时间参数为负数，返回默认值")
        return "00:00"
      }

      // 🎯 处理时间为0的情况（正常显示 00:00）
      if (seconds === 0) {
        console.log("✅ 时间为0，正常显示 00:00")
        return "00:00"
      }

      // 🎯 处理过大的时间（超过99分钟显示为99:59）
      if (seconds > 5999) {
        console.log("⚠️ 时间参数过大，返回最大值")
        return "99:59"
      }

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      const formatted = `${minutes
        .toString()
        .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`

      console.log(
        "✅ 格式化结果:",
        formatted,
        "分钟:",
        minutes,
        "秒:",
        remainingSeconds
      )
      return formatted
    },

    // 挡位按下
    gearPress(gear) {
      if (this.disabled) return
      // 🎯 不在按下时立即更新高亮状态，等待确认后再更新
      this.$emit("gear-press", gear)
    },

    // 挡位释放
    gearRelease(gear) {
      if (this.disabled) return
      this.$emit("gear-release", gear)
      // 🎯 档位释放时触发档位切换
      this.changeGear(gear)
    },

    // 挡位切换（保留兼容性）
    changeGear(gear) {
      if (this.disabled) return
      // 🎯 先保存之前的档位，以便可能需要恢复
      this.previousGear = this.currentGear
      // 🎯 不立即更新高亮状态，等待父组件确认后再更新
      this.$emit("gear-change", gear)
    },

    // 🎯 确认档位切换成功，更新高亮状态
    confirmGearChange(gear) {
      console.log(
        `🎯 confirmGearChange 被调用，目标档位: ${gear}，当前档位: ${this.currentGear}`
      )
      this.currentGear = gear
      console.log(`🎯 档位已更新为: ${this.currentGear}`)
    },

    // 🎯 重置档位到之前的状态（用于限制模式下恢复）
    resetGear() {
      console.log(
        `🎯 resetGear 被调用，当前档位: ${this.currentGear}, 之前档位: ${this.previousGear}`
      )
      if (this.previousGear !== undefined) {
        console.log(`🎯 恢复档位: ${this.previousGear}`)
        this.currentGear = this.previousGear
      } else {
        console.log(`⚠️ previousGear 未定义，无法恢复档位`)
      }
    },

    // 🎯 设置档位但不发送指令（用于状态查询响应后的档位同步）
    setGearWithoutCommand(gear) {
      console.log(
        `🎯 setGearWithoutCommand 被调用，目标档位: ${gear}，当前档位: ${this.currentGear}`
      )

      // 保存之前的档位
      this.previousGear = this.currentGear

      // 直接更新档位显示，不触发 gear-change 事件
      this.currentGear = gear

      console.log(`🎯 档位已静默更新为: ${this.currentGear}`)
    },

    // 初始化方向盘位置
    initWheelPosition() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this)
        query
          .select(".steering-wheel")
          .boundingClientRect((rect) => {
            if (rect) {
              this.wheelCenter = {
                x: rect.left + rect.width / 2,
                y: rect.top + rect.height / 2,
              }
              resolve()
            }
          })
          .exec()
      })
    },

    // 触摸开始
    async onTouchStart(e) {
      if (this.disabled) return

      this.stopAnimation()
      await this.initWheelPosition()

      const touch = e.touches[0]
      this.startAngle = this.calculateAngle(touch.clientX, touch.clientY)
      this.isTouching = true
      this.$emit("steer-start")
    },

    // 触摸移动
    onTouchMove(e) {
      if (!this.isTouching || !this.wheelCenter.x) return

      const touch = e.touches[0]
      const currentAngle = this.calculateAngle(touch.clientX, touch.clientY)
      let angleDiff = currentAngle - this.startAngle

      // 标准化角度差
      angleDiff = this.normalizeAngle(angleDiff)

      // 计算目标角度（灵敏度增强，使用正常方向）
      const targetAngle = angleDiff * (180 / Math.PI) * 2.5 // 去掉负号，使用正常方向
      this.wheelAngle = Math.min(Math.max(targetAngle, -90), 90)
      console.log(
        "🎯 SteeringWheel 角度:",
        this.wheelAngle,
        "angleDiff:",
        angleDiff
      )
      this.$emit("steer-change", this.wheelAngle)
    },

    // 触摸结束
    onTouchEnd() {
      if (!this.isTouching) return

      this.isTouching = false
      this.returnToCenter()
      this.$emit("steer-end")
    },

    // 计算角度
    calculateAngle(x, y) {
      return Math.atan2(y - this.wheelCenter.y, x - this.wheelCenter.x)
    },

    // 标准化角度
    normalizeAngle(angle) {
      while (angle > Math.PI) angle -= 2 * Math.PI
      while (angle < -Math.PI) angle += 2 * Math.PI
      return angle
    },

    // 回中动画
    returnToCenter() {
      const startAngle = this.wheelAngle
      const duration = 400
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用弹性缓动
        this.wheelAngle = startAngle * (1 - this.easeOutElastic(progress))
        // 🎯 修复：回中动画过程中不发送转向事件，避免误触发指令

        if (progress < 1) {
          this.animationTimer = setTimeout(animate, 16)
        } else {
          this.wheelAngle = 0
          this.animationTimer = null
          // 🎯 只在动画完成时发送一次回中事件
          this.$emit("steer-change", 0)
        }
      }

      this.animationTimer = setTimeout(animate, 16)
    },

    // 停止动画
    stopAnimation() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
    },

    // 弹性缓动函数
    easeOutElastic(t) {
      const p = 0.3
      return (
        Math.pow(2, -10 * t) * Math.sin(((t - p / 4) * (2 * Math.PI)) / p) + 1
      )
    },
  },
  mounted() {
    this.initWheelPosition()
    // 初始化时触发默认档位
    this.$emit("gear-change", this.currentGear)

    // 🎯 调试时长数据
    console.log("🎯 SteeringWheel 接收到的时长数据:")
    console.log("总时长:", this.totalTime, "类型:", typeof this.totalTime)
    console.log(
      "剩余时长:",
      this.remainingTime,
      "类型:",
      typeof this.remainingTime
    )
    console.log("格式化总时长:", this.formatTime(this.totalTime))
    console.log("格式化剩余时长:", this.formatTime(this.remainingTime))
  },

  // 🎯 添加watch监听props变化
  watch: {
    totalTime(newVal, oldVal) {
      console.log("🎯 totalTime 变化:", oldVal, "->", newVal)
    },
    remainingTime(newVal, oldVal) {
      console.log("🎯 remainingTime 变化:", oldVal, "->", newVal)
    },
  },
  beforeDestroy() {
    this.stopAnimation()
  },
}
</script>

<style lang="scss" scoped>
.speed-control-wrapper {
  font-weight: bold;
  color: white;
  font-size: 62rpx;
  letter-spacing: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content {
  // background-color: #ff0000;
  margin-bottom: 25rpx;
  font-weight: bold;
  font-size: 32rpx;
  .left {
    margin-right: 140rpx;
  }
}
.control-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 9;
}

/* 速度控制样式 */
.speed-control-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 15rpx;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8rpx;
  width: 50%; /* 只占一半位置 */
  justify-content: center;
}

.speed-input {
  width: 80rpx;
  height: 50rpx;
  background-color: rgba(128, 128, 128, 0.3);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  text-align: center;
  font-size: 25rpx;
  line-height: 48rpx;
  padding: 0 8rpx;
  margin: 0;
  box-sizing: border-box;
  outline: none;
  vertical-align: middle;

  /* 确保在所有状态下都垂直居中 */
  display: inline-block;

  /* 兼容性更好的垂直居中方法 */
  padding-top: 1rpx;
  padding-bottom: 1rpx;
}

.speed-input:focus {
  border-color: rgba(0, 255, 255, 1);
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.5);
}

.speed-unit {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  white-space: nowrap;
}

.speed-btn {
  width: 80rpx;
  height: 50rpx;
  background-color: rgba(0, 255, 255, 0.2);
  border: 1rpx solid rgba(0, 255, 255, 0.6);
  border-radius: 4rpx;
  color: white;
  font-size: 25rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10rpx rgba(0, 255, 255, 0.3);
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
}

.speed-btn:active {
  background-color: rgba(0, 255, 255, 0.4);
  box-shadow: 0 0 15rpx rgba(0, 255, 255, 0.5);
}

.gear-panel {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.gear-btn {
  /* 保持原有样式 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 130rpx;
  height: 80rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  background-color: rgba(128, 128, 128, 0.273);
  position: relative;
  margin: 0 25rpx;
  padding: 5rpx;

  /* 确保按钮内容在伪元素之上 */
  z-index: 1;
  /* 为伪元素定位 */
  overflow: hidden;
  /* 隐藏溢出效果 */
  border-radius: 12rpx;
  /* 可选圆角 */
  .gear-text {
    font-size: 30rpx;
  }
  /* 科幻发光边框 */
  box-shadow: 0 0 20rpx rgba(0, 255, 255, 0.3),
    inset 0 0 20rpx rgba(0, 255, 255, 0.3);
  border: 1rpx solid rgba(0, 255, 255, 0.6);

  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to bottom right,
      transparent 45%,
      rgba(0, 255, 255, 0.1) 50%,
      transparent 55%
    );
    transform: rotate(45deg);
    animation: shine 3s linear infinite;
  }

  .gear-label {
    font-size: 40rpx;
    font-weight: bold;
    color: #fff;
    z-index: 2;
  }

  &.active {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 25rpx rgba(0, 255, 255, 0.7);
    border-color: rgba(0, 255, 255, 0.9);

    .gear-label {
      color: #7df9ff;
      text-shadow: 0 0 10rpx rgba(125, 249, 255, 0.8);
    }
  }
}

.steering-wheel {
  width: 300rpx;
  height: 300rpx;
  position: relative;
  touch-action: none;

  .wheel-img {
    width: 100%;
    height: 100%;
  }

  .center-indicator {
    position: absolute;
    width: 20rpx;
    height: 20rpx;
    background: #ff0000;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
  }
}

@keyframes shine {
  0% {
    transform: rotate(45deg) translateX(-100%);
  }

  100% {
    transform: rotate(45deg) translateX(100%);
  }
}
</style>
