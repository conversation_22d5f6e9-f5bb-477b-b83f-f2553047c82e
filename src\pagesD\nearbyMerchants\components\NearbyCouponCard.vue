<template>
  <view class="card">
    <view class="card-left">
      <image class="card-left-img" src="/pagesD/static/coupon_bg.png" />
      <view class="card-left-main">
        <view class="card-left-box">
          <view class="card-left-box-unit">￥</view>
          <view class="card-left-box-price">{{ info.money }}</view>
        </view>
        <view class="card-left-title">优惠券</view>
      </view>
    </view>
    <view class="card-right">
      <view class="card-right-info">
        <view class="card-right-info-title">{{ info.name }}</view>
        <view class="card-right-info-valid">{{ info.descibe }}</view>
        <view class="card-right-info-name">{{ placeName }}</view>
      </view>
      <view class="card-right-box">
        <view class="card-right-box-remain">剩余{{ info.remain_num }}张</view>
        <view
          class="card-right-box-receive flexRowAllCenter"
          hover-class="card-right-box-click"
          @click="receiveHandle"
        >
          立即领取
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { receiveHotelCoupon } from "@/common/http/api.js";
export default {
  props: {
    info: { type: Object, default: {} },
    placeName: { type: String, default: "" },
  },
  data() {
    return {};
  },

  methods: {
    async receiveHandle() {
      let data = {
        coupon_id: this.info.id,
      };
      await receiveHotelCoupon(data);
      this.isShowSuccess("领取成功");
    },
  },
};
</script>


<style scoped  lang='scss'>
.card {
  height: 210rpx;
  display: flex;
  justify-content: space-between;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  &-left {
    flex: 0 0 315rpx;
    position: relative;
    &-img {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      border-radius: 10rpx 0 0 10rpx;
      z-index: 0;
    }
    &-main {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      z-index: 1;
    }
    &-box {
      display: flex;
      align-items: flex-end;

      &-unit {
        font-size: 30rpx;
      }
      &-price {
        font-size: 48rpx;
      }
    }
    &-title {
      font-size: 28rpx;
    }
  }
  &-right {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 35rpx 16rpx 28rpx 26rpx;
    box-sizing: border-box;
    > view {
      &:nth-child(n) {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    &-info {
      color: #757575;
      font-size: 16rpx;
      &-title {
        color: #5b5b5b;
        font-size: 32rpx;
        font-weight: 700;
      }
    }
    &-box {
      color: #757575;
      font-size: 24rpx;
      &-receive {
        width: 138rpx;
        height: 60rpx;
        color: #fff;
        font-size: 24rpx;
        background-color: #f66060;
        transition: all 0.3s;
      }
      &-click {
        opacity: 0.8;
        scale: 1;
      }
    }
  }
}
</style>