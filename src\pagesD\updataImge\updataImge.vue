<template>
  <view>
    <view class="img_box">
      <view class="img_zhe">
        <image src="../static/mbg.png" class="imag"></image>
      </view>

      <movable-area style="width: 100%; height: 1200rpx; overflow: hidden">
        <movable-view
          :x="x"
          :y="y"
          direction="all"
          @change="onChange"
          style="width: 100%; height: 1200rpx"
          :scale="true"
          @scale="scaleChange"
          :scale-min="minScale"
          :scale-max="maxScale"
          :scale-value="isIos ? 1 : currentScale"
          out-of-bounds
        >
          <image
            :src="tempImagePath"
            class="imag"
            style="width: 100%; height: 100%"
            @touchstart="onTouchStart"
            @click.stop="scaleValue"
          >
          </image>
        </movable-view>
      </movable-area>
    </view>
    <view>
      <view class="flex">
        <view class="flex_item">
          <view class="item_b"
            >{{ text1 || 0 }}{{ listItem.unit === 3 ? "分钟" : "" }}</view
          >
          <view class="item_g">{{ text2 }}</view>
        </view>
        <view class="line"> </view>

        <view class="flex_item">
          <view class="item_b">{{ text3 || "默认套餐" }}</view>
          <view class="item_g">{{ text4 }}</view>
        </view>
        <view class="line"> </view>
        <view class="flex_item">
          <view class="item_b">{{ text5 || 0 }}元</view>
          <view class="item_g">{{ text6 }}</view>
        </view>
      </view>
      <view class="flex buttom">
        <button class="btn" @click="toSelect">选择订单</button>
        <button class="btn" @click="takePhoto">拍照</button>
        <button class="btn" @click="upImg">本地图片</button>
        <button class="btn save" id="btn_img" @click="captureAndSave">
          保存图片
        </button>
      </view>
      <view v-if="!isIos" class="tip">双击缩小,双指点击放大</view>
    </view>
    <canvas
      class="canvs"
      canvas-id="bacbackground"
      ref="canvas"
      :style="'width:750rpx;height:1330rpx; bacbackground-color:#333'"
    >
    </canvas>
    <BasePopup :show.sync="isShowPopup">
      <OrderList :payWayList="payWayList" @changeItem="changeItem"></OrderList>
    </BasePopup>
  </view>
</template>

<script>
import BasePopup from "@/components/base/BasePopup.vue"
import OrderList from "@/components/OrderList.vue"
import { getOrderList } from "@/common/http/api"
export default {
  components: { BasePopup, OrderList },
  data() {
    return {
      tempImagePath: "", // 存储临时图片路径
      canvasWidth: 0, // 画布宽度
      canvasHeight: 0, // 画布高度
      img: null, // 图片对象
      scale: 0.9,
      translateX: 0, // X轴偏移
      translateY: 0, // Y轴偏移
      touchStart: { x: 0, y: 0 }, // 触摸起始点
      touchMove: { x: 0, y: 0 }, // 触摸移动点
      isTouching: false, // 是否正在触摸
      ctx: null, // canvas 2d 上下文
      backgroundImagePath: "https://your-cdn.com/images/mbg-optimized.webp", // 使用CDN优化版本
      pinch: false, //双指
      clear: 0,
      x: 0,
      y: 0,
      text1: "",
      text2: "运动时长",
      text3: "",
      text4: "运动名称",
      text5: "",
      text6: "运动金额",
      isShowPopup: false,
      payWayList: [],
      listItem: [],
      isIos: false,
      lastTapTime: 0,
      currentScale: 0.9,
      isDoubleTap: false,
      minScale: 0.5,
      maxScale: 5,
    }
  },
  methods: {
    onTouchStart(event) {
      // 判断触摸点的数量是否为两个
      if (event.touches.length === 2) {
        // 获取触摸点的坐标
        const touch1 = event.touches[0]
        const touch2 = event.touches[1]
        // 计算两个触摸点之间的距离
        const deltaX = touch1.clientX - touch2.clientX
        const deltaY = touch1.clientY - touch2.clientY
        const distance = Math.sqrt(deltaX ** 2 + deltaY ** 2)
        // 判断触摸点之间的距离是否在某个范围内，即是否为双指点击
        if (distance < 300) {
          console.log("双指点击事件触发")
          // 在这里处理双指点击事件的逻辑
          let scale = this.currentScale + 0.1
          if (scale > this.maxScale) {
            this.currentScale = this.maxScale
          } else {
            this.currentScale = scale
          }
        }
      } else {
        console.log("event", event)
      }
    },
    scaleValue(event) {
      // 获取当前点击时间
      console.log("event", event)
      const currentTime = new Date().getTime()
      // 如果当前点击时间与上次点击时间间隔小于500毫秒，则认为是双击事件
      if (currentTime - this.lastTapTime < 500) {
        // 处理双击事件的逻辑
        console.log("双击事件触发")
        // 重置上次点击时间
        this.lastTapTime = 0
        let scale = this.currentScale - 0.1
        if (scale < this.minScale) {
          this.currentScale = this.minScale
        } else {
          this.currentScale = scale
        }
        // 设置双击事件触发标志为 true
        this.isDoubleTap = true
      } else {
        this.lastTapTime = currentTime
      }
    },
    changeItem(item) {
      this.listItem = item
      console.log("listitem", this.listItem)
      this.isShowPopup = false
      this.text3 = item.goods_name
      this.text5 = item.order_amount
      this.text1 = item.outnum
    },
    getlist() {
      let data = {
        page: 1,
        limit: 10,
        status: 1,
      }
      getOrderList(data).then((res) => {
        this.payWayList = res.data
      })
    },
    toSelect() {
      this.getlist()
      this.isShowPopup = true
    },
    upImg() {
      // 选择本地图片
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]

          // 更新页面或其他操作
          this.tempImagePath = tempFilePath
          console.log("选择的本地图片路径：", tempFilePath)
        },
      })
    },
    onChange(event) {
      console.log("change", event)
      this.translateX = event.detail.x
      this.translateY = event.detail.y
    },
    scaleChange(event) {
      // console.log('scale', event)
      this.translateX = event.detail.x
      this.translateY = event.detail.y
      this.scale = event.detail.scale
    },
    takePhoto() {
      // 选择图片
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths
          // 将临时图片路径存储在 data 中
          this.tempImagePath = tempFilePaths[0]
        },
      })
    },
    initCanvas() {
      // 获取图片信息
      uni.getImageInfo({
        src: this.tempImagePath,
        success: (info) => {
          // 计算画布大小，使用画布的大小作为背景图片的大小
          this.canvasWidth = uni.upx2px(750) // 100upx
          this.canvasHeight = uni.upx2px(1200) // 1200upx
          // 初始化 canvas 上下文
          // 初始化画布
          this.drawCanvas()
        },
      })
    },
    drawCanvas() {
      //
      this.canvasWidth = uni.upx2px(750) // 100upx
      this.canvasHeight = uni.upx2px(1200) // 1200upx
      console.log("画布更新了")
      // 清空画布
      // this.ctx = null
      this.ctx = uni.createCanvasContext("bacbackground", this)
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

      //设置图片的位置和缩放
      this.ctx.translate(this.translateX, this.translateY)
      this.ctx.scale(this.scale, this.scale)
      // 旋转图片
      this.ctx.rotate((this.clear * Math.PI) / 180)

      //绘制拍照的图片
      this.ctx.drawImage(
        this.tempImagePath,
        0,
        0,
        this.canvasWidth,
        this.canvasHeight
      )
      // 重置变换矩阵，确保接下来的操作不影响之前绘制的图片
      this.ctx.setTransform(1, 0, 0, 1, 0, 0)

      // 绘制背景图片
      this.ctx.drawImage(
        this.backgroundImagePath,
        0,
        0,
        this.canvasWidth,
        this.canvasHeight
      )
      //
      this.ctx.setFillStyle("#ffffff")
      this.ctx.fillRect(
        0,
        this.canvasHeight,
        this.canvasWidth,
        this.canvasHeight + 200
      )
      //设置填充文字样式
      this.ctx.font = "16rpx bold Arial"
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#000"
      let min = this.listItem.unit === 3 ? "分钟" : ""
      this.ctx.fillText(
        this.text1 + min,
        (this.canvasWidth / 3) * 0.5,
        this.canvasHeight + 30
      )
      this.ctx.font = "12rpx  bold "
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#9a9a9a"
      this.ctx.fillText(
        this.text2,
        (this.canvasWidth / 3) * 0.5,
        this.canvasHeight + 50
      )
      //绘制|
      // 指定开始绘制的起始地点
      this.ctx.moveTo(this.canvasWidth / 3, this.canvasHeight + 20)
      // 移动画笔至画布中坐标为 (200px, 200px) 的位置
      this.ctx.lineTo(this.canvasWidth / 3, this.canvasHeight + 45)
      // 开始对图形进行绘制
      this.ctx.stroke()

      //设置填充文字样式
      this.ctx.font = "16rpx  bold Arial"
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#000"
      this.ctx.fillText(
        this.text3,
        (this.canvasWidth / 3) * 1.5,
        this.canvasHeight + 30
      )
      this.ctx.font = "12rpx  bold "
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#9a9a9a"
      this.ctx.fillText(
        this.text4,
        (this.canvasWidth / 3) * 1.5,
        this.canvasHeight + 50
      )
      //绘制|
      // 指定开始绘制的起始地点
      this.ctx.moveTo((this.canvasWidth / 3) * 2, this.canvasHeight + 20)
      // 移动画笔至画布中坐标为 (200px, 200px) 的位置
      this.ctx.lineTo((this.canvasWidth / 3) * 2, this.canvasHeight + 45)
      // 开始对图形进行绘制
      this.ctx.stroke()
      //设置填充文字样式
      this.ctx.font = "16rpx  bold Arial"
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#000"
      this.ctx.fillText(
        this.text5 + "元",
        (this.canvasWidth / 3) * 2.5,
        this.canvasHeight + 30
      )
      this.ctx.font = "12rpx  bold "
      this.ctx.textAlign = "center"
      this.ctx.fillStyle = "#9a9a9a"
      this.ctx.fillText(
        this.text6,
        (this.canvasWidth / 3) * 2.5,
        this.canvasHeight + 50
      )

      // 将绘制的内容显示到画布上
      this.ctx.draw()
      setTimeout(() => {
        this.fopush()
      }, 100)
    },
    captureAndSave(event) {
      if (this.tempImagePath) {
        if (this.listItem.order_status != 1) {
          return uni.showToast({
            title: "请选择已经完成的订单",
            icon: "none",
            duration: 2000,
          })
        } else {
          /* #ifdef MP-WEIXIN   */
          console.log("btn_img", event.target.id)
          wx.reportEvent("btn_img", {
            btn_img: 0,
            success: function (res) {
              console.log("上报事件成功", res)
            },
            fail: function (err) {
              console.error("上报事件失败", err)
            },
          })
          /* #endif */
          /* #ifdef MP-ALIPAY*/
          my.reportEvent("btn_img", {
            btn_img: 0,
            success: function (res) {
              console.log("上报事件成功", res)
            },
            fail: function (err) {
              console.error("上报事件失败", err)
            },
          })
          /* #endif */
          this.initCanvas()
        }
      } else {
        uni.showToast({
          title: "请先上传图片",
          icon: "none",
          duration: 2000,
        })
      }
    },
    fopush() {
      return new Promise((resolve, reject) => {
        // 将 canvas 截图生成临时文件路径
        uni.canvasToTempFilePath({
          canvasId: "bacbackground", // canvas-id
          success: (res) => {
            // 保存成功，res.tempFilePath 是保存到本地的路径
            const tempFilePath = res.tempFilePath

            // 保存到相册
            uni.saveImageToPhotosAlbum({
              filePath: tempFilePath,
              success: () => {
                uni.showToast({
                  title: "保存成功，请到相册查看",
                  icon: "none",
                  duration: 2000,
                })

                resolve(tempFilePath)
              },
              fail: (err) => {
                uni.showToast({
                  title: "保存失败",
                  icon: "none",
                  duration: 2000,
                })
                reject(err)
              },
            })
          },
          fail: (err) => {
            console.error(err)
            uni.showToast({
              title: "截图失败",
              icon: "none",
              duration: 2000,
            })
            reject(err)
          },
        })
      })
    },
  },
  onLoad(opt) {
    if (opt.form == "select") {
      this.backgroundImagePath = opt?.url
    }
    // this.drawCanvas()
    if (uni.getSystemInfoSync().platform === "ios") {
      this.isIos = true
      this.maxScale = 1
      this.minScale = 0.9
      this.scale = 1
      console.log("当前设备是iOS")
    } else {
      console.log("当前设备不是iOS")
    }
  },
}
</script>
<style lang="scss" scoped>
.img_box {
  height: 1200rpx;
  position: relative;
}

.canvs {
  position: absolute;
  top: 0;
  left: -1200rpx;
  // left: 0;
  // border: 2rpx solid red;
  pointer-events: none;
}

.btn_slider {
  width: 50%;
  display: flex;
  align-items: center;
}

.imag {
  width: 100%;
  height: 1200rpx;
}

.buttom_btn {
  display: flex;
  // margin: 10 30rpx;
  font-size: 35rpx;
  // border: 2rpx solid red;
  padding: 10rpx 30rpx;
}

::v-deep .u-number-box__plus {
  width: 80rpx !important;
}

/* 将 canvas 隐藏在页面外 */
// canvas {
//     position: absolute;
//     top: 0;
//     left: 0;

// }

.flex {
  margin-top: 10rpx;
  display: flex;
  justify-content: space-between;
  padding: 10rpx;
  height: 80rpx;
  align-items: center;

  .item_b {
    font-size: 30rpx;
    font-weight: bold;
  }

  .item_g {
    font-size: 25rpx;
    color: #9a9a9a;
  }

  .flex_item {
    width: 200rpx;
    text-align: center;
    padding: 10rpx;
  }

  .line {
    width: 2rpx;
    background-color: #333;
    height: 60rpx;
  }
}

.img_zhe {
  width: 100%;
  height: 1200rpx;
  position: absolute;

  top: 0;
  left: 0;
  z-index: 99;
  pointer-events: none;

  image {
    width: 100%;
    height: 100%;
  }
}

.tip {
  text-align: center;
  font-size: 25rpx;
  color: #9a9a9a;
}
.btn {
  font-size: 25rpx;
  width: 170rpx;
  border-radius: 50rpx;
  background: white;
  box-sizing: border-box;
  border: 2rpx solid #9a9a;
}
</style>
