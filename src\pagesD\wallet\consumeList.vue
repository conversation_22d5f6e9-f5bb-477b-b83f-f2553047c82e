<template>
  <!--消费明细页面-->
  <view>
    <!-- #ifndef H5 -->
    <BaseNavbar :title="`${vHName}明细`" />
    <!-- #endif -->
    <view class="content">
    <!-- <u-sticky> -->
      <view class="tab">
        <u-subsection
          :list="tablist"
          :current="current"
          mode="subsection"
          @change="changeTab"
        />
      </view>
    <!-- </u-sticky> -->
    <ComList :loadingType="loadingType">
      <ConsumeListCard v-for="item in listData" :key="item.id" :info="item" />
    </ComList>
    <LoginPopup />
  </view>
  </view>
  
</template>

<script>
import ConsumeListCard from "@/pagesD/wallet/components/ConsumeListCard";
import ComList from "@/components/list/ComList.vue";
import myPull from "@/mixins/myPull";
import { getMemberCashLog } from "@/common/http/api";
import LoginPopup from "@/components/LoginPopup.vue";
import BaseNavbar from '@/components/base/BaseNavbar.vue'
export default {
  name: "index",
  components: { ConsumeListCard, ComList, LoginPopup,BaseNavbar },
  mixins: [myPull()],
  data() {
    return {
      consumeType: 1, //1-余额明细 2-环保豆明细

      tablist: [
        { name: "全部", status: "" },
        { name: "充值", status: "充值" },
        { name: "消费", status: "消费" },
        { name: "退款", status: "退款" },
        // { name: "提现", status: "提现" },
      ],
      current: 0,
    };
  },
  methods: {
    changeTab(i) {
      this.current = i;
      this.refresh();
    },
    getList(page, done) {
      let data = {
        type: this.tablist[this.current]?.status,
        page,
        limit: 10,
      };
      getMemberCashLog(data).then((res) => {
        done(res);
      });
    },
  },
  onLoad(opt) {
    if (opt.type) {
      this.consumeType = parseInt(opt.type);
    }

    uni.setNavigationBarTitle({
      title: "汉币明细",
    });
    this.refresh();
  },
};
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>
<style scoped lang="scss">
.tab {
  padding: 20rpx;
  background-color: #fff;
}
</style>