<template>
  <view class="html">
    <view class="main">
      <view>
      </view>
      <view class="title">
        {{ '共享健身泡泡车' }}
      </view>
      <view v-if="!idShow"  class="min" @click="toNavgit()">
        请使用微信/支付宝扫码
      </view>
      <view class="goTo" @click="toNavgitWx()"  v-else>
        {{ baidu?'打开微信/支付宝':'打开小程序' }}
        
      </view>
     
      <view class="buttom">
        <img v-if="idShow" src="../static/icon/html_x.jpg" alt="" />
        <img v-else src="../static/icon/html_h5.jpg" alt="" />
      </view>
    </view>
    <BasePopup :show.sync="isShowPopup" :closeable="true">
      <view class="poput_box">
        <view class="item">
          <img src="@/static/public/ic_pay_weixin_theme.png" alt="" />
          <span>启动微信</span>
        </view>
        <view @click="gotoapliay()" class="item">
          <img src="@/static/public/ic_pay_ali_theme.png" alt="" />
          <span>启动支付宝</span>
        </view>
      </view>
    </BasePopup>
  </view>
</template>
<script>
import BasePopup from '../../components/base/BasePopup.vue'
import { getMiniSchema } from '@/common/http/api'
export default {
  components: { BasePopup },
  data() {
    return {
      isShowPopup: false,
      baidu:true,
      idShow:true,
      vscode:'',
      href:''
    }
  },

  methods: {
    toNavgit() {
      this.isShowPopup = true
    },
    toNavgitWx(){

      let href = this.href
      console.log('点击打开小程序',href)
      window.open(href)
    },
    gotoWixin() {
      let href = 'weixin://'
      window.open(href)
    },
    gotoapliay() {
      let href = 'alipayqr://platformapi/startapp?saId=10000007'
      window.open = href
    },
    getMiniSchemaList(vscode){
      let data={
        user_agent:'wx',
        vscode,
      }
      getMiniSchema(data).then(res=>{
        console.log('res',res)
        if(res){
          this.idShow=true
          this.href=res
          console.log(this.href)
        }else{
          this.idShow=false
        }
      })

    }
  },
  onLoad(opt) {
    let userAgent = navigator.userAgent
    if(userAgent.indexOf('Baidu') !== -1){
        console.log('百度浏览器')
        this.baidu=true
        return
    }else{
      this.baidu=false
    }
    console.log('opt',opt)
    if(opt){
      this.vscode=opt?.vscode
    }
    this.getMiniSchemaList(this.vscode)
  },
}
</script>

<style lang="scss">
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
.html {
  height: 100vh;
  box-sizing: border-box;
  .main {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 80rpx 40rpx;

  }
  .goTo {
    color: #fff;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('../static/icon/bj.png');
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: center;
    width: 450rpx;
    height: 220rpx;
    font-size: 38rpx;
  }
  
  .title {
    font-weight: bold;
    font-size: 50rpx;
  }
  .buttom {
    width: 100%;
    img {
      width: 100%;
    }
  }
}
.poput_box {
  height: 400rpx;
  padding: 60rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .item {
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
</style>
