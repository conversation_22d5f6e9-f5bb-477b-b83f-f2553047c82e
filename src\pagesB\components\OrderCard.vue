<template>
    <!--  订单列表项-->
    <view class="comContent" @click="gotoOrderDetail">
        <view class="topLine ">
            <view class="count">{{ itemInfo.add_time }}</view>
        </view>
        <view class="topLine ">
            <view class="leftNo">订单编号：{{ itemInfo.order_sn }}</view>
            <view class="rightStatus" :class="{ 'red': itemInfo.this_status != 0 }">{{ itemInfo.this_status === 0 ?
        "未付款" : itemInfo.prom_type == 8 && !itemInfo.end_time && [1].includes(itemInfo.this_status) ? '订单进行中' :
            '订单完成' }}</view>
        </view>


        <view class="bottomInfo">
            <view class="left">
                <image class="cover" :src="itemInfo.goods_img"></image>
            </view>

            <view class="rightInfo">
                <view class="name textMaxOneLine">
                    {{ itemInfo.goods_name }}
                </view>
                <block v-if="itemInfo.prom_type == 8">
                    <view class="count">
                        <span>计费时长:{{ itemInfo.outnum }}{{ itemInfo.unit === 3 ? "分钟 " : "" }}</span>
                        <span class="count">起步价:{{ itemInfo.pre_min_price }}元{{ ' ' }} </span>
                        <span> 起步时长:{{ itemInfo.pre_min_time }}{{ itemInfo.unit === 3 ? "分钟 " : "" }}</span>
                    </view>


                </block>
                <block v-else>
                    <view class="count">
                        <span>x{{ itemInfo.outnum }}{{ itemInfo.unit === 3 ? "分钟 " : "" }} </span>
                    </view>

                </block>


                <view class="count" v-if="itemInfo.prom_type == 8 && itemInfo.end_time">
                    超出时长:{{ itemInfo.realoutnum - itemInfo.pre_min_time > 0 ? itemInfo.realoutnum -
        itemInfo.pre_min_time
        : '0' }}
                    {{ itemInfo.unit === 3 ? "分钟" : "" }}
                    超出单价:{{ itemInfo.pre_per_minute }}元/分钟
                    超出总金额:{{ itemInfo.extra_amount }}元
                    预付金额：{{ itemInfo.total_amount }}元
                    退还金额: {{ itemInfo.back_amount }}元 </view>
                <view class="count">{{ orderType[itemInfo.prom_type] }}</view>
                <view class="priceLine ">
                    <!-- <view class="realPay ">
                        {{ itemInfo.this_status == 0 ? '未付款：' : itemInfo.prom_type == 8 && !itemInfo.end_time ? '预付：'
        : '实付：' }}
                    </view>
                    <view class="payValue">
                        <view class="value">{{ itemInfo.prom_type == 1 || itemInfo.prom_type == 8 ?
        itemInfo.order_amount
        : '0.00' }}</view>
                        <view class="unit">元</view>
                        <view class="small" v-if="itemInfo.prom_type == 8 && itemInfo.end_time">
                            (起步价{{ itemInfo.pre_min_price }}+超出总金额{{ itemInfo.extra_amount }})
                        </view>
                    </view> -->

                    <text>
                        {{ itemInfo.this_status == 0 ? '未付款：' : itemInfo.prom_type == 8 && !itemInfo.end_time ? '预付：'
        : '实付：' }}
                    </text>
                    <text style="color: red;font-size: 28rpx; font-weight: bold;">
                        {{ itemInfo.prom_type == 1 || itemInfo.prom_type == 8 ? itemInfo.order_amount : '0.00' }}
                    </text>
                    <text style="color: red; margin: 0 6rpx; font-weight: bold;">元</text>
                    <text v-if="itemInfo.prom_type == 8 && itemInfo.end_time" style="font-size: 21rpx;">
                        (起步价{{ itemInfo.pre_min_price }}+超出总金额{{ itemInfo.extra_amount }})
                    </text>
                </view>

            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "OrderCard",
    props: {
        itemInfo: Object,
    },

    data() {
        return {

            status: {
                1: '已完成',
                2: '订单异常',
                3: '订单已取消',
                4: '订单异常',
                50: '退款中',
                0: '未付款',
                60: '已出货',
                70: '已退款',
                80: '出货失败',
            },
            orderType: {
                1: '普通订单',
                2: '免费订单',
                3: '免费订单',
                5: '积分订单',
                8: '预售订单'

            }
        };
    },

    methods: {
        gotoOrderDetail() {
            if (this.itemInfo.prom_type == 8 && [1, 2, 3].includes(this.itemInfo.order_status)) {
                uni.navigateTo({
                    url: `/pagesB/product/perGame?order_sn=${this.itemInfo.order_sn}`,
                });

            } else {
                uni.navigateTo({
                    url: `/pagesB/order/OrderDetails?from=orderList&order_sn=${this.itemInfo.order_sn}`,
                });
            }

        },
    },
};
</script>

<style scoped lang="scss">
.comContent {
    background: white;
    box-shadow: 0px 3rpx 7rpx 0px rgba(32, 32, 32, 0.15);
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;

    .topLine {
        @include flexRowVertCenter();

        .leftNo {
            color: $textGray;
            font-size: $font-size-xsmall;
        }

        .rightStatus {
            margin-left: auto;
            font-size: $font-size-xsmall;
            // color: $mainRed;
        }

        .red {
            color: $mainRed;
        }
    }

    .count {
        margin-top: 3rpx;
        font-size: 19rpx;
        color: $textDarkGray;
    }

    .small {
        font-size: 19rpx;
        color: #666;
    }

    .bottomInfo {
        margin-top: 30rpx;
        display: flex;
        height: 210rpx;

        .left {
            width: 190rpx;
            height: 210rpx;
            border-radius: 10rpx;
            flex-shrink: 0;

            .cover {
                width: 100%;
                height: 100%;
                border-radius: 10rpx;
            }
        }


        .rightInfo {
            flex: 1;
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            height: 210rpx;
            overflow: hidden;

            .name {
                font-size: $font-size-xlarge;
                color: $textBlack;
                font-weight: bold;
            }



            .priceLine {
                @include flexRowVertCenter();
                margin-top: auto;
                font-size: $font-size-xsmall ;
                display: flex;
                align-items: flex-end;

                // .realPay {
                //     @include flexRowVertCenter();

                //     .label {
                //         font-size: $font-size-xsmall;
                //         color: $textDarkGray;
                //     }

                //     .payValue {
                //         margin-left: 0rpx;
                //         color: $mainRed;
                //         white-space: nowrap;
                //         display: flex;
                //         align-items: flex-end;

                //         .value {
                //             font-size: $font-size-xlarge;
                //             font-weight: bold;
                //         }

                //         .unit {
                //             font-size: $font-size-xsmall;
                //             margin-left: 8rpx;
                //             font-weight: bold;
                //             margin-bottom: 5rpx;
                //         }
                //     }
                // }

                // .btn {
                //     margin-left: auto;
                // }
            }
        }
    }
}
</style>