<template>
  <view class="content">
    <view class="top">
      <image class="img" :src="info.goods_img" />
      <view class="name textMaxOneLine">{{ info.goods_name }}</view>
    </view>
    <view class="bottom">
      <view class="price">
        <view>￥</view>
        <view>{{ info.good_price }}{{info.unit === 3 ? "元/" + info.game_time + "分钟": ""}}</view>
      </view>
      <view
        class="buy"
        hover-class="buy-active"
        :hover-start-time="0"
        :hover-stay-time="200"
        @click="buyHandle"
      >
        购买
      </view>
    </view>
    <view class="cargo-way">
      <view>货道</view>
      <view>{{
        String(info.num).length === 1 ? "0" + info.num : info.num
      }}</view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {};
  },

  methods: {
    buyHandle() {
      this.$emit("click");
    },
  },
};
</script>


<style scoped  lang='scss'>
.content {
  position: relative;
  height: 462rpx;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
}
.top {
  position: relative;
  height: 368rpx;
  .img {
    width: 100%;
    height: 100%;
  }
  .name {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    font-size: 24rpx;
    text-align: center;
    height: 58rpx;
    line-height: 58rpx;
    padding: 0 10rpx;
    background-color: rgba($color: #bebebe, $alpha: 0.7);
    z-index: 0;
  }
}
.bottom {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 94rpx;
  .price {
    display: flex;
    align-items: flex-end;
    color: #ff5d65;
    :first-child {
      font-size: 30rpx;
      margin:auto;
    }
    :last-child {
      font-size: 36rpx;
    }
  }
  .buy {
    width: 98rpx;
    height: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff5d65;
    border: 2rpx solid #ff5d65;
    border-radius: 10rpx;
  }
  .buy-active {
    opacity: 0.7;
  }
}
.cargo-way {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 74rpx;
  height: 36rpx;
  color: #c585ff;
  font-weight: 700;
  font-size: 20rpx;
  border-radius: 6rpx;
  border: 2rpx solid #c585ff;
  background-color: #fff;
}
</style>