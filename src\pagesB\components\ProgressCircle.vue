<template>
    <view class="progress-container">
        <view class="progress-circle">
            <view class="progress"></view>
            <view class="inner-circle">
                <text>{{ progress }}s</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'ProgressCircle',
    props: {
        // 接收的进度值，范围为 0 - 100
        progress: {
            type: Number,
            default: 0,
            validator: (value) => {
                return value >= 0 && value <= 100;
            }
        }
    }
};
</script>

<style scoped>
/* 整体容器样式 */
.progress-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    /* background-color: #f4f4f4; */
}

/* 圆形进度条外层容器 */
.progress-circle {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    background: conic-gradient(#e0e0e0 0deg, #e0e0e0 360deg);
}

/* 进度条部分样式 */
.progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(#007BFF 0deg, #00C6FF 360deg);
    transform-origin: center;
    /* 加快旋转速度，将持续时间改为 0.5 秒 */
    animation: spin 0.5s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 中间渐变色圆形部分 */
.inner-circle {
    position: absolute;
    top: 10rpx;
    left: 10rpx;
    width: 180rpx;
    height: 180rpx;
    border-radius: 50%;
    background: radial-gradient(#007BFF, #00C6FF);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;
    color: white;
}
</style>