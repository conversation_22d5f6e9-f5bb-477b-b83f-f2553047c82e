<template>
    <view class="content" @click="buyHandle">
        <!-- 顶部区域 -->
        <view class="cargo-way">
            <view class="way">{{ info.goods_name }}</view>
        </view>
        <view class="flex ented">
            <view class="price">
                
            </view>
        </view>
        <!-- 中间内容区域 -->
        <view class="flex btwn">
            <view class="name">
                <view class="pre_many">
                    金额：{{ info.pre_pay_money }}元
                </view>
            </view>

            <view class="buy" hover-class="buy-active" :hover-start-time="0" :hover-stay-time="200">
                预支付
            </view>
        </view>
        <view class="pre_btn">超出部分按{{ info.pre_per_minute }}元/分钟计算</view>
        <!-- </view> -->
    </view>
</template>
<script>
export default {
    props: {
        info: { type: Object, default: {} },
    },
    data() {
        return {};
    },

    methods: {
        buyHandle() {
            this.$emit("click");
        },
    },
};
</script>


<style scoped lang='scss'>
.pre_btn {
    margin-top: 10rpx;
    color: #ffffff;
    font-size: 25rpx;
}
.pre_many{
    color: orangered;
}

.content {
    position: relative;
    // height: 150rpx;
    border-radius: 10rpx;
    background-color: rgb(63, 149, 232);
    overflow: hidden;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
    border: 1px solid rgb(54, 89, 157);
    font-weight: 600;
    padding: 20rpx 30rpx;
}

.vip_price {
    // width: 200rpx;

    margin-left: 20rpx;
    height: 40rpx;
    background-image: url('../../static/btnbg.png');
    background-repeat: no-repeat;
    background-clip: content-box;
    background-position: center;
    background-size: 100%;
    color: #5c2700;
    display: flex;
    justify-content: center;
    align-items: center;

    .vip_text {
        font-size: 32rpx;
        font-weight: bold;
        padding: 5rpx 15rpx;
    }
}

.flex {
    display: flex;
}

.btwn {
    justify-content: space-between;
    align-items: center;
}

.ented {
    align-items: flex-end;
    margin: 10rpx 0;
}

.top {
    position: relative;
    height: 94rpx;
}

.hot {
    position: absolute;
    right: -66rpx;
    top: -22rpx;
    background-color: red;
    font-size: 28rpx;
    color: #fee425;
    padding: 40rpx 60rpx 10rpx 60rpx;
    transform: rotate(45deg);
}


.name {
    display: flex;
    box-sizing: border-box;
    // margin-top: 15rpx;
    // margin-left: 30rpx;
    width: 70%;
    height: 66rpx;
    // border: 1px solid rgb(39, 105, 178);
    overflow: hidden;
    border-radius: 10rpx;
    // background: rgb(255, 255, 255) url("../../static/sport/5.png") no-repeat top center;
    background-size: 100% 100%;
    z-index: 1;
    position: relative;

    .name_title {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        overflow: hidden;

        // background: url('../../static/sport/6.png') no-repeat top center;
        // background-size: 100% 100%;
        .title {
            width: 422rpx;
            height: 100%;
            background: url("../../static/sport/6.png") no-repeat;
            background-size: 121% 100%;
        }
    }
}

.price {
    display: flex;
    // align-items: flex-end;
    color: white;

    :first-child {
        font-size: 25rpx;
        margin: auto;
    }

    :last-child {
        font-size: 25rpx;
    }
}

.buy {
    width: 150rpx;
    height: 66rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    border: 2rpx solid rgb(83, 72, 146);
    background: rgb(253, 12, 53);
    border-radius: 10rpx;
    margin-right: 8rpx;
    // margin-top: 15rpx;
    box-sizing: border-box;
}

.buy-active {
    opacity: 0.7;
}

// }
.cargo-way {
    // position: absolute;
    // top: 12rpx;
    // left: 30rpx;
    display: flex;
    align-items: center;
    color: white;

    .way {
        width: 171rpx;
        background: rgb(22, 90, 195);
        border-radius: 10rpx;
        text-align: center;
        margin-right: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>