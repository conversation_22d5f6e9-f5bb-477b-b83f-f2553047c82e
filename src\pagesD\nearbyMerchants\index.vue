<template>
  <view>
    <BaseNavbar
      :isShowSlot="false"
      :bgColor="bgColor"
      color="#fff"
      title="附近商户"
      @leftClick="onClickHome"
    ></BaseNavbar>
    <view class="swiper">
      <view
        class="swiper-bg"
        :style="{
          backgroundImage: `linear-gradient(to bottom,${bgColor},#fff 90%)`,
        }"
      ></view>
      <view class="swiper-box">
        <u-swiper
          height="304rpx"
          :list="adInfoList"
          :indicator="true"
          :circular="true"
          bgColor="#fff"
          radius="24rpx"
          imgMode="scaleToFill"
          indicatorStyle="left: 30rpx;bottom:20rpx;"
          :loading="swiperLoading"
          @click="clickSwiper"
        ></u-swiper>
        <view class="notice">
          <u-notice-bar
            :text="noticeFilterList"
            direction="column"
            bgColor="#fff"
            fontSize="24rpx"
            color="#3f3f3f"
            @click="noticeClick"
          />
        </view>
      </view>
    </view>
    <view class="content">
      <view class="menu">
        <NearbyMoreMenu />
      </view>
    </view>
    <ComList :loadingType="loadingType">
      <view class="list-top-card">
        <view class="title-box">
          <view class="title-box-line"></view>
          <view class="title-box-text">
            <view>附近</view>
            <view class="recommend">推荐</view>
            <view>品牌</view>
          </view>
          <view class="title-box-line"></view>
        </view>
        <view class="sort-box">
          <view
            v-for="(item, index) in screenType"
            :key="item.name"
            :class="{ active: item.active }"
            @click="screenTypeHandle(index)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
      <NearbyMerchantsCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
      />
    </ComList>
  </view>
</template>
<script>
import BaseNavbar from '../../components/base/BaseNavbar.vue'
import { getNearHotels, getNearHotelAds } from '@/common/http/api'
import NearbyMoreMenu from './components/NearbyMoreMenu.vue'
import ComList from '../../components/list/ComList.vue'
import myPull from '@/mixins/myPull.js'
import { locationMixin } from '@/mixins/locationMixin'
import NearbyMerchantsCard from './components/NearbyMerchantsCard.vue'
export default {
  components: { BaseNavbar, NearbyMoreMenu, ComList, NearbyMerchantsCard },
  mixins: [locationMixin,myPull()],
  computed: {
    bgColor() {
      return this.vBaseInfo?.xcx_custom_theme_color || 'rgba(157, 70, 235,1)'
    },
    swiperLoading() {
      return (this.adInfoList?.length ?? 0) === 0
    },
  },
  data() {
    return {
      adInfoList: [],
      noticeList: [],
      noticeFilterList: [],
      screenType: [
        {
          name: '距您最近',
          status: 1,
          active: false,
        },
        {
          name: '推荐排序',
          status: 2,
          active: true,
        },
        {
          name: '人气优先',
          status: 3,
          active: false,
        },
      ],
      sort_type: 2,
    }
  },

  methods: {
    /* 获取定位 */
    getLocPermission() {
      // #ifdef MP-WEIXIN || MP-TOUTIAO
      this.initLocPermission(() => {
        this.getCurrentLocation(() => {})
      })
      //#endif
      // #ifdef MP-ALIPAY
      this.getCurrentLocation(() => {})
      //#endif
    },
    screenTypeHandle(index) {
      this.screenType = this.screenType?.map((item, i) => {
        item.active = i === index
        item.active && (this.sort_type = item.status)
        return item
      })
      this.refresh()
    },
    clickSwiper(i) {
      this.handleAdInfo(this.adInfoList[i])
    },
    noticeClick(i) {
      this.handleAdInfo(this.noticeList[i])
    },
    handleAdInfo(item) {
      if (item.file_type == 2) return //视频广告不跳转
      //小程序跳转广告
      if (item.wx_mp_appid && item.wx_mp_path)
        return wx.navigateToMiniProgram({
          appId: item.wx_mp_appid,
          path: item.wx_mp_path,
          success: (result) => {
            console.log('🚀 ~ result', result)
          },
        })
      //链接跳转
      let linkHref = item.img_href
      console.log('🚀 ~ linkHref', linkHref)

      if (!linkHref || linkHref == '#') return

      linkHref && this.goToWebView(linkHref)
    },

    getList(page, done) {
      let data = {
        page,
        limit: 10,
        lon: this.vCurLocation.longitude || 114.32168,
        lat: this.vCurLocation.latitude || 30.37559,
        radius: 1000,
        sort_type: this.sort_type,
      }
      getNearHotels(data).then((res) => {
        done(res)
        console.log('🚀 ~ res', res,)
      })
    },
    async getNearHotelAdsHandle() {
      const { carousel_ads, run_ads } = await getNearHotelAds()
      this.adInfoList =
        carousel_ads?.map((el) => {
          el.url = el.img_url
          return el
        }) || []

      this.noticeList = run_ads || []
      this.noticeFilterList = this.noticeList?.map((el) => el.content_text)
    },
  },
  onLoad() {
    this.getLocPermission()
    this.refresh()
    this.getNearHotelAdsHandle()
  },
}
</script>
<style lang="scss">
page {
  background-color: #f4f4f4;
}
</style>

<style scoped lang="scss">
.swiper {
  position: relative;
  padding: 10rpx 24rpx 0;
  background-color: #fff;
  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 250rpx;
    border-radius: 0 0 24rpx 24rpx;
  }
  &-box {
    border-radius: 24rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);

    .device-info {
      display: flex;
      justify-content: space-between;
      padding: 18rpx 25rpx;
    }
  }
}
.content {
  padding: 0 24rpx;
  margin-top: 20rpx;
  .menu {
    padding: 20rpx 0;
    border-radius: 24rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
  }
}
.list-top-card {
  padding: 50rpx 26rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
  .title-box {
    display: flex;
    justify-items: center;
    align-items: center;
    &-line {
      flex: 1;
      height: 2rpx;
      background-color: #c6c6c6;
    }
    &-text {
      flex: 1 0 auto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28rpx;
      color: #666666;
      .recommend {
        color: #e70e0d;
      }
    }
  }
  .sort-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 26rpx;
    color: #333;
    .active {
      font-weight: 700;
    }
  }
}
</style>
