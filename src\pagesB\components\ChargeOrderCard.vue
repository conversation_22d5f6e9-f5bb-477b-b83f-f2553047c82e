<template>
  <view class="card" @click="gotoOrderDetail">
    <view class="content border-bottom">
      <view class="info">
        <view class="goods_name flexRowBetween">
          <view class="name">无线充</view>
          <view class="order-time">{{
            $u.timeFormat(info.create_time * 1000, "yyyy-mm-dd hh:MM:ss")
          }}</view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>时长：</text><text class="num">{{ info.duration }}</text>
          </view>
          <view>{{ info.rule_unit == 2 ? "小时" : "分钟" }}</view>
        </view>
        <view class="flexRowBetween">
          <view>
            <text>价格：</text>
            <text class="price">￥{{ info.amount }}</text>
          </view>
          <view class="status flexRowAllCenter">
            <block v-if="info.charge_status == 4">
              <image
                v-if="info.charge_status == 4"
                class="status_icon"
                src="@/pagesB/static/icon/refund_icon.png"
              />
              订单退款
            </block>
            <block v-else>
              <image
                v-if="info.pay_status == 2"
                class="status_icon"
                src="@/pagesB/static/icon/finish_icon.png"
              />
              <view
                :style="{ color: info.pay_status == 2 ? '#0EADE2' : 'red' }"
              >
                <block v-if="info.prom_type === 2"> 免费订单 </block>
                <block v-else>
                  {{ pay_status[info.pay_status] }}
                </block>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="details">
      <view>
        <view>充电状态</view>
        <view class="details-charge-status">
          {{ charge_status[info.charge_status] }}
        </view>
      </view>
      <view v-if="info.pay_status == 2 || info.prom_type == 2">
        <view>充电时间</view>
        <view>
          <view>
            {{ $u.timeFormat(info.start_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
          </view>

          <view v-if="info.end_time > 0">
            {{ $u.timeFormat(info.end_time * 1000, "yyyy-mm-dd hh:MM:ss") }}
          </view>
        </view>
      </view>
      <block v-if="info.charge_status == 4">
        <view>
          <view>退款金额</view>
          <view class="price">{{ info.drawback_money }}</view>
        </view>
        <view>
          <view>退款时长</view>
          <view>{{ info.rest_time }}小时</view>
        </view>
      </block>

      <view>
        <view>商户名称</view>
        <view>{{ info.hotelName }}</view>
      </view>
      <view>
        <view>设备编号</view>
        <view>{{ info.device_sn }}</view>
      </view>
      <view>
        <view>订单编号</view>
        <view>{{ info.charge_sn }}</view>
      </view>
      <view>
        <view>支付方式</view>
        <view>{{ info.pay_name }}</view>
      </view>
      <view>
        <view>支付时间</view>
        <view>{{
          $u.timeFormat(info.pay_time * 1000, "yyyy-mm-dd hh:MM:ss")
        }}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "ChargeOrderCard",
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {
      pay_status: {
        1: "未支付",
        2: "支付成功",
      },
      charge_status: {
        1: "未充电",
        2: "充电中",
        3: "订单完成",
        4: "订单退款",
      },
    };
  },

  methods: {
    gotoOrderDetail() {
      if (this.info.charge_status !== 2) return;
      uni.navigateTo({
        url: `/pagesB/chargeDetails/index?charge_sn=${this.info.charge_sn}`,
      });
    },
  },
};
</script>




<style scoped lang="scss">
.card {
  padding: 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.card {
  padding: 20rpx;

  .border-bottom {
    border-bottom: 2rpx solid $dividerColor;
  }

  .content {
    .flexRowBetween {
      display: flex;
      justify-content: space-between;
    }

    box-sizing: content-box;
    display: flex;
    height: 150rpx;
    transition: all 0.4s;
    padding-bottom: 20rpx;

    .img {
      width: 150rpx;
      height: 100%;
      background: #bfbfbf;
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      font-size: $font-size-small;
      color: $textDarkGray;

      .goods_name {
        .name {
          color: $textBlack;
          font-size: $font-size-middle;
          font-weight: bold;
        }

        .status {
          color: $themeColor;
          font-size: $font-size-xsmall;

          .status_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 6rpx;
          }
        }
      }

      .num {
        color: $textBlack;
      }

      .price {
        color: #ef0000;
      }

      .status {
        color: $themeColor;

        .arrow {
          transition: all 0.4s;
          margin-left: 6rpx;
        }

        .rotate {
          transform: rotate(90deg);
        }
      }
    }
  }

  .status_icon {
    width: 30rpx;
    height: 30rpx;
    margin-right: 6rpx;
  }

  .details {
    &-charge-status {
      display: flex;
      align-items: center;
    }

    > view {
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      font-size: $font-size-base;

      > view {
        &:first-child {
          color: $textDarkGray;
        }

        &:last-child {
          color: $textBlack;
        }
      }

      .flex_white {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }
  }
}

.again-open {
  justify-content: flex-end !important;
}
</style>
