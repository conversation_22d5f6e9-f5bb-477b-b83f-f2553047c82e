/**
 * 玩具车蓝牙控制工具
 * 功能：提供玩具车蓝牙连接、指令发送、数据接收等功能
 * 协议：V1.10 玩具车蓝牙协议
 */

// 导入工具函数
import { generateData, getServiceUUIDs } from "../myutil/BLEUtil"  // BLE数据处理工具
import { ab2hex, inArray } from "../myutil/util"  // 处理转码工具函数
// 导入配置文件
import {
  DEFAULT_CONFIG,
  GEAR_SPEED_MAP,
  getCurrentConfig,
  saveConfig
} from './toyCarBleConfig'

// 玩具车蓝牙控制类
class ToyCarBleUnified {
  constructor() {
    // 🎯 使用配置文件中的默认配置
    this.config = getCurrentConfig()
    this.deviceAddress = 0x00  // 默认设备地址码 (用于指令生成)

    // 蓝牙状态
    this.isConnected = false  // 是否已连接
    this.isScanning = false  // 是否正在扫描设备
    this.isAdvertising = false  // 是否正在广播设备
    this.discoveryStarted = false  // 是否已开始扫描
    this.advertiseStart = false   // 是否已开始广播
    this.discoveryReady = false   // 是否已准备好扫描设备
    this.advertiseReady = false     // 是否已准备好广播设备
    this.isRebuilding = false       // 是否正在重建服务器

    // 设备信息 (统一使用配置文件)
    this.targetDeviceName = this.config.deviceName
    this.targetAddress = this.config.address
    this.connectedDevice = null

    // 数据管理
    this.devices = []
    this.receiveDataList = []
    this.filterName = this.config.deviceName  // 🎯 统一使用配置文件中的设备名称

    // 🎯 新架构：独立广播管理
    this.broadcastServers = new Map()  // 存储每个指令的独立服务器
    this.buttonStates = {
      forward: false,      // 前进按钮状态
      backward: false,     // 后退按钮状态
      left: false,         // 左转按钮状态
      right: false,        // 右转按钮状态
      brake: false,        // 刹车按钮状态
      parking: false,      // 驻车按钮状态
      gear1: false,        // 1档按钮状态
      gear2: false,        // 2档按钮状态
      gear3: false,        // 3档按钮状态
      customSpeed: false   // 自定义速度状态
    }

    // 🎯 指令映射表
    this.commandMap = {
      forward: { cmd: 0x10, data: 0x01, name: '前进' },
      backward: { cmd: 0x10, data: 0x02, name: '后退' },
      stop: { cmd: 0x10, data: 0x00, name: '停止' },
      brake: { cmd: 0x10, data: 0x03, name: '刹车' },
      left: { cmd: 0x14, data: 0x01, name: '左转' },
      right: { cmd: 0x14, data: 0x02, name: '右转' },
      stopTurn: { cmd: 0x14, data: 0x00, name: '停转' },
      parking: { cmd: 0x1A, data: 0x01, name: '驻车' },
      gear1: { cmd: 0x12, data: 0x1E, name: '1档' },
      gear2: { cmd: 0x12, data: 0x32, name: '2档' },
      gear3: { cmd: 0x12, data: 0x64, name: '3档' }
    }

    // 系统信息
    this.system = 'unknown'

    // 回调函数
    this.onConnectionChange = null
    this.onDataReceived = null
    this.onError = null
    this.onSendData = null  // 🎯 发送数据记录回调

    // 🎯 初始化状态管理
    this.isInitialized = false
    this.isInitializing = false

  }



  /**
   * 页面卸载时的清理函数
   * 停止所有蓝牙活动，释放资源
   */
  onNoneLoad() {
    // 🎯 新架构：停止所有独立广播服务器
    this.stopAllIndependentBroadcasts()
    this.stopBluetoothDevicesDiscovery()      // 停止设备扫描

    // 🎯 清理响应检测
    this.waitingForResponse = false
  }

  /**
   * 🎯 新架构：停止所有独立广播服务器
   */
  stopAllIndependentBroadcasts() {
    console.log('🛑 停止所有独立广播服务器')

    this.broadcastServers.forEach((serverInfo, commandKey) => {
      try {
        if (serverInfo.server && serverInfo.isActive) {
          serverInfo.server.stopAdvertising()
          serverInfo.isActive = false
          console.log(`✅ 停止指令 ${commandKey} 的广播`)
        }
        // 关闭服务器
        if (serverInfo.server) {
          serverInfo.server.close()
          console.log(`🔒 关闭指令 ${commandKey} 的服务器`)
        }
      } catch (error) {
        console.error(`❌ 停止指令 ${commandKey} 广播失败:`, error)
      }
    })

    // 清空服务器映射
    this.broadcastServers.clear()

    // 重置所有按钮状态
    Object.keys(this.buttonStates).forEach(key => {
      this.buttonStates[key] = false
    })

    console.log('🧹 所有独立广播已清理完成')
  }

  /**
   * 清空接收数据列表
   * 用户点击"清空"按钮时调用
   */
  clickClearResult() {
    // 通过回调通知页面清空数据
    if (this.onDataReceived) {
      this.onDataReceived('CLEAR_DATA')
    }
  }



  /**
   * 初始化系统信息
   * 检测当前运行平台（iOS/Android），影响BLE数据封装方式
   */
  initSystemInfo() {
    try {
      const { system } = wx.getSystemInfoSync()
      this.system = system
      console.log('🔧 检测到系统:', system)
    } catch (error) {
      console.error('❌ 获取系统信息失败:', error)
      this.system = 'Android'
    }
  }



  /**
   * 初始化设备发现适配器（接收功能） 查找设备和连接设备
   * 以中心设备模式打开蓝牙适配器，用于扫描和接收数据
   */
  initDiscoveryAdapter() {
    wx.openBluetoothAdapter({
      // mode: 'peripheral',  // 注释掉，使用默认的中心设备模式
      success: (res) => {
        console.log('initDiscoveryAdapter success', res)
        this.discoveryReady = true
        this.initBluetoothDevicesDiscovery()  // 初始化蓝牙成功后 初始化设备发现

        // 🎯 接收服务准备好后，检查是否需要恢复连接状态
        this.checkAndRestoreConnection()
      },
      fail: (res) => {
        console.log("initDiscoveryAdapter ble unavailable!", res)
        // 🍎 iOS特殊处理：如果失败，尝试重新初始化
        if (this.system && this.system.toLowerCase().indexOf('ios') >= 0) {
          console.log('🍎 iOS蓝牙发现适配器初始化失败，尝试重新初始化...')
          this.initDiscoveryAdapter()
        }
        // 可选：显示错误提示
        // wx.showModal({
        //   content:'初始化失败',
        //   cancelColor: 'cancelColor',
        // })
      }
    })
  }

  /**
   * 初始化广播适配器（发送功能）
   * 以外设模式打开蓝牙适配器，用于发送BLE广播
   */
  initAdvertiseAdapter() {
    wx.openBluetoothAdapter({
      mode: 'peripheral',  // 外设模式，用于广播数据
      success: (res) => {
        console.log('初始化广播适配器（发送功能）initAdvertiseAdapter 成功', res)
        this.createBLEPeripheralServer()  // 成功后创建BLE外设服务器
      },
      fail: (res) => {
        console.log("initAdvertiseAdapter ble unavailable!", res)
        // 可选：显示错误提示
        // wx.showModal({
        //   content:'初始化失败',
        //   cancelColor: 'cancelColor',
        // })
      }
    })
  }

  /**
   * 开始发送数据和发现设备
   * 用户点击"发送广播包"按钮时调用  发送指令，按钮获取输入数据
   * 主要流程：验证数据 → 保存数据 → 检查状态 → 生成BLE数据 → 开始广播
   **/
  startSendAndDiscovery() {
    console.log('🚀 startSendAndDiscovery 被调用')

    // 1. 获取并清理payload数据
    const payload = this.payload || ''
    const cleanedPayload = payload.replace(/\s+/g, '')
    console.log('📋 处理payload:', payload)

    // 2. 检查广播适配器是否就绪
    if (!this.advertiseReady) {
      console.log('⚠️ 蓝牙广播适配器未就绪，静默重新初始化')
      this.initAdvertiseAdapter()  // 静默重新初始化
      return
    }

    // 🎯 3. Android平台特殊检查：验证服务器是否真的存在
    if (!this.isIos && !this.checkServerStatus()) {
      console.log('🔄 Android平台检测到服务器丢失，重新创建服务器')
      this.createBLEPeripheralServer()
      // 等待服务器创建完成后重试
      setTimeout(() => {
        if (this.checkServerStatus()) {
          this.startSendAndDiscovery()
        } else {
          console.log('❌ 服务器重建失败')
        }
      }, 500)
      return
    }

    // 3. 确保有地址配置
    if (!wx.getStorageSync('address')) {
      console.log('🔧 设置默认地址:', this.config.address)
      wx.setStorageSync('address', this.config.address)
    }

    // 4. 检测系统平台并生成BLE数据包
    const systemStr = (this.system || '').toLowerCase()
    const isIos = systemStr.indexOf('ios') >= 0
    const actPayload = generateData(cleanedPayload, isIos)

    // 6. 停止之前的广播（如果有）
    if (this.advertiseStart) {
      console.log('🔄 停止之前的广播')
      this.stopAdvertising()
    }

    // 7. 开始BLE广播 需要到BLEUtil.js文件中的startAdvertising函数转换为小程序支持的格式
    this.isBroadcasting = true
    this.startAdvertising(actPayload)

    // 🎯 设置设备响应检测 (减少超时时间)
    this.setupResponseDetection(payload)

  }


  /**
   * 设置设备响应检测
   * 发送指令后自动检测设备是否响应
   * @param {string} sentCommand - 发送的指令
   */
  setupResponseDetection(sentCommand) {
    console.log('📤 指令发送:', sentCommand)

    // 🎯 发送数据记录现在在广播成功/失败时进行

    // 如果已经在等待响应，先清理之前的状态
    if (this.waitingForResponse) {
      console.log('🔄 清理之前的响应等待状态')
    }

    // 设置响应等待状态
    this.waitingForResponse = true
    this.sentCommand = sentCommand
    this.responseStartTime = Date.now()

    // 自动开启接收功能（如果未开启）
    if (!this.discoveryStarted) {
      console.log('🔍 设备发现未启动，开始启动')
      this.startDiscovery()
    } else {
      // 即使已经启动，也检查一下状态
      console.log('🔍 设备发现已启动，检查状态')
      this.checkDiscoveryStatus()
    }
  }

  /**
   * 检查设备发现状态
   */
  checkDiscoveryStatus() {
    wx.getBluetoothAdapterState({
      success: (res) => {
        console.log('🔍 蓝牙适配器状态检查:', res)
        if (!res.discovering) {
          console.log('⚠️ 设备发现已停止，重新启动')
          this.discoveryStarted = false
          this.startDiscovery()
        } else {
          console.log('✅ 设备发现正在运行')
        }
      },
      fail: (res) => {
        console.log('❌ 检查蓝牙状态失败:', res)
        // 如果检查失败，尝试重新启动发现
        this.discoveryStarted = false
        this.startDiscovery()
      }
    })
  }

  /**
   * 开始设备发现（接收按钮）
   * 用户点击"接收"按钮时调用  主要流程：检查状态 → 延迟后开始扫描 这里是返回数据
   */
  startDiscovery() {
    // 检查发现适配器是否就绪
    if (!this.discoveryReady) {
      console.log('❌ 接收适配器未就绪，无法开始扫描')
      return
    }

    // 立即开始扫描
    this.startBluetoothDevicesDiscovery()  // 开始扫描
  }
  /**
   * 初始化蓝牙设备发现功能  在此处初始化蓝牙设备发现功能 还没有查找
   * 配置设备扫描参数，但不立即开始扫描
   */
  initBluetoothDevicesDiscovery() {
    // 防止重复初始化
    if (this.discoveryStarted) {
      return
    }

    if (!this.discoveryStarted) {
      wx.startBluetoothDevicesDiscovery({  // 🎯 配置扫描参数
        allowDuplicatesKey: true,  // 允许重复上报同一设备，用于实时数据接收  实时接收
        powerLevel: "high",        // 高功率扫描，增加接收范围
        // services: ['11:22:33:44:55:66'],  // 可选：指定服务UUID过滤
        success: (res) => {
          console.log('startBluetoothDevicesDiscovery success! ', res)
          // 标记发现功能就绪 (工具类不需要setData)
        },
        fail: (res) => {
          console.log('startBluetoothDevicesDiscovery failed! ', res)
        }
      })
    }
  }

  /**
   * 开始蓝牙设备发现
   * 设置设备发现监听器并标记扫描状态
   */
  startBluetoothDevicesDiscovery() {
    // 防止重复启动
    // 如果已经启动过蓝牙设备发现，则直接返回
    if (this.discoveryStarted)
      return

    // 设置设备发现监听器，用来接收其他设备发送的数据 此处接收数据
    this.onBluetoothDeviceFound()

    // 标记扫描已开始
    this.discoveryStarted = true
  }

  /**
   * 停止蓝牙设备发现
   * 取消监听器并更新状态
   */
  stopBluetoothDevicesDiscovery() {
    // 检查是否正在扫描
    if (!this.discoveryStarted)
      return

    this.discoveryStarted = false  // 标记扫描已停止
    wx.offBluetoothDeviceFound()                 // 取消设备发现监听器
  }
  /**
   * 设备发现事件监听器
   * 当扫描到蓝牙设备时触发，处理接收到的数据
   */
  onBluetoothDeviceFound() {
    const filterName = this.filterName  // 获取设备名称过滤器

    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach((device) => {
        // 1. 设备名称过滤：只处理匹配filterName的设备
        if (!device.localName || device.localName != filterName) {
          return
        }

        // console.log(`🔗 发现设备: ${device.localName} (ID: ${device.deviceId})`)

        // 查找到对应的设备以后才进行处理 往下走，对输入的指令进行处理
        // 2. 更新设备列表（避免重复添加）
        const foundDevices = this.devices    // 新设备：在设备列表中没找到，添加到列表末尾
        const idx = inArray(foundDevices, 'deviceId', device.deviceId) // 检查设备是否已存在

        if (idx === -1) {
          // 新设备：在设备列表中没找到，添加到列表末尾
          this.devices.push(device)
        } else {
          // 已存在设备，更新信息
          this.devices[idx] = device
        }

        // 3. 提取并处理广播数据
        let hexData = ab2hex(device.advertisData)  // 转换为十六进制字符串
        hexData = hexData.substring(4)             // 去掉前4位（BLE头部）
        device.data = hexData

        // 🎯 检查是否是新数据（避免重复打印相同数据）
        const currentDataKey = `${device.deviceId}_${hexData}`
        const isNewData = !this.lastPrintedData || this.lastPrintedData !== currentDataKey

        if (isNewData) {
          this.lastPrintedData = currentDataKey
          console.log('=== 📦 新设备数据 ===')
          console.log('📱 设备:', device.localName)
          console.log('🆔 ID:', device.deviceId.substring(0, 8) + '...')
          console.log('🔋 信号:', device.RSSI, 'dBm')
          console.log('📊 数据:', hexData)
          console.log('⏰ 时间:', new Date().toLocaleTimeString())
          console.log('===================')
        }

        // 🎯 检查是否是对发送指令的响应
        if (this.waitingForResponse) {
          const responseTime = ((Date.now() - this.responseStartTime) / 1000).toFixed(1)
          this.waitingForResponse = false

          console.log('🎉 === 指令响应确认 ===')
          console.log('✅ 设备已接收并响应指令')
          console.log('📋 原始指令:', this.sentCommand)
          console.log('📨 设备响应:', hexData)
          console.log('⚡ 响应时间:', responseTime, '秒')
          console.log('🔗 通信状态: 正常')

          // 简单的响应分析
          this.analyzeDeviceResponse(hexData, this.sentCommand)

          console.log('========================')
        } else {
          // console.log('📡 这是设备主动发送的数据（非指令响应）')
        }

        // 4. 生成时间戳并格式化接收数据
        const myDate = new Date();
        const time = myDate.toLocaleTimeString() + " " + myDate.getMilliseconds()
        const receiveData = { time, data: hexData }

        // 5. 更新接收数据列表（限制最大100条，防止内存溢出）
        let lastDataList = this.receiveDataList // 获取当前接收数据列表
        if (lastDataList.length > 100) {
          lastDataList = lastDataList.slice(0, 100)  // 保留最新的100条
        }

        // 6. 🎯 检测设备状态异常（连续相同响应）
        this.checkDeviceStateAnomaly(hexData)

        // 7. 将新数据添加到列表开头（最新的在上面） 此处添加接收到的数据到列表中 显示到页面
        if (this.onDataReceived) {
          this.onDataReceived(receiveData)  // 🎯 传递完整的对象 {time, data}
        }
      })
    })
  }

  /**
   * 🎯 检测设备状态异常（连续相同响应）
   */
  checkDeviceStateAnomaly(responseData) {
    // 如果正在重置设备状态，跳过检测
    if (this.deviceResetInProgress) {
      return
    }

    // 🎯 简化响应检测，避免日志刷屏
    if (this.lastResponseData === responseData) {
      this.sameResponseCount++

      // 只在第一次检测到时记录日志
      if (this.sameResponseCount === 1) {
        console.log(`🔧 检测到重复响应，这是设备固件正常行为`)
      }

      // 每50次重置一次计数器，避免溢出
      if (this.sameResponseCount >= 50) {
        this.sameResponseCount = 0
      }
    } else {
      // 不同的响应，重置计数器
      this.sameResponseCount = 0
      this.lastResponseData = responseData
    }
  }



  /**
   * 分析设备响应数据
   * 简单分析响应数据的含义
   */
  analyzeDeviceResponse(responseData, originalCommand) {
    console.log('🔍 === 响应数据分析 ===')

    // 基本信息
    console.log('📊 响应数据长度:', responseData.length / 2, '字节')
    console.log('📋 原始指令长度:', originalCommand.replace(/\s+/g, '').length / 2, '字节')

    // 简单的响应模式识别
    if (responseData.length === 0) {
      console.log('❓ 空响应 - 设备可能只确认接收，无返回数据')
    } else if (responseData.startsWith('80')) {
      console.log('✅ 成功响应 - 设备确认指令执行成功')
    } else if (responseData.startsWith('FF')) {
      console.log('❌ 错误响应 - 设备报告指令执行失败')
    } else if (responseData.startsWith('81')) {
      console.log('📊 数据响应 - 设备返回查询数据')
      const dataValue = responseData.substring(2)
      console.log('📈 返回数据值:', dataValue)
    } else if (responseData === originalCommand.replace(/\s+/g, '').toLowerCase()) {
      console.log('🔄 回显响应 - 设备回显了发送的指令')
    } else {
      console.log('📡 自定义响应 - 设备返回自定义数据')
    }

    // 响应时间评估
    const responseTime = ((Date.now() - this.responseStartTime) / 1000).toFixed(1)
    if (responseTime < 1) {
      console.log('⚡ 响应速度: 极快 (<1秒)')
    } else if (responseTime < 3) {
      console.log('🚀 响应速度: 快速 (<3秒)')
    } else if (responseTime < 5) {
      console.log('🐌 响应速度: 一般 (<5秒)')
    } else {
      console.log('🐢 响应速度: 较慢 (>5秒)')
    }

    console.log('===================')
  }

  /**
   * 关闭蓝牙适配器
   * 完全关闭蓝牙功能，释放所有资源
   */
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.discoveryStarted = false
  }

  /**
   * 🎯 新架构：为每个指令创建独立的BLE服务器
   * @param {string} commandKey - 指令键名
   * @returns {Promise} 服务器创建结果
   */
  async createIndependentServer(commandKey) {
    try {
      const res = await wx.createBLEPeripheralServer()
      console.log(`✅ 为指令 ${commandKey} 创建独立服务器成功:`, res.server.serverId)

      // 存储到独立服务器映射中
      this.broadcastServers.set(commandKey, {
        server: res.server,
        serverId: res.server.serverId,
        isActive: false,
        lastCommand: null
      })

      return res.server
    } catch (error) {
      console.error(`❌ 为指令 ${commandKey} 创建服务器失败:`, error)
      throw error
    }
  }

  /**
   * 🎯 新架构：获取或创建指令的独立服务器
   * @param {string} commandKey - 指令键名
   * @returns {Promise} 服务器实例
   */
  async getOrCreateServer(commandKey) {
    // 检查是否已有该指令的服务器
    if (this.broadcastServers.has(commandKey)) {
      const serverInfo = this.broadcastServers.get(commandKey)
      if (serverInfo.server) {
        console.log(`🔄 复用指令 ${commandKey} 的现有服务器`)
        return serverInfo.server
      }
    }

    // 创建新的独立服务器
    console.log(`🆕 为指令 ${commandKey} 创建新服务器`)
    return await this.createIndependentServer(commandKey)
  }

  /**
   * 创建BLE外设服务器 (保留原有方法用于兼容)
   * 用于BLE广播功能，创建可被其他设备发现的服务器
   */
  createBLEPeripheralServer() {
    wx.createBLEPeripheralServer().then(res => {
      console.log('创建BLE外设服务器成功createBLEPeripheralServer', res)
      // 标记广播功能就绪
      this.advertiseReady = true

      // 🎯 重置重建状态
      this.isRebuilding = false

      // 🎯 服务器重建成功后，检查是否需要恢复连接状态
      this.checkAndRestoreConnection()
    }).catch(error => {
      console.error('❌ 创建BLE外设服务器失败:', error)
      this.isRebuilding = false
      this.advertiseReady = false
    })
  }

  /**
   * 检查服务器是否真的丢失
   */
  checkServerStatus() {
    console.log('🔍 检查服务器状态:', {
      hasServer: !!this.server,
      serverId: this.serverId,
      advertiseReady: this.advertiseReady,
      discoveryReady: this.discoveryReady
    })

    return !!this.server && this.advertiseReady
  }

  /**
   * 检查蓝牙服务状态
   * 用于页面状态监听时检查服务是否正常
   */
  checkServiceStatus() {
    return {
      advertiseReady: this.advertiseReady,
      discoveryReady: this.discoveryReady,
      hasServer: !!this.server,
      serverId: this.serverId,
      isInitialized: this.isInitialized,
      hasConnectionCallback: !!this.onConnectionChange
    }
  }

  /**
   * 检查并恢复连接状态
   * 当广播和接收服务都准备好时，恢复连接状态
   */
  checkAndRestoreConnection() {
    console.log('🔍 检查服务状态:', {
      advertiseReady: this.advertiseReady,
      discoveryReady: this.discoveryReady,
      hasConnectionCallback: !!this.onConnectionChange,
      isRebuilding: this.isRebuilding
    })

    if (this.advertiseReady && this.discoveryReady && this.onConnectionChange) {
      console.log('✅ 蓝牙服务重建完成，恢复连接状态')

      // 🎯 延迟一点时间确保状态稳定后再通知
      setTimeout(() => {
        if (this.advertiseReady && this.discoveryReady && this.onConnectionChange) {
          this.isRebuilding = false  // 清除重建状态
          this.onConnectionChange(true)
          console.log('🔗 连接状态已恢复')
        }
      }, 100) // 100ms延迟确保状态稳定
    }
  }

  /**
   * 关闭BLE服务器
   * 停止广播并释放服务器资源
   */
  closeServer() {
    this.server.close()
  }

  /**
   * 开始BLE广播  发送指令
   * 根据平台类型选择不同的数据封装方式进行广播
   * @param {Array} actPayload - 经过处理的BLE数据包
   */
  startAdvertising(actPayload) {
    // 1. 检测系统平台
    // 确保 this.system 不为 undefined
    if (!this.system) {
      console.log('⚠️ 系统信息未初始化，重新获取')
      this.initSystemInfo()
    }

    const systemStr = (this.system || '').toLowerCase()
    const isIos = systemStr.indexOf('ios') >= 0
    const isIos13 = isIos && systemStr.indexOf('13.') >= 0  // iOS 13有特殊处理

    // console.log('actPayload', actPayload)

    // 2. 根据平台生成Service UUIDs（主要用于iOS）
    const uuids = getServiceUUIDs(actPayload, isIos13)

    // 3. 开始BLE广播，根据平台使用不同的数据封装方式 此处封装了BLE广播数据包，并将其发送出去。 在这里发送指令了
    // this.server.startAdvertising此方法来自一开始wx.createBLEPeripheralServer() 创建服务器实例 BLEPeripheralServer实例上面有这个方法 用来开始广播本地创建的外围设备。

    console.log('🍎 iOS平台配置:', {
      isIos: isIos,
      deviceName: isIos ? '11' : '',
      serviceUuids: isIos ? uuids : [],
      manufacturerData: isIos ? [] : [{
        manufacturerId: '0x00C7',
        manufacturerSpecificData: actPayload,
      }]
    })

    this.server.startAdvertising({
      advertiseTimeout: 0,
      advertiseRequest: {
        connectable: true,                    // 允许连接
        deviceName: isIos ? '11' : '',        // iOS设备名为'11'，Android为空

        // iOS平台：数据编码到Service UUIDs中
        serviceUuids: isIos ? uuids : [],

        // Android平台：数据放在Manufacturer Data中
        manufacturerData: isIos ? [] : [{
          manufacturerId: '0x00C7',           // 制造商ID
          manufacturerSpecificData: actPayload, // 实际数据
        }]
      },
      powerLevel: 'high'  // 高功率广播，增加传输距离
    }).then(res => {
      // 广播成功
      console.log('✅ 广播启动成功:', res)
      this.advertiseStart = true
      // 保持 isBroadcasting = true，因为广播确实在进行中

      // 🎯 记录广播成功状态
      if (this.onSendData) {
        this.onSendData(this.payload, '发送指令', {
          broadcastStatus: 'success',
          broadcastMessage: '指令发送成功'

        })
      }
    }, res => {
      // 广播失败
      this.advertiseStart = false
      this.isBroadcasting = false

      let errorMessage = '广播失败'
      let isSuccess = false

      // 🎯 检查是否是服务器丢失错误 - 支持多种错误消息格式
      if ((res.errMsg && (res.errMsg.includes('no such server') || res.errMsg.includes('no server'))) && !this.isRebuilding) {
        console.log('⚠️ 检测到服务器丢失错误，检查实际状态')
        console.log('🔍 错误详情:', res)

        // 检查服务器实际状态
        const serverExists = this.checkServerStatus()

        // 检查 serverId 是否匹配（从错误信息中提取）
        const errorServerIdMatch = res.errMsg.match(/serverId:\s*(\w*)/)
        const errorServerId = errorServerIdMatch ? errorServerIdMatch[1] : ''
        const serverIdMismatch = errorServerId !== this.serverId

        console.log('🔍 服务器ID检查:', {
          errorServerId: errorServerId,
          currentServerId: this.serverId,
          mismatch: serverIdMismatch
        })

        // 🎯 Android平台强制重建服务器
        if (!this.isIos || !serverExists || serverIdMismatch) {
          console.log('🔧 确认需要重新创建服务器 (Android平台或服务器丢失)')
          errorMessage = '服务器需要重新创建'

          // 设置重建状态，避免重复触发
          this.isRebuilding = true

          // 重置服务器相关状态
          this.server = null
          this.serverId = null
          this.advertiseReady = false
          this.discoveryReady = false

          // 🎯 不立即通知连接状态变化，等待重建完成
          // 避免在重建过程中显示"蓝牙断开"的闪烁提示
          console.log('🔄 开始服务器重建，暂不通知连接状态变化')

          // 重新初始化
          this.initAdvertiseAdapter()
          this.initDiscoveryAdapter()
        } else {
          console.log('✅ 服务器实际存在且ID匹配，可能是临时错误，忽略')
          errorMessage = '临时错误，服务器正常'
          isSuccess = true
        }
      } else if (res.errMsg && res.errMsg.includes('already started')) {
        console.log('⚠️ 广播已在进行中，忽略此错误')
        this.advertiseStart = true  // 设置为已开始状态
        isSuccess = true
        errorMessage = '广播已在进行中'
      } else {
        console.log("startAdvertising fail: ", res)
        errorMessage = res.errMsg || '未知错误'
      }

      // 🎯 记录广播状态
      if (this.onSendData) {
        this.onSendData(this.payload, '发送指令', {
          broadcastStatus: isSuccess ? 'success' : 'failed',
          broadcastMessage: errorMessage
        })
      }
    })
  }

  /**
   * 停止BLE广播
   * 停止数据发送并更新状态
   */
  stopAdvertising() {
    if (this.server) {
      this.server.stopAdvertising()           // 停止广播
      this.advertiseStart = false // 更新状态
    }
  }

  /**
   * 清理转向指令的定时器
   */
  clearDirectCommandTimers() {
    if (this.directCommandTimer1) {
      clearTimeout(this.directCommandTimer1)
      this.directCommandTimer1 = null
      console.log('🧹 清理转向定时器1')
    }
    if (this.directCommandTimer2) {
      clearTimeout(this.directCommandTimer2)
      this.directCommandTimer2 = null
      console.log('🧹 清理转向定时器2')
    }
  }

  /**
   * 等待设备准备就绪
   */
  async waitForDeviceReady() {
    const now = Date.now()
    const timeSinceLastCommand = now - this.lastCommandTime

    // 如果距离上次指令不足500ms，等待
    if (timeSinceLastCommand < 500) {
      const waitTime = 500 - timeSinceLastCommand
      console.log(`⏳ 等待设备准备就绪: ${waitTime}ms`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }

    // 如果设备忙碌，等待
    if (this.deviceBusy) {
      console.log('⏳ 设备忙碌，等待完成...')
      await new Promise(resolve => {
        const checkReady = () => {
          if (!this.deviceBusy) {
            resolve()
          } else {
            setTimeout(checkReady, 100)
          }
        }
        checkReady()
      })
    }
  }

  /**
   * 发送停止指令并等待响应
   */
  async sendStopAndWait() {
    console.log('🛑 发送停止指令并等待响应')
    this.deviceBusy = true

    // 强制停止所有广播
    this.forceStopAllAdvertising()

    // 发送停止指令
    const stopCommand = this.generateCarCommand(0x10, 0x00)
    const stopHex = stopCommand
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    this.payload = stopHex
    this.lastCommandTime = Date.now()
    this.startSendAndDiscovery()

    // 等待设备响应或超时
    await new Promise(resolve => {
      const timeout = setTimeout(() => {
        console.log('⚠️ 停止指令超时')
        this.deviceBusy = false
        resolve()
      }, 1000)

      // 监听设备响应
      const originalOnDataReceived = this.onDataReceived
      this.onDataReceived = (data) => {
        if (originalOnDataReceived) {
          originalOnDataReceived(data)
        }
        // 如果收到停止响应，标记设备就绪
        if (data.data && data.data.includes('81000211')) {
          clearTimeout(timeout)
          this.deviceBusy = false
          console.log('✅ 设备已停止，准备就绪')
          resolve()
        }
      }
    })
  }

  /**
   * 直接发送指令而不改变当前广播状态
   * 用于运动过程中的转向和档位切换
   */
  sendDirectCommand(hexCommand) {
    console.log('📤 直接发送指令:', hexCommand)

    // 🎯 清理之前的定时器
    this.clearDirectCommandTimers()

    // 🎯 保存原始运动指令（如果还没有保存的话）
    if (!this.originalMotionPayload) {
      // 只有在是运动指令时才保存
      if (this.payload && (this.payload.includes('81000210') || this.payload.includes('81000211'))) {
        this.originalMotionPayload = this.payload
        console.log('💾 保存原始运动指令:', this.originalMotionPayload)
      }
    }

    // 🎯 确保有原始运动指令可以恢复
    if (!this.originalMotionPayload) {
      console.log('⚠️ 没有原始运动指令可恢复，跳过转向')
      return
    }

    // 🎯 优化：减少定时器嵌套，使用Promise链
    this.stopAdvertising()

    // 🎯 使用更短的延迟，减少性能消耗
    this.directCommandTimer1 = setTimeout(() => {
      console.log('📤 发送转向指令:', hexCommand)
      this.payload = hexCommand
      this.startSendAndDiscovery()

      // 🎯 减少恢复延迟，提高响应速度
      this.directCommandTimer2 = setTimeout(() => {
        console.log('🔄 恢复原来的运动指令:', this.originalMotionPayload)

        if (this.originalMotionPayload && this.originalMotionPayload.length > 0) {
          this.payload = this.originalMotionPayload
          this.startSendAndDiscovery()
          console.log('✅ 原运动指令已恢复')
        } else {
          console.log('⚠️ 没有有效的原始运动指令，跳过恢复')
        }
        this.directCommandTimer2 = null
      }, 200) // 🎯 从300ms减少到200ms

      this.directCommandTimer1 = null
    }, 30) // 🎯 从50ms减少到30ms
  }

  /**
   * 强制停止所有广播
   * 用于紧急情况下停止所有广播活动
   */
  forceStopAllAdvertising() {
    console.log('🚨 强制停止所有广播活动')

    // 停止当前服务器的广播
    if (this.server) {
      try {
        this.server.stopAdvertising()
        this.advertiseStart = false
        console.log('✅ 当前服务器广播已停止')
      } catch (e) {
        console.log('⚠️ 停止当前服务器广播失败:', e)
      }
    }

    // 停止所有服务器的广播
    this.servers.forEach((server, index) => {
      try {
        server.stopAdvertising()
        console.log(`✅ 服务器${index}广播已停止`)
      } catch (e) {
        console.log(`⚠️ 停止服务器${index}广播失败:`, e)
      }
    })

    console.log('🛑 强制停止广播完成')
  }

  // ===== 新增的玩具车控制方法 =====

  /**
   * 设置连接状态变化回调
   * @param {Function} callback - 回调函数 (isConnected) => {}
   */
  setConnectionCallback(callback) {
    this.onConnectionChange = callback
  }

  /**
   * 设置数据接收回调
   * @param {Function} callback - 回调函数 (data) => {}
   */
  setDataCallback(callback) {
    this.onDataReceived = callback
  }

  /**
   * 设置错误回调
   * @param {Function} callback - 回调函数 (error) => {}
   */
  setErrorCallback(callback) {
    this.onError = callback
  }

  /**
   * 设置发送数据记录回调
   * @param {Function} callback - 回调函数 (hexCommand, commandType) => {}
   */
  setSendDataCallback(callback) {
    this.onSendData = callback
  }

  /**
   * 初始化蓝牙功能 - 参考 advertise297 的简洁架构
   * 按顺序初始化各个组件
   */
  initBluetooth() {
    // 🎯 Android平台特殊处理：检查服务器是否真的存在
    if (this.isInitialized || this.isInitializing) {
      // 如果是Android平台且服务器不存在，强制重新初始化
      if (!this.isIos && !this.checkServerStatus()) {
        console.log('🔄 Android平台检测到服务器丢失，强制重新初始化')
        this.resetInitializationState()
      } else {
        console.log('🔄 蓝牙已初始化或正在初始化，跳过')
        return
      }
    }

    console.log('🔧 开始蓝牙工具初始化')
    this.isInitializing = true

    try {
      this.initSystemInfo()        // 1. 获取系统信息（iOS/Android）
      this.initPayload()           // 2. 初始化地址默认
      this.initAdvertiseAdapter()  // 3. 初始化广播适配器（发送功能）
      this.initDiscoveryAdapter()  // 4. 初始化发现适配器（接收功能）

      this.isInitialized = true
      console.log('✅ 蓝牙工具初始化完成')
    } catch (error) {
      console.error('❌ 蓝牙工具初始化失败:', error)
    } finally {
      this.isInitializing = false
    }
  }

  /**
   * 重置初始化状态
   * 用于重连时清理状态
   */
  resetInitializationState() {
    console.log('🔄 重置蓝牙初始化状态')
    this.isInitialized = false
    this.isInitializing = false
    this.advertiseReady = false
    this.discoveryReady = false
    this.advertiseStart = false
    this.discoveryStarted = false

    // 🎯 重置服务器相关状态 - 修复Android页面退出后服务器丢失问题
    this.server = null
    this.serverId = null
    this.servers = []
  }



  /**
   * 初始化载荷数据 - 参考 advertise297
   * 从本地存储读取配置数据
   */
  initPayload() {
    try {
      saveConfig(DEFAULT_CONFIG)
      console.log('✅ 配置初始化完成')
    } catch (error) {
      console.error('❌ 配置初始化失败:', error)
    }
  }

  /**
   * 初始化广播适配器（发送功能） - 参考 advertise297
   * 以外设模式打开蓝牙适配器，用于发送BLE广播
   */
  initAdvertiseAdapter() {
    // iOS需要先检查权限状态，避免频繁请求
    if (this.system && this.system.toLowerCase().indexOf('ios') >= 0) {
      // 🎯 先检查授权状态
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.bluetooth'] === true) {
            console.log('✅ iOS蓝牙权限已授权，直接初始化')
            this.openAdvertiseAdapter()
          } else if (res.authSetting['scope.bluetooth'] === false) {
            console.log('❌ iOS蓝牙权限被拒绝，尝试继续初始化')
            // 权限被拒绝，但仍尝试初始化（某些设备可能仍可工作）
            this.openAdvertiseAdapter()
          } else {
            // 🎯 未授权，请求授权（添加频率限制保护）
            console.log('🔍 iOS蓝牙权限未设置，请求授权')
            wx.authorize({
              scope: 'scope.bluetooth',
              success: () => {
                console.log('✅ iOS蓝牙权限获取成功')
                this.openAdvertiseAdapter()
              },
              fail: (error) => {
                console.log('⚠️ iOS蓝牙权限获取失败，继续尝试', error)
                // 🎯 授权失败也尝试初始化，可能设备支持
                this.openAdvertiseAdapter()
              }
            })
          }
        },
        fail: (error) => {
          console.log('⚠️ 获取设置失败，直接尝试初始化', error)
          this.openAdvertiseAdapter()
        }
      })
    } else {
      this.openAdvertiseAdapter()
    }
  }

  /**
   * 打开广播适配器
   */
  openAdvertiseAdapter() {
    wx.openBluetoothAdapter({
      mode: 'peripheral',  // 外设模式，用于广播数据
      success: (res) => {
        console.log('initAdvertiseAdapter success', res)
        this.createBLEPeripheralServer()  // 成功后创建BLE外设服务器
      },
      fail: (res) => {
        console.log("initAdvertiseAdapter ble unavailable!", res)
      }
    })
  }



  /**
   * 开始扫描设备
   */
  async startScan() {
    try {
      console.log('🔍 开始扫描蓝牙设备...')

      // 使用现有的方法
      this.startBluetoothDevicesDiscovery()

      return true
    } catch (error) {
      if (this.onError) {
        this.onError('开始扫描失败: ' + error.message)
      }
      return false
    }
  }

  /**
   * 生成车控制指令（按照V1.10协议）
   * @param {number} commandByte - 命令字 (0x10/0x12/0x14)
   * @param {number} dataByte - 数据字节
   * @param {number} deviceAddress - 设备地址 (默认使用配置中的地址)
   * @returns {Array} 指令字节数组
   */
  generateCarCommand(commandByte, dataByte, deviceAddress = null) {
    // 如果没有指定设备地址，使用配置中的默认地址
    if (deviceAddress === null) {
      deviceAddress = this.deviceAddress
    }
    // 协议格式：81 [设备地址] 02 [命令字] [数据] [校验和低位] [校验和高位] FA
    const byte1 = deviceAddress // 设备地址码
    const byte2 = 0x02 // 数据长度
    const byte3 = commandByte // 命令字 (0x10/0x12/0x14)
    const byte4 = dataByte // 数据

    // 校验计算：BYTE1 到 BYTE4 的累加
    const checksum = (byte1 + byte2 + byte3 + byte4) & 0xffff
    const sumL = checksum & 0xff // 低8位
    const sumH = (checksum >> 8) & 0xff // 高8位

    return [
      0x81, // 控制起始符
      byte1, // 设备地址码
      byte2, // 数据长度
      byte3, // 命令字
      byte4, // 数据
      sumL, // 校验和低位
      sumH, // 校验和高位
      0xfa, // 控制结束符
    ]
  }

  /**
   * 🎯 新架构：设置按钮状态
   * @param {string} buttonKey - 按钮键名
   * @param {boolean} isPressed - 是否按下
   */
  setButtonState(buttonKey, isPressed) {
    console.log(`🎯 设置按钮状态: ${buttonKey} = ${isPressed}`)
    this.buttonStates[buttonKey] = isPressed

    // 立即处理状态变化
    this.processButtonStates()
  }

  /**
   * 🎯 新架构：根据按钮状态处理指令发送
   */
  async processButtonStates() {
    console.log('🔄 处理按钮状态:', this.buttonStates)

    // 遍历所有按钮状态，为每个激活的按钮发送对应指令
    for (const [buttonKey, isPressed] of Object.entries(this.buttonStates)) {
      if (isPressed) {
        await this.sendIndependentCommand(buttonKey)
      } else {
        await this.stopIndependentCommand(buttonKey)
      }
    }
  }

  /**
   * 🎯 新架构：发送独立指令
   * @param {string} commandKey - 指令键名
   */
  async sendIndependentCommand(commandKey) {
    try {
      // 获取指令配置
      const commandConfig = this.commandMap[commandKey]
      if (!commandConfig) {
        console.error(`❌ 未找到指令配置: ${commandKey}`)
        return
      }

      // 检查是否已在广播中
      const serverInfo = this.broadcastServers.get(commandKey)
      if (serverInfo && serverInfo.isActive) {
        console.log(`⚠️ 指令 ${commandKey} 已在广播中，跳过`)
        return
      }

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer(commandKey)

      // 生成指令
      const command = this.generateCarCommand(commandConfig.cmd, commandConfig.data)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送独立指令 ${commandConfig.name}:`, hexCommand)

      // 开始独立广播
      await this.startIndependentAdvertising(commandKey, server, hexCommand)

    } catch (error) {
      console.error(`❌ 发送独立指令 ${commandKey} 失败:`, error)
    }
  }

  /**
   * 🎯 新架构：停止独立指令
   * @param {string} commandKey - 指令键名
   */
  async stopIndependentCommand(commandKey) {
    const serverInfo = this.broadcastServers.get(commandKey)
    if (serverInfo && serverInfo.isActive) {
      try {
        serverInfo.server.stopAdvertising()
        serverInfo.isActive = false
        console.log(`🛑 停止独立指令 ${commandKey} 的广播`)
      } catch (error) {
        console.error(`❌ 停止独立指令 ${commandKey} 失败:`, error)
      }
    }
  }

  /**
   * 🎯 新架构：开始独立广播
   * @param {string} commandKey - 指令键名
   * @param {Object} server - 服务器实例
   * @param {string} hexCommand - 十六进制指令
   */
  async startIndependentAdvertising(commandKey, server, hexCommand) {
    try {
      // 检测系统平台并生成BLE数据包
      const systemStr = (this.system || '').toLowerCase()
      const isIos = systemStr.indexOf('ios') >= 0
      const actPayload = generateData(hexCommand, isIos)

      // 根据平台生成Service UUIDs
      const uuids = getServiceUUIDs(actPayload, isIos && systemStr.indexOf('13.') >= 0)

      console.log(`🍎 ${commandKey} 独立广播配置:`, {
        isIos: isIos,
        deviceName: isIos ? '11' : '',
        serviceUuids: isIos ? uuids : [],
        manufacturerData: isIos ? [] : [{
          manufacturerId: '0x00C7',
          manufacturerSpecificData: actPayload,
        }]
      })

      // 开始独立广播
      await server.startAdvertising({
        advertiseTimeout: 0,
        advertiseRequest: {
          connectable: true,
          deviceName: isIos ? '11' : '',
          serviceUuids: isIos ? uuids : [],
          manufacturerData: isIos ? [] : [{
            manufacturerId: '0x00C7',
            manufacturerSpecificData: actPayload,
          }]
        },
        powerLevel: 'high'
      })

      // 更新服务器状态
      const serverInfo = this.broadcastServers.get(commandKey)
      if (serverInfo) {
        serverInfo.isActive = true
        serverInfo.lastCommand = hexCommand
      }

      console.log(`✅ 独立指令 ${commandKey} 广播启动成功`)

      // 记录发送数据
      if (this.onSendData) {
        this.onSendData(hexCommand, `独立指令-${commandKey}`, {
          broadcastStatus: 'success',
          broadcastMessage: `独立指令 ${commandKey} 发送成功`
        })
      }

    } catch (error) {
      console.error(`❌ 独立指令 ${commandKey} 广播失败:`, error)

      // 记录发送失败
      if (this.onSendData) {
        this.onSendData(hexCommand, `独立指令-${commandKey}`, {
          broadcastStatus: 'failed',
          broadcastMessage: `独立指令 ${commandKey} 发送失败: ${error.message}`
        })
      }
    }
  }

  /**
   * 发送前进指令 (独立广播版本)
   */
  async sendForward() {
    console.log('🔍 sendForward 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过前进指令')
      return
    }

    // 🎯 创建独立服务器并发送前进指令
    await this.createIndependentBroadcast('forward', '81000210011300FA')
  }

  /**
   * 🎯 创建独立广播服务器并发送指令
   * @param {string} commandKey - 指令键名
   * @param {string} hexCommand - 十六进制指令
   */
  async createIndependentBroadcast(commandKey, hexCommand) {
    try {
      // 如果已有该指令的服务器，先停止
      if (this.independentServers && this.independentServers.has(commandKey)) {
        const existingServer = this.independentServers.get(commandKey)
        if (existingServer.isActive) {
          console.log(`🛑 停止已有的 ${commandKey} 广播`)
          existingServer.server.stopAdvertising()
          existingServer.isActive = false
        }
      }

      // 初始化独立服务器映射
      if (!this.independentServers) {
        this.independentServers = new Map()
      }

      // 创建新的独立服务器
      const res = await wx.createBLEPeripheralServer()
      const server = res.server
      const serverId = res.server.serverId

      console.log(`✅ 创建 ${commandKey} 独立服务器成功: ${serverId}`)

      // 保存服务器信息
      this.independentServers.set(commandKey, {
        server: server,
        serverId: serverId,
        isActive: false
      })

      // 发送指令
      await this.startIndependentAdvertising(commandKey, hexCommand)

    } catch (error) {
      console.error(`❌ 创建 ${commandKey} 独立服务器失败:`, error)
    }
  }

  /**
   * 🎯 启动独立服务器广播
   * @param {string} commandKey - 指令键名
   * @param {string} hexCommand - 十六进制指令
   */
  async startIndependentAdvertising(commandKey, hexCommand) {
    const serverInfo = this.independentServers.get(commandKey)
    if (!serverInfo) {
      console.error(`❌ ${commandKey} 服务器不存在`)
      return
    }

    try {
      // 使用旧版本的数据处理逻辑
      const actPayload = this.buildPayload(hexCommand)
      const uuids = this.buildServiceUuids(actPayload)
      const isIos = this.system && this.system.toLowerCase().indexOf('ios') >= 0

      console.log(`📤 发送独立指令 ${this.getCommandName(commandKey)}: ${hexCommand}`)

      const advertiseConfig = {
        advertiseTimeout: 0,
        advertiseRequest: {
          connectable: true,
          deviceName: isIos ? '11' : '',
          serviceUuids: isIos ? uuids : [],
          manufacturerData: isIos ? [] : [{
            manufacturerId: '0x00C7',
            manufacturerSpecificData: actPayload,
          }]
        },
        powerLevel: 'high'
      }

      await new Promise((resolve, reject) => {
        serverInfo.server.startAdvertising({
          ...advertiseConfig,
          success: (res) => {
            console.log(`✅ 独立指令 ${commandKey} 广播启动成功`)
            serverInfo.isActive = true
            resolve(res)
          },
          fail: (error) => {
            console.error(`❌ 独立指令 ${commandKey} 广播失败:`, error)
            reject(error)
          }
        })
      })

    } catch (error) {
      console.error(`❌ ${commandKey} 广播启动失败:`, error)
    }
  }

  /**
   * 🎯 获取指令对应的中文名称
   */
  getCommandName(commandKey) {
    const nameMap = {
      'forward': '前进',
      'backward': '后退',
      'left': '左转',
      'right': '右转',
      'brake': '刹车',
      'parking': '驻车'
    }
    return nameMap[commandKey] || commandKey
  }

  /**
   * 🎯 获取按钮状态（兼容新架构）
   */
  getButtonStates() {
    // 返回空对象，因为旧版本不需要按钮状态管理
    return {}
  }

  /**
   * 发送后退指令 (独立广播版本)
   */
  async sendBackward() {
    console.log('🔍 sendBackward 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过后退指令')
      return
    }

    // 🎯 创建独立服务器并发送后退指令
    await this.createIndependentBroadcast('backward', '81000210021400FA')
  }

  /**
   * 停止前进指令 (独立广播版本)
   */
  stopForward() {
    console.log('🔍 stopForward 被调用')
    this.stopIndependentBroadcast('forward')
  }

  /**
   * 🎯 停止独立广播服务器
   * @param {string} commandKey - 指令键名
   */
  stopIndependentBroadcast(commandKey) {
    try {
      if (!this.independentServers || !this.independentServers.has(commandKey)) {
        console.log(`⚠️ ${commandKey} 服务器不存在，无需停止`)
        return
      }

      const serverInfo = this.independentServers.get(commandKey)
      if (serverInfo.isActive) {
        console.log(`🛑 停止 ${commandKey} 广播`)
        serverInfo.server.stopAdvertising()
        serverInfo.isActive = false
      }

      // 销毁服务器
      console.log(`🗑️ 销毁 ${commandKey} 服务器`)
      this.independentServers.delete(commandKey)

    } catch (error) {
      console.error(`❌ 停止 ${commandKey} 失败:`, error)
    }
  }

  /**
   * 停止后退指令 (独立广播版本)
   */
  stopBackward() {
    console.log('🔍 stopBackward 被调用')
    this.stopIndependentBroadcast('backward')
  }

  /**
   * 发送左转指令 (独立广播版本)
   */
  async sendLeft() {
    console.log('🔍 sendLeft 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过左转指令')
      return
    }

    // 🎯 创建独立服务器并发送左转指令
    await this.createIndependentBroadcast('left', '81000214011700FA')
  }

  /**
   * 停止左转指令 (独立广播版本)
   */
  stopLeft() {
    console.log('🔍 stopLeft 被调用')
    this.stopIndependentBroadcast('left')
  }

  /**
   * 发送右转指令 (独立广播版本)
   */
  async sendRight() {
    console.log('🔍 sendRight 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过右转指令')
      return
    }

    // 🎯 创建独立服务器并发送右转指令
    await this.createIndependentBroadcast('right', '81000214021800FA')
  }

  /**
   * 停止右转指令 (独立广播版本)
   */
  stopRight() {
    console.log('🔍 stopRight 被调用')
    this.stopIndependentBroadcast('right')
  }

  /**
   * 发送刹车指令 (新架构适配)
   */
  sendBrake() {
    console.log('🔍 sendBrake 被调用')
    this.setButtonState('brake', true)
  }

  /**
   * 停止刹车指令
   */
  stopBrake() {
    console.log('🔍 stopBrake 被调用')
    this.setButtonState('brake', false)
  }

  /**
   * 发送驻车指令 (新架构适配)
   */
  sendParking(parkingState = 1) {
    console.log('🔍 sendParking 被调用, 状态:', parkingState)
    // 更新指令映射中的驻车数据
    this.commandMap.parking.data = parkingState
    this.setButtonState('parking', parkingState === 1)
  }

  /**
   * 发送档位指令 (新架构适配)
   */
  sendGear(gear) {
    console.log('🔍 sendGear 被调用，档位:', gear)

    // 先清除所有档位状态
    this.setButtonState('gear1', false)
    this.setButtonState('gear2', false)
    this.setButtonState('gear3', false)

    // 设置对应档位状态
    if (gear >= 1 && gear <= 3) {
      this.setButtonState(`gear${gear}`, true)
    }
  }

  /**
   * 发送自定义速度指令 (新架构适配)
   */
  sendCustomSpeed(speedValue) {
    console.log('🔍 sendCustomSpeed 被调用，速度值:', speedValue)

    // 动态更新指令映射
    this.commandMap.customSpeed = { cmd: 0x12, data: speedValue, name: '自定义速度' }
    this.setButtonState('customSpeed', true)
  }

  /**
   * 旧版本发送后退指令 (保留用于兼容)
   */
  sendBackward() {
    console.log('🔍 sendBackward 被调用 - 执行设备重置序列')

    // 🎯 彻底清理所有可能影响的定时器
    this.clearDirectCommandTimers()

    // 🎯 强制清理 originalMotionPayload，防止被定时器恢复
    this.originalMotionPayload = null
    console.log('🗑️ 强制清空原始运动指令，防止定时器干扰')

    // 🎯 检查蓝牙适配器状态
    if (!this.advertiseReady) {
      console.log('⚠️ 蓝牙广播适配器未就绪，尝试重新初始化')
      this.initAdvertiseAdapter()
      return
    }

    // 🎯 执行设备重置序列 - 模拟"松开重置"的效果
    console.log('🔄 开始设备重置序列，解锁设备状态')

    // 第一步：强制停止所有广播，模拟断开连接
    this.forceStopAllAdvertising()
    console.log('� 第一步：强制停止广播，模拟断开')

    // 第二步：等待设备超时重置（模拟松开按钮的效果）
    setTimeout(() => {
      console.log('⏳ 第二步：等待设备状态重置')

      // 第三步：发送停止指令，确保设备完全停止
      const stopCommand = this.generateCarCommand(0x10, 0x00)
      const stopHex = stopCommand
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log('🛑 第三步：发送停止指令解锁状态:', stopHex)
      this.payload = stopHex
      this.startSendAndDiscovery()

      // 第四步：等待设备处理停止指令后发送后退
      setTimeout(() => {
        console.log('🚗 第四步：发送后退指令')

        const command = this.generateCarCommand(0x10, 0x02)
        const hexCommand = command
          .map((b) => b.toString(16).padStart(2, "0"))
          .join("")
          .toUpperCase()

        console.log('🚗 发送后退指令:', hexCommand)

        // 🎯 记录这是运动指令，用于超时重试
        this.lastMotionCommand = hexCommand
        this.motionRetryCount = 0

        // 🎯 设置为原始运动指令，用于转向后恢复
        this.originalMotionPayload = hexCommand
        console.log('💾 设置原始运动指令:', this.originalMotionPayload)

        // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
        this.payload = hexCommand
        this.startSendAndDiscovery()

      }, 300) // 等待设备处理停止指令

    }, 200) // 等待设备超时重置
  }

  /**
   * 发送左转指令
   * @param {boolean} keepBroadcasting - 是否保持当前广播（运动过程中的转向）
   */
  sendLeft(keepBroadcasting = false) {
    console.log('🔄 发送左转指令')

    // 🎯 检查蓝牙适配器状态
    if (!this.advertiseReady) {
      console.log('⚠️ 蓝牙广播适配器未就绪，尝试重新初始化')
      this.initAdvertiseAdapter()
      return
    }

    // 生成左转指令
    const command = this.generateCarCommand(0x14, 0x01)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log('🚗 发送左转指令:', hexCommand)

    if (keepBroadcasting) {
      console.log('🎯 运动中转向，保持当前广播状态')
      // 运动过程中的转向，只发送转向指令，不改变当前广播
      this.sendDirectCommand(hexCommand)
    } else {
      // 独立的转向指令，停止当前广播并发送转向指令
      this.stopAdvertising()
      this.payload = hexCommand
      this.startSendAndDiscovery()
    }
  }

  /**
   * 发送右转指令
   * @param {boolean} keepBroadcasting - 是否保持当前广播（运动过程中的转向）
   */
  sendRight(keepBroadcasting = false) {
    console.log('🔄 发送右转指令')

    // 🎯 检查蓝牙适配器状态
    if (!this.advertiseReady) {
      console.log('⚠️ 蓝牙广播适配器未就绪，尝试重新初始化')
      this.initAdvertiseAdapter()
      return
    }

    // 生成右转指令
    const command = this.generateCarCommand(0x14, 0x02)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log('🚗 发送右转指令:', hexCommand)

    if (keepBroadcasting) {
      console.log('🎯 运动中转向，保持当前广播状态')
      // 运动过程中的转向，只发送转向指令，不改变当前广播
      this.sendDirectCommand(hexCommand)
    } else {
      // 独立的转向指令，停止当前广播并发送转向指令
      this.stopAdvertising()
      this.payload = hexCommand
      this.startSendAndDiscovery()
    }
  }



  /**
   * 发送停车指令 - 修改为发送刹车指令，更有效
   */
  sendStop() {
    const command = this.generateCarCommand(0x10, 0x03)  // 🎯 改为刹车指令
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log('🚗 发送刹车指令(通过sendStop):', hexCommand)
    // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
    this.payload = hexCommand
    this.startSendAndDiscovery()
  }

  /**
   * 发送刹车指令 - 解决异步冲突问题
   */
  sendBrake() {
    // 🎯 强制停止之前的广播，避免异步冲突
    console.log('🛑 刹车前强制停止广播，避免指令冲突')
    this.stopAdvertising()

    // 等待广播完全停止后再发送刹车指令
    setTimeout(() => {
      const command = this.generateCarCommand(0x10, 0x03)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log('🚗 发送刹车指令:', hexCommand)
      // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
      this.payload = hexCommand
      this.startSendAndDiscovery()
    }, 50) // 🎯 等待50ms确保广播完全停止
  }

  /**
   * 发送驻车指令
   * @param {number} parkingState - 驻车状态 (0: 关闭驻车, 1: 开启驻车)
   */
  sendParking(parkingState = 1) {
    // 🎯 强制停止之前的广播，避免异步冲突
    console.log('🅿️ 驻车前强制停止广播，避免指令冲突')
    this.stopAdvertising()

    // 等待广播完全停止后再发送驻车指令
    setTimeout(() => {
      // 驻车指令：81 00 02 1A 01 1D 00 FA (开启驻车)
      // 驻车指令：81 00 02 1A 00 1C 00 FA (关闭驻车)
      const command = this.generateCarCommand(0x1A, parkingState)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`🅿️ 发送驻车指令 (${parkingState ? '开启' : '关闭'}):`, hexCommand)
      // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
      this.payload = hexCommand
      this.startSendAndDiscovery()

      // 🎯 驻车指令不自动停止广播，等待设备响应后由主页面控制停止
    }, 50) // 🎯 等待50ms确保广播完全停止
  }

  // 移除自动设备初始化方法，避免进入页面时自动发送指令

  /**
   * 发送停转指令
   */
  sendStopTurn() {
    // console.log('🔍 sendStopTurn 被调用')
    const command = this.generateCarCommand(0x14, 0x00)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log('🚗 发送停转指令:', hexCommand)
    // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
    this.payload = hexCommand
    this.startSendAndDiscovery()
  }


  sendWithoutStop() {
    // 1. 获取并清理payload数据（去除空格）
    let payload = this.payload.replace(/\s+/g, '')

    // 2. 检查广播适配器是否就绪
    if (!this.advertiseReady) {
      console.log('🔄 蓝牙广播适配器未就绪，跳过发送')
      return
    }

    // 3. 检测系统平台并生成BLE数据包
    const systemStr = (this.system || '').toLowerCase()
    const isIos = systemStr.indexOf('ios') >= 0
    const actPayload = generateData(payload, isIos)

    // 4. 🎯 不停止之前的广播，直接开始新的广播
    console.log('🔄 无缝切换广播内容')
    this.startAdvertising(actPayload)

    // 5. 设置设备响应检测
    this.setupResponseDetection(payload, 3000)  // 3秒超时
  }

  /**
   * 发送档位指令
   * @param {number} gear - 档位 (1-3)
   * @param {boolean} keepBroadcasting - 是否保持当前广播（运动过程中的档位切换）
   */
  sendGear(gear, keepBroadcasting = false) {
    console.log('🔍 sendGear 被调用，档位:', gear)

    // 🎯 根据档位设置速度（使用配置文件）
    const speedValue = GEAR_SPEED_MAP[gear] || GEAR_SPEED_MAP[1]  // 默认1档

    const command = this.generateCarCommand(0x12, speedValue)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log(`🚗 发送档位指令: ${gear}档 (速度${speedValue.toString(16).toUpperCase()}):`, hexCommand)

    if (keepBroadcasting) {
      console.log('🎯 运动中切换档位，使用强制停止策略')
      // 运动过程中的档位切换，使用强制停止策略确保档位指令被处理

      // 保存当前运动指令
      const currentMotionPayload = this.payload

      // 强制停止所有广播
      this.forceStopAllAdvertising()

      // 等待更长时间确保广播完全停止
      setTimeout(() => {
        console.log('📤 发送档位指令（第1次）')
        // 发送档位指令
        this.payload = hexCommand
        this.startSendAndDiscovery()

        // 🎯 减少重复发送，降低系统负载
        // setTimeout(() => {
        //   console.log('📤 重复发送档位指令（第2次）')
        //   this.payload = hexCommand
        //   this.startSendAndDiscovery()
        // }, 300)

        // 设置响应检测，等待档位响应
        const checkResponse = () => {
          // 等待档位响应或超时后恢复运动
          setTimeout(() => {
            console.log('🔄 档位处理完成，恢复运动指令')
            this.payload = currentMotionPayload
            this.startSendAndDiscovery()
          }, 1000) // 给档位指令1000ms的处理时间
        }

        checkResponse()
      }, 200) // 等待200ms确保广播完全停止

    } else {
      // 独立的档位切换，正常发送
      console.log('🎯 直接发送档位指令')
      this.payload = hexCommand
      this.startSendAndDiscovery()
    }
  }

  /**
   * 发送自定义速度指令
   * @param {number} speedValue - 速度值 (0x0A-0x64, 即10-100)
   */
  sendCustomSpeed(speedValue) {
    console.log('🔍 sendCustomSpeed 被调用，速度值:', `0x${speedValue.toString(16).toUpperCase()}`)

    const command = this.generateCarCommand(0x12, speedValue)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log(`🚗 发送自定义速度指令 (速度0x${speedValue.toString(16).toUpperCase()}):`, hexCommand)

    // 🎯 检测是否在运动中（通过payload判断）
    const currentPayload = this.payload || ''
    const isActuallyMoving = currentPayload.includes('81000210011300FA') || currentPayload.includes('81000210021400FA')

    if (isActuallyMoving) {
      console.log('🔄 运动中设置速度，使用无缝模式')
      // 保存当前运动指令
      const savedMotionPayload = this.payload

      // 临时发送速度指令
      this.payload = hexCommand
      this.sendWithoutStop()

      // 短暂延迟后恢复运动指令
      setTimeout(() => {
        console.log('🔄 恢复运动指令:', savedMotionPayload)
        this.payload = savedMotionPayload
        this.sendWithoutStop()
      }, 100)
    } else {
      console.log('🎯 静止状态设置速度')
      this.payload = hexCommand
      this.startSendAndDiscovery()
    }
  }



  /**
   * 发送调速指令
   * @param {number} speedPercent - 速度百分比 (10-100)
   */
  sendSpeed(speedPercent) {
    const speed = Math.max(10, Math.min(100, speedPercent))
    const command = this.generateCarCommand(0x12, speed)
    const hexCommand = command
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase()

    console.log(`🚗 发送调速指令 ${speed}%:`, hexCommand)
    // 把指令赋值给 payload，然后通过 startSendAndDiscovery 发送
    this.payload = hexCommand
    this.startSendAndDiscovery()
  }
}


export default new ToyCarBleUnified()

/**
 * 文件总结：
 * 这个文件实现了一个RF通信测试工具的核心功能：
 *
 * 主要功能：
 * 1. 发送功能：将用户输入的payload转换为BLE广播包发送给RF设备
 * 2. 接收功能：扫描并接收RF设备发送的BLE广播数据
 * 3. 配置管理：跳转到配置页面设置RF地址、信道等参数
 * 4. 数据管理：显示、复制、清空接收到的数据
 *
 * 技术特点：
 * - 支持iOS和Android平台的不同BLE封装方式
 * - 实现XN297L RF协议到BLE协议的转换
 * - 提供实时数据接收和显示功能
 * - 包含完整的错误处理和状态管理
 *
 * 数据流程：
 * 发送：用户输入 → 数据验证 → BLE转换 → 平台适配 → BLE广播 → RF设备接收
 * 接收：RF设备发送 → BLE广播 → 设备过滤 → 数据提取 → 界面显示
**/