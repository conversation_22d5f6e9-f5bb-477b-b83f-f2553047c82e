<template>
  <!--环保袋套餐-组件-->
  <view class="comContent">
    <view class="bagList">
      <view class="bagItem flexColumnAllCenter" v-for="(item, index) in bagMealList" :key="index" :class="{
        placeItem: !item.goods_name,
        selectBagItem: curSelectIndex == index,
      }" @click="onClickBagItem(item, index)">
        <view class="name">{{ item.goods_name }}</view>
        <view class="price">
          <view class="unit">¥</view>
          <view class="value">{{ item.good_price }}</view>
        </view>
      </view>
      <view style="width: 210rpx"></view>
      <view style="width: 210rpx"></view>
    </view>
  </view>
</template>

<script>

export default {
  name: "BagMeal",
  props: {
    pointPlaceInfo: Object,
    bagMealList: { type: Array, default: [] },
  },

  data() {
    return {
      curSelectIndex: 0,
    };
  },

  methods: {
    onClickBagItem(item, index) {
      this.curSelectIndex = index;
      this.$emit("selectBagItem", { item });
    },
  },


};
</script>

<style scoped lang="scss">
.comContent {
  .bagList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .bagItem {
      // width: 210rpx;
      // height: 160rpx;
      background: #f2f2f2;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      padding: 20rpx 20rpx;

      .name {
        color: $textBlack;
        font-weight: bold;
        font-size: $font-size-xlarge;
      }

      .price {
        margin-top: 20rpx;
        color: $mainRed;
        display: flex;
        align-items: flex-end;

        .unit {
          margin-bottom: 0rpx;
          font-size: $font-size-xsmall;
        }

        .value {
          margin-left: 6rpx;
          font-weight: bold;
          font-size: $font-size-middle;
        }
      }
    }

    .selectBagItem {
      background-color: $themeColor;

      .name {
        color: white;
      }

      .price {
        color: white;
      }
    }

    .placeItem {
      visibility: hidden;
    }
  }
}
</style>
