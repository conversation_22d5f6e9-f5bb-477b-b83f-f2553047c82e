import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

let lifeData = {};
try {
    // 尝试获取本地是否存在lifeData变量，第一次启动APP时是不存在的
    lifeData = uni.getStorageSync('lifeData');
} catch (e) {

}
// 需要永久存储，且下次APP启动需要取出的，在state中的变量名
let saveStateKeys = ['vSession3rd', 'vIsLogin', 'vIsLocation','vAppId',"vUrl",'vUserName','vHName'];

// 保存变量到本地存储中
const saveLifeData = function (key, value) {
    // 判断变量名是否在需要存储的数组中
    if (saveStateKeys.indexOf(key) != -1) {
        // 获取本地存储的lifeData对象，将变量添加到对象中
        let tmp = uni.getStorageSync('lifeData');
        // 第一次打开APP，不存在lifeData变量，故放一个{}空对象
        tmp = tmp ? tmp : {};
        tmp[key] = value;
        // 执行这一步后，所有需要存储的变量，都挂载在本地的lifeData对象中
        uni.setStorageSync('lifeData', tmp);
    }
}

const store = new Vuex.Store({
    state: {
        // 如果上面从本地获取的lifeData对象下有对应的属性，就赋值给state中对应的变量
        // 加上vuex_前缀，是防止变量名冲突，也让人一目了然
        // vuex_user: lifeData.vuex_user ? lifeData.vuex_user : {name: '明月'},
        // vuex_token: lifeData.vuex_token ? lifeData.vuex_token : '',
        // 如果vuex_version无需保存到本地永久存储，无需lifeData.vuex_version方式
        // vuex_version: '1.0.1',
        vBaseURL: 'https://ppj.handaiwulian.com',
        // vBaseURL: 'https://hd.handaiwulian.com',
        vIphoneXBottomHeight: 0,//iphonex 底部安全区域高度
        vIsLogin: lifeData.vIsLogin ?? false,//是否已经登录
        vSession3rd: lifeData.vSession3rd,//用户Token
        vTel: '4000-027-115',//客服热线
        vSiteConfig: {},//网站配置信息
        vAppId:lifeData.vAppId,
        vUrl:lifeData.vUrl,
        vCurLocation: {
            longitude: '',//经度
            latitude: '',//纬度
        },
        vIsLocation: JSON.parse(lifeData.vIsLocation ?? false) ?? false,//是否获取到地理位置信息
        vServicePhone: "4000-027-115",//联系电话
        vAppName: '汉骑士',//小程序名称
        vDeviceSn: '',//保存的全局设备编号
        vDeviceType: '',//设备类型
        vPointInfo: {},//位置信息
        vCreateOrderInfo: {},//创建的订单信息
        vBaseInfo: {},//网站配置信息
        vRewardedVideoAd: "",//激励视频广告引用
        vMemberInfo:lifeData.vMemberInfo, // 会员信息，后台服务器的用户信息， vUserInfo 是授权获取的信息
        vAd: {
            //各种广告id
            //激励视频广告id
            rewardedVideoAdId: "",//adunit-3ab6ab1b74671af4
            //首页插屏广告位
            homeInsertScreenAd: "",//adunit-a2ca851ed515839f
            // 地图首页产品广告
            ScreenAdHomeEarth:"adunit-8b2d0d108bc79f94",
            //我的个人中心列表
            personalCenterInsertScreenAd: "",//adunit-c84887f856476f01
            //我的出袋订单详情插屏广告
            myOutBagDetailsInsertScreenAd: "",//adunit-58cb9fc9372090f6
            //招募合伙人页面插屏广告
            companionInsertScreenAd: "",//adunit-c8049d6af1831436

            //个人中心banner广告位
            personalCenterBannerAd: "adunit-8f72ef8698c241f4",
            //购买商品 免费领取页面banner广告
            buyProductBannerAd: "adunit-f0941a0fa9be1e4b",
            //商铺详情页banner广告
            pointPlaceDetailsBannerAd: "",//adunit-1b6c01cfaf6fbbc5

            //个人中心列表页原生广告位
            personalCenterListCustomAd: "adunit-c14bd671230c4e0f",
            //商城小店原生广告
            pointPlaceGoodsCustomAd: "",//adunit-f34c3f5d89c0276e
            //我的出袋订单详情原生广告
            myOutBagDetailsCustomAd: "",//adunit-1fa95fe16045f3de

            //广告投放页面视频广告
            advertiseVideoAd: "",//adunit-c6dd832bbc08eb70
            //购买商品 免费领取页面视频广告
            buyProductVideoAd: "",//adunit-d5bee51330f4f66e

            // 骑行界面 Banner 广告
            gameDetailBannerAd:"adunit-6c174703a6f4349c",
            // 骑行界面 ScreenAd 
            screenAdGameDetail : "adunit-ed881bb8638bf9b3",
            /* 购买界面轮播广告 */
            swiperBannerAd:"adunit-796785b58fec1500",
            /* 订单详情 Banner 广告 */
            myOutBagBanner:"adunit-4b69a20b28ec4008"
        },
        vPageFullPath: '',//当前页面路径参数
        vIsBleDevice: false,//是否是蓝牙设备
        vPositioning:false,//是否需要定位
        https:"https://*************:80",
        vHName:lifeData.vHName,
        vUserName:lifeData.vUserName
    },
    mutations: {
        $uStore(state, payload) {
            // 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
            let nameArr = payload.name.split('.');
            let saveKey = '';
            let len = nameArr.length;
            if (nameArr.length >= 2) {
                let obj = state[nameArr[0]];
                for (let i = 1; i < len - 1; i++) {
                    obj = obj[nameArr[i]];
                }
                obj[nameArr[len - 1]] = payload.value;
                saveKey = nameArr[0];
            } else {
                // 单层级变量，在state就是一个普通变量的情况
                state[payload.name] = payload.value;
                saveKey = payload.name;
            }
            // 保存变量到本地，见顶部函数定义
            saveLifeData(saveKey, state[saveKey])
        },
    }
})


export default store