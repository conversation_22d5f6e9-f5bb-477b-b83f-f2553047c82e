<template>
    <view class="content">
        <image class="topDetailsImg" :src="pointPlaceInfo.img" />
        <view class="detailsInfo">
            <PointDetails :pointPlaceInfo="pointPlaceInfo" :navIsShow="true" />
        </view>
        <!-- #ifndef MP-TOUTIAO -->
        <view class="recommend-goods" v-if="goodsList.length > 0">
            <view class="top">
                <view class="top-title">商品推荐</view>
                <view class="top-more" @click="goGoodsList">更多</view>
            </view>
            <view class="card">
                <view v-for="(item, index) in goodsList" :key="item.goods_id" class="card-item">
                    <PlaceGoodsCard :info="item" :index="index" :isShowNum="false" :isShow="false" />
                </view>

            </view>
        </view>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <CommonAd :ad="vAd.pointPlaceDetailsBannerAd||''" type="banner" />
        <!-- #endif -->
        <LoginPopup />
    </view>
</template>

<script>
import { getGoodsList } from "@/common/http/api";
import PointDetails from '../../components/PointDetails.vue';
import PlaceGoodsCard from '../components/PlaceGoodsCard.vue';
import CommonAd from '../../components/WxAd/CommonAd.vue';
import LoginPopup from '../../components/LoginPopup.vue';
export default {
    components: { PointDetails, PlaceGoodsCard, CommonAd, LoginPopup },
    data() {
        return {
            pointPlaceInfo: {},
            goodsList: [],
        };
    },

    methods: {
        getGoodsList() {
            let data = { device_sn: this.pointPlaceInfo.device_sn, page: 1 };

            getGoodsList(data).then((res) => {
                console.log(res);
                this.goodsList = res.data?.slice(0, 2);
            });
        },
        goGoodsList() {
            uni.navigateTo({
                url:
                    "/pagesB/placeGoods/index?device_sn=" +
                    this.pointPlaceInfo.device_sn,
            });
        },
    },
    onLoad() {
        this.pointPlaceInfo = this.vPointInfo;
        this.getGoodsList()
    },
};
</script>

<style lang="scss" scoped>
.topDetailsImg {
    position: relative;
    z-index: 0;
    width: 100%;
    height: 400rpx;
    background-color: #cccccc;
}

.detailsInfo {
    position: relative;
    z-index: 1;
    padding: 30rpx;
    box-sizing: border-box;
    border-radius: 20rpx 20rpx 0 0;
    margin-top: -40rpx;
    background-color: #fff;
}

.adContainer {
    margin-top: 30rpx;
}

.recommend-goods {
    background-color: #f4f4f4;
    padding: 20rpx;

    .top {
        display: flex;
        justify-content: space-between;

        &-title {
            color: $textBlack;
            font-size: $font-size-middle;
            font-weight: 700;
        }

        &-more {
            color: $themeColor;
            font-size: $font-size-small;
        }
    }

    .card {
        display: flex;
        justify-content: space-between;
        margin-top: 20rpx;

        &-item {
            &:nth-child(n+2) {
                margin-left: 20rpx;
            }
        }
    }
}
</style>
