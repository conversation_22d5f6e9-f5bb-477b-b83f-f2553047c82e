<template>
  <view class="goods card" @click="onClick">
    <radio
      v-if="isShow"
      value=""
      :checked="info.isCheck"
      style="transform: scale(0.7)"
      color="#007aff"
    />
    <view class="goods-box">
      <view class="goods-img" @click.stop="goDetails">
        <image class="img" :src="info.original_img" />
      </view>
      <view class="goods-content">
        <view class="goods-content-name textMaxTwoLine">{{
          info.goods_name
        }}</view>
        <view class="goods-content-num" v-if="isShowNum">
          x{{ info.isAmount }}
        </view>
        <view class="goods-content-box">
          <view class="goods-content-box-price">
            <text>￥</text>
            <text class="money">{{ info.shop_price }}</text>
          </view>
          <view
            v-if="isShow"
            class="goods-content-box-number"
            @click.stop="tap"
          >
            <u-number-box
              @change="changeNumberBox"
              :long-press="false"
              :index="index"
            ></u-number-box>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "PlaceGoodsCard",
  props: {
    info: { type: Object, default: {} },
    index: { type: Number | String, default: 0 },
    isShow: { type: Boolean, default: true },
    isShowNum: { type: Boolean, default: true },
  },
  data() {
    return {
      changeInfo: {},
      debounceTimer: null,
    };
  },
  methods: {
    tap() {},
    goDetails() {
      //   uni.navigateTo({
      //     url:
      //       "/homePages/PointPlaceDetail/PointPlaceGoodsDetails?goods_id=" +
      //       this.info.goods_id,
      //   });
    },
    changeNumberBox(e) {
      this.changeInfo = e;
      this.debounce(this.valChange); //防抖
    },
    debounce(fn) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        fn();
      }, 500);
    },
    valChange() {
      this.$emit("checkValue", { ...this.changeInfo, index: this.index });
    },
    //改变选中状态
    onClick() {
      this.$emit("onCheck");
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  background-color: #fff;
  border-radius: 20rpx;
}

.goods {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;

  .goods-box {
    flex: 1;
    display: flex;
    align-items: center;
  }

  &-img {
    width: 160rpx;
    height: 160rpx;
    flex-shrink: 0;

    .img {
      width: 100%;
      height: 100%;
    }
  }

  &-content {
    flex: 1;
    margin-left: 20rpx;
    height: 160rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-name {
      color: $textBlack;
      font-weight: 700;
      font-size: $font-size-middle;
    }

    &-specs {
      color: $textDarkGray;
      font-size: $font-size-base;
      margin: 16rpx 0;
    }

    &-box {
      display: flex;
      justify-content: space-between;
      font-weight: 700;

      &-price {
        color: #ef0000;
        font-size: $font-size-xsmall;

        .money {
          font-size: $font-size-middle;
        }
      }
    }
  }
}
</style>
