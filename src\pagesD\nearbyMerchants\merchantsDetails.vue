<template>
  <view class="content">
    <view class="merchants">
      <image
        class="merchants-left"
        :src="hotelInfo.img || '/static/icon/logo.png'"
      />
      <view class="merchants-center">
        <view class="merchants-center-title textMaxOneLine">{{
          hotelInfo.hotelName
        }}</view>
        <view class="merchants-center-introduce">欢迎光临</view>
        <view class="merchants-center-phone"
          >电话：{{ hotelInfo.linkmanPhone }}</view
        >
      </view>
      <view class="merchants-right" @click="onClickBtnNav">
        <BaseIcon name="map-fill" color="#1451ff" />
        <text class="merchants-right-text">一键导航</text>
      </view>
    </view>
    <view class="wifi">
      <view class="describe">
        <view class="describe-item">
          <BaseIcon name="wifi" color="#f04924" />
          <text class="describe-item-text">一键连接</text>
        </view>
        <view class="describe-item">
          <BaseIcon name="lock-fill" color="#f04924" />
          <text class="describe-item-text">无需密码</text>
        </view>
        <view class="describe-item">
          <BaseIcon name="eye-fill" color="#f04924" />
          <text class="describe-item-text">保护隐私</text>
        </view>
      </view>
      <view class="wifi-main">
        <image class="wifi-main-img" src="/pagesD/static/wifi_max.png" />
        <view class="wifi-main-btn" @click="go">一键安全连接WiFi</view>
        <view class="wifi-main-tips">连接前请开启手机WiFi及定位功能</view>
      </view>
    </view>
    <view class="coupon" v-if="couponList.length">
      <view class="coupon-title">本店活动</view>
      <view class="coupon-list">
        <NearbyCouponCard
          v-for="item in couponList"
          :key="item.id"
          :info="item"
          :placeName="hotelInfo.hotelName"
        />
      </view>
    </view>
  </view>
</template>
<script>
import { getHotelDetail, getHotelCoupons } from "@/common/http/api";
import BaseIcon from "../../components/base/BaseIcon.vue";
import NearbyCouponCard from "./components/NearbyCouponCard.vue";
export default {
  components: { BaseIcon, NearbyCouponCard },
  data() {
    return {
      hotel_id: undefined,
      hotelInfo: {},
      couponList: [],
    };
  },

  methods: {
    async getHotelDetailHandle() {
      let data = { hotel_id: this.hotel_id };
      this.hotelInfo = await getHotelDetail(data);
    },
    onClickBtnNav() {
      if (!this.hotelInfo.lat || !this.hotelInfo.lon)
        return this.isShowErr("该点位暂未设置位置信息~");
      uni.openLocation({
        latitude: parseFloat(this.hotelInfo.lat),
        longitude: parseFloat(this.hotelInfo.lon),
        name: this.hotelInfo.hotelName,
        address: this.hotelInfo.address,
        success: function (res) {
          console.log("导航返回值：", res);
        },
      });
    },
    async getHotelCouponsHandle() {
      let data = { hotel_id: this.hotel_id };
      this.couponList = (await getHotelCoupons(data)) || [];
    },
    go() {
      uni.navigateTo({ url: "/pagesB/wifi/index" });
    },
  },
  onLoad(opt) {
    console.log("🚀 ~ opt", opt);
    this.hotel_id = opt?.hotel_id;
    this.hotel_id &&
      this.getHotelDetailHandle() &&
      this.getHotelCouponsHandle();
  },
};
</script>
<style  lang='scss'>
page {
  background-color: #f4f4f4;
}
</style>

<style scoped  lang='scss'>
.content {
  padding: 20rpx 26rpx;
}
.merchants {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 230rpx;
  padding: 45rpx 22rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-sizing: border-box;
  &-left {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    flex-shrink: 0;
  }
  &-center {
    flex: 1;
    height: 100%;
    color: #464646;
    font-size: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 30rpx;
    &-title {
      font-size: 30rpx;
      font-weight: 700;
    }
  }
  &-right {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-text {
      color: #5a5a5a;
      font-size: 24rpx;
      margin-top: 24rpx;
    }
  }
}
.wifi {
  .describe {
    height: 94rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    &-item {
      display: flex;
      align-items: center;

      &-text {
        color: #464646;
        font-size: 24rpx;
        margin-left: 14rpx;
      }
    }
  }
  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 420rpx;
    border-radius: 20rpx;
    background-color: #fff;
    padding: 34rpx 0;
    box-sizing: border-box;
    &-img {
      width: 268rpx;
      height: 192rpx;
    }
    &-btn {
      width: 558rpx;
      height: 74rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 24rpx;
      border-radius: 37rpx;
      background-color: #00c2f3;
    }
    &-tips {
      color: #f14924;
      font-size: 24rpx;
    }
  }
}
.coupon {
  &-title {
    color: #585858;
    font-size: 36rpx;
    font-weight: 700;
    margin: 40rpx 0 36rpx;
  }
}
</style>