<template>
  <view class="card" @click="previewImage(info.qr_img)">
    <view class="card-left">
      <image class="card-left-img" src="/pagesB/static/coupon_bg.png" />
      <view class="card-left-main">
        <view class="card-left-box">
          <view class="card-left-box-unit">￥</view>
          <view class="card-left-box-price">
            {{ info.money }}
          </view>
        </view>
        <view class="card-left-title">优惠券</view>
      </view>
    </view>
    <view class="card-right">
      <view class="card-right-top">
        <view class="card-right-title textMaxOneLine">{{ info.name }}</view>
        <view>优惠</view>
      </view>
      <view class="card-right-line"></view>
      <view class="card-right-bottom">
        <view>{{ info.hotelName }}</view>

        <image class="card-right-bottom-img" :src="info.qr_img" />
      </view>
      <view
        class="card-right-write"
        :class="{ active: info.is_write_off === 0 }"
      >
        {{ writeOff }}
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    info: { type: Object, default: {} },
  },
  computed: {
    writeOff() {
      let is_write_off = {
        0: "未核销",
        1: "已核销",
      };
      return is_write_off[this.info.is_write_off] || "未知";
    },
  },
  data() {
    return {};
  },

  methods: {
    previewImage(urls) {
      uni.previewImage({
        urls: [urls],
      });
    },
  },
};
</script>


<style scoped  lang='scss'>
.card {
  height: 210rpx;
  display: flex;
  justify-content: space-between;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  overflow: hidden;
  &-left {
    flex: 0 0 315rpx;
    position: relative;
    &-img {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      border-radius: 10rpx 0 0 10rpx;
      z-index: 0;
    }
    &-main {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      z-index: 1;
    }
    &-box {
      display: flex;
      align-items: flex-end;

      &-unit {
        font-size: 30rpx;
      }
      &-price {
        font-size: 48rpx;
      }
    }
    &-title {
      font-size: 28rpx;
    }
  }
  &-right {
    position: relative;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: #757575;
    padding: 16rpx 16rpx 24rpx 28rpx;
    box-sizing: border-box;
    &-top {
      flex: 0 0 46%;
      font-size: 26rpx;
    }
    &-bottom {
      flex: 0 0 46%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 26rpx;
      &-img {
        width: 84rpx;
        height: 84rpx;
        border-radius: 50%;
        flex-shrink: 0;
        margin-left: 10rpx;
        box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
      }
    }

    &-title {
      color: #5b5b5b;
      font-size: 30rpx;
      font-weight: 700;
    }
    &-line {
      flex: 1;
      margin: 10rpx 0;
      border-top: 2rpx dashed #e3e3e3;
    }
    &-write {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 26rpx;
      border-radius: 0 0 10rpx 10rpx;
      background-color: #9099a7;
      color: #fff;
      padding: 16rpx 26rpx;
    }
    .active {
      background-color: #f46c6c;
    }
  }
}
</style>