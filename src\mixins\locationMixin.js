//这个混入用于初始化位置权限，获取当前位置经纬度


export const locationMixin = {
    data() {
        return {
            curLatitude: '',
            curLongitude: '',
            //用户当前所处市级
            curCity: '',
            //到目标地址距离
            distance: ''
        }
    },


    methods: {
        //初始化位置授权
        initLocPermission(callback) {
            let that = this
            uni.authorize({
                scope: 'scope.userLocation',
                success(res) {
                    //存储获得授权flag
                    console.log('调用initLocPermission,并且成功获得授权')
                    uni.$u.vuex('vIsLocation', true)
                    if (callback)
                        callback()

                },
                // 授权失败
                fail(err) {
                    console.log("授权失败err", err);
                    uni.showModal({
                        title: '提示',
                        content: '未获取到位置授权，是否授权位置信息？',
                        showCancel: true,
                        success: ({ confirm, cancel }) => {
                            if (confirm) {
                                uni.openSetting({
                                    success(res) {
                                        console.log('openSetting成功回调，res=', res)

                                        // 如果用户授权了地理信息在，则提示授权成功
                                        if (res.authSetting['scope.userLocation'] === true) {
                                            //存储获得授权flag
                                            uni.$u.vuex('vIsLocation', true)

                                            if (callback)
                                                callback()
                                        } else {

                                            uni.showToast({
                                                title: "授权失败",
                                                icon: "none",
                                                duration: 1500
                                            })
                                        }
                                    },
                                    fail(err) {
                                        uni.showToast({
                                            title: "未授权位置信息~",
                                            icon: "none",
                                            duration: 1500
                                        })
                                        console.log("未获得授权", err);
                                    }
                                })
                            }

                        }
                    })

                }
            })
        },

        //获取当前位置
        getCurrentLocation(successCallback) {
            let that = this
            uni.getLocation({
                // #ifdef MP-WEIXIN
                type: "gcj02",   // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
                // #endif
                success: function (res) {
                    console.log('获取的当前位置信息：', res)
                    const { longitude, latitude } = res
                    that.curLongitude = res.longitude
                    // console.log("🚀 ~  that.curLongitude", that.curLongitude)
                    that.curLatitude = res.latitude
                    // console.log("🚀 ~  that.curLatitude", that.curLatitude)

                    uni.$u.vuex('vCurLocation', {
                        longitude,
                        latitude
                    })
                    uni.$u.vuex('vIsLocation', true)
                    if (successCallback)
                        successCallback()
                },
                fail: err => {
                    console.log('获取当前位置失败:', err)
                    if (err.errMsg.includes('ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF')) {
                        uni.showModal({
                            title: "提示",
                            content: "获取当前位置失败，请检查您是否打开了系统定位开关",
                            showCancel: false,
                        })

                        return
                    }
                }
            })
        },

        //选取当前位置
        chooseLocation(successCallback, failCallback) {
            uni.chooseLocation({
                success: res => {
                    console.log('选取位置成功：', res)
                    if (successCallback)
                        successCallback(res)
                },
                fail: err => {
                    console.log('选取位置失败：', err)
                    if (failCallback)
                        failCallback(err)
                }
            })
        },
    }
}
