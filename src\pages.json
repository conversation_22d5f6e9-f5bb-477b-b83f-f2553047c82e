{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
			"LoginPopup": "@/components/LoginPopup.vue",
			"BaseNavbar": "@/components/base/BaseNavbar.vue",
			"CommonAd": "@/components/WxAd/CommonAd.vue"
		}
	},
	/* #ifndef H5 */
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "共享泡泡机",
				"navigationStyle": "custom",
				"mp-toutiao": {
					"navigationStyle": "default"
				},
				"mp-alipay": {
					"transparentTitle": "always",
					"allowsBounceVertical": "NO",
					"pullRefresh": false,
					"titlePenetrate": "YES",
					"defaultTitle": ""
				}
			}
		}
	],
	/* #endif */
	/* #ifdef H5 */
	"pages": [
		{
			"path": "pagesB/product/index",
			// "path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "共享泡泡机",
				"navigationStyle": "custom",
				"mp-toutiao": {
					"navigationStyle": "default"
				},
				"mp-alipay": {
					"transparentTitle": "always",
					"allowsBounceVertical": "NO",
					"pullRefresh": false,
					"defaultTitle": ""
				}
			}
		}
	],
	/* #endif */
	"subPackages": [
		{
			"root": "pagesB",
			"pages": [
				{
					"path": "product/index",
					"style": {
						"navigationBarTitleText": "共享泡泡机",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false,
						"mp-toutiao": {
							"navigationStyle": "default"
						},
						"mp-alipay": {
							"transparentTitle": "always",
							"allowsBounceVertical": "NO",
							"pullRefresh": false,
							"titlePenetrate": "YES",
							"defaultTitle": ""
						}
					}
				},
				{
					"path": "product/ConfirmOrder",
					"style": {
						"navigationBarTitleText": "确认订单"
					}
				},
				{
					"path": "order/Order",
					"style": {
						"navigationBarTitleText": "我的订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/OrderDetails",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "partner/index",
					"style": {
						"navigationBarTitleText": "招募合伙人",
						"navigationStyle": "custom",
						"mp-toutiao": {
							"navigationStyle": "default"
						}
					}
				},
				{
					"path": "partner/ConfirmPartner",
					"style": {
						"navigationBarTitleText": "确认认养"
					}
				},
				{
					"path": "marketOrder/index",
					"style": {
						"navigationBarTitleText": "商城订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "advertiseOrder/index",
					"style": {
						"navigationBarTitleText": "广告订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "advertiseOrder/AdvertiseOrderDetails",
					"style": {
						"navigationBarTitleText": "广告订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "investOrder/index",
					"style": {
						"navigationBarTitleText": "认养订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "placeDetails/index",
					"style": {
						"navigationBarTitleText": "商铺详情"
					}
				},
				{
					"path": "placeGoods/index",
					"style": {
						"navigationBarTitleText": "商户小店"
					}
				},
				{
					"path": "placeGoods/PlaceGoodsConfirmOrder",
					"style": {
						"navigationBarTitleText": "确认订单"
					}
				},
				{
					"path": "wifi/index",
					"style": {
						"navigationBarTitleText": "连接Wi-Fi"
					}
				},
				{
					"path": "wifi/iosTip",
					"style": {
						"navigationBarTitleText": "IOS连接帮助"
					}
				},
				{
					"path": "coupon/CouponList",
					"style": {
						"navigationBarTitleText": "优惠券联盟"
					}
				},
				{
					"path": "chargeDetails/index",
					"style": {
						"navigationBarTitleText": "充电详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "chargeDetails/gaming",
					"style": {
						"navigationBarTitleText": "游戏详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "product/perGame",
					"style": {
						"navigationBarTitleText": "骑行详情"
					}
				},
				{
					"path": "product/preInfo",
					"style": {
						"navigationBarTitleText": "价格说明"
					}
				},
				{
					"path": "userCoupon/index",
					"style": {
						"navigationBarTitleText": "优惠券",
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pagesC",
			"pages": [
				{
					"path": "webView/WebView",
					"style": {}
				},
				{
					"path": "profile/Profile",
					"style": {
						"navigationBarTitleText": "个人中心",
						"navigationStyle": "custom",
						"mp-toutiao": {
							"navigationStyle": "default"
						},
						"mp-alipay": {
							"transparentTitle": "always",
							"allowsBounceVertical": "NO",
							"pullRefresh": false,
							"titlePenetrate": "YES",
							"defaultTitle": ""
						}
					}
				},
				{
					"path": "help/index",
					"style": {
						"navigationBarTitleText": "帮助中心"
					}
				},
				{
					"path": "about/index",
					"style": {
						"navigationBarTitleText": "关于我们",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "userServiceNotice/index",
					"style": {
						"navigationBarTitleText": "用户服务须知"
					}
				},
				{
					"path": "useInstruction/index",
					"style": {
						"navigationBarTitleText": "使用说明"
					}
				},
				{
					"path": "Scan/Scan",
					"style": {
						"navigationBarTitleText": "扫一扫",
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "pagesD",
			"pages": [
				{
					"path": "updataImge/updataImge",
					"style": {
						"navigationBarTitleText": "纪念时刻"
					}
				},
				{
					"path": "updataImge/template",
					"style": {
						"navigationBarTitleText": "选择模板"
					}
				},
				{
					"path": "nearbyMerchants/index",
					"style": {
						"navigationBarTitleText": "附近商户",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "nearbyMerchants/merchantsDetails",
					"style": {
						"navigationBarTitleText": "商户详情"
					}
				},
				{
					"path": "nearbyMerchants/merchantsType",
					"style": {
						"navigationBarTitleText": "商户类型",
						"navigationStyle": "custom",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "CommonPage/NearbyMerchants",
					"style": {
						"navigationBarTitleText": "附近商户",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "CommonPage/NearbyMerchantsDevice",
					"style": {
						"navigationBarTitleText": "商户设备列表"
					}
				},
				{
					"path": "wallet/index",
					"style": {
						"navigationBarTitleText": "我的钱包"
					}
				},
				{
					"path": "wallet/myWallet",
					"style": {
						"navigationBarTitleText": "我的汉币",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "wallet/consumeList",
					"style": {
						"navigationBarTitleText": "汉币明细",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "wallet/rechargeCenter",
					"style": {
						"navigationBarTitleText": "充值中心"
					}
				},
				{
					"path": "nearbyDevices/index",
					"style": {
						"navigationBarTitleText": "附近设备",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "advertise/index",
					"style": {
						"navigationBarTitleText": "广告投放"
					}
				},
				{
					"path": "advertise/AdConfirmTime",
					"style": {
						"navigationBarTitleText": "确认投放时间"
					}
				},
				{
					"path": "scoreTask/index",
					"style": {
						"navigationBarTitleText": "积分任务"
					}
				},
				{
					"path": "scoreTask/ScoreRecord",
					"style": {
						"navigationBarTitleText": "积分记录"
					}
				},
				{
					"path": "remote/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#F8F8F8"
	}
}