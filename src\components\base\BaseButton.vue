<template>
  <u-button class="defaultStyle" :ripple="true" :type="type" hover-class="none" :shape="shape" :text="text"
    :customStyle="customStyles" :color="color" :plain="plain" @click="onClick">
  </u-button>
</template>

<script>
export default {
  name: "BaseButton",
  props: {
    text: {
      type: String, //按钮类型primary default
      default: "按钮",
    },
    width: {
      type: String, //按钮宽度
      default: "auto",
    },
    height: {
      type: String,
      default: '80rpx'
    },
    type: {
      type: String, //按钮类型primary default
      default: "primary",
    },
    shape: {
      type: String, //按钮圆角 circle square
      default: "square",
    },
    color: String,
    plain: Boolean,
  },
  computed: {
    customStyles() {
      return {
        width: this.width || 'auto',
        height: this.height || '80rpx'
      };
    },
  },
  data() {
    return {};
  },
  methods: {
    onClick() {
      this.$emit("onClick");
    },
  },
  options: { styleIsolation: "shared" }, //组件必须加,才能修改内部样式
};
</script>

<style lang="scss" scoped>
</style>
