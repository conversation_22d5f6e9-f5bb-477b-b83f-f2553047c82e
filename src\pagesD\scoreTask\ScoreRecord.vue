<template>
    <view class="content">
        <!-- #ifdef MP-WEIXIN -->
        <CommonAd :ad="vAd.personalCenterListCustomAd||''" type="custom" />
        <!-- #endif -->
        <view class="top">
            <text class="title">总积分：</text>
            <text class="total">{{ totalScore }}</text>
        </view>
        <ComList :loadingType="loadingType" class="list">
            <view class="list-item" v-for="item in listData" :key="item.id">
                <view> {{ item.create_time }}</view>
                <view class="score">
                    <view v-if="item.sign == 0">支出积分：</view>
                    <view v-else>收入积分：</view>
                    <view :style="{ color: item.sign == 0 ? 'red' : '#00B3AB' }">{{
                            item.number
                    }}</view>
                </view>
            </view>
        </ComList>
    </view>
</template>

<script>
import { getPointList } from "@/common/http/api";
import myPull from "@/mixins/myPull.js";
import ComList from "@/components/list/ComList.vue";
import CommonAd from '@/components/WxAd/CommonAd.vue';
export default {
    components: { ComList, CommonAd },
    mixins: [myPull()],
    data() {
        return {
            totalScore: 0, //总积分
        };
    },
    methods: {
        getList(page, done) {
            let data = {
                page,
            };
            getPointList(data).then((res) => {

                let dataList = (res.data || []).map((item) => {
                    item.create_time = this.getLocalTime(item.create_time);
                    return item;
                });
                done(dataList);
                this.totalScore = res.point;

            });
        },
        getLocalTime(nS) {
            return new Date(parseInt(nS) * 1000)
                .toLocaleString()
                .replace(/:\d{1,2}$/, " ");
        },
    },
    onLoad(opt) {
        this.refresh();
    },
};
</script>
<style lang="scss">
page {
    background-color: #f4f4f4;
}
</style>
<style lang="scss" scoped>
.top {
    padding: 30rpx;

    .title {
        color: #333;
        font-size: 32rpx;
    }

    .total {
        color: red;
        font-size: 36rpx;
    }
}

.list {
    color: $textBlack;

    &-item {
        border-radius: 20rpx;
        background-color: #fff;
        padding: 30rpx;
        margin-bottom: 20rpx;
    }

    .score {
        display: flex;
        margin-top: 20rpx;
    }
}
</style>