<template>
  <!--我的订单页面-->
  <view class="content">
    <LoginPopup />
    <!-- <BaseTabs :list="orderType" @change="changeOrderTypeTab" /> -->
    <!-- <block>
      <BaseTabs
        v-if="selectOrderType === 0"
        :list="navArrCharge"
        @change="changeTab"
        :fontSize="10"
      />
      <BaseTabs
        v-else-if="selectOrderType === 1"
        :list="navCharge"
        @change="changeTab"
        :fontSize="10"
      />
    </block> -->

    <CommonAd :ad="vAd.personalCenterListCustomAd||''" type="custom" />
    <ComList :loadingType="loadingType">
      <block v-if="selectOrderType === 0">
        <OrderCard v-for="item in listData" :key="item.id" :itemInfo="item" />
      </block>
      <block v-else-if="selectOrderType === 1">
        <ChargeOrderCard v-for="item in listData" :key="item.id" :info="item" />
      </block>
    </ComList>

    <CommonAd :ad="vAd.personalCenterInsertScreenAd||''" type="inter" />
  </view>
</template>
<script>
import BaseTabs from '../../components/base/BaseTabs.vue'
import myPull from '@/mixins/myPull'
import OrderCard from '../components/OrderCard.vue'
import LoginPopup from '../../components/LoginPopup.vue'
import { getOrderList, getUserRechargeOrderList } from '@/common/http/api'
import ComList from '../../components/list/ComList.vue'
import CommonAd from '../../components/WxAd/CommonAd.vue'
import ChargeOrderCard from '../components/ChargeOrderCard.vue'
// import BaseNavbar from '../../components/base/BaseNavbar.vue'
export default {
  components: {
    BaseTabs,
    OrderCard,
    LoginPopup,
    ComList,
    CommonAd,
    ChargeOrderCard,
    // BaseNavbar
  },
  mixins: [myPull()],
  data() {
    return {
      navArrCharge: [
        {
          name: '全部',
          status: 0,
        },
        {
          name: '待支付',
          status: 3,
        },
        {
          name: '已完成',
          status: 1,
        },
        // {
        //   name: '订单异常',
        //   status: 2,
        // },
      ],
      navCharge: [
        {
          name: '全部',
          status: 0,
        },
        {
          name: '服务中',
          status: 2,
        },
        {
          name: '已完成',
          status: 3,
        },
      ],
      orderType: [
        {
          name: '普通订单',
          type: 0,
        },
        {
          name: '充电订单',
          type: 1,
        },
      ],
      selectNav: 0,
      selectOrderType: 0,
    }
  },

  methods: {
    changeOrderTypeTab(item) {
      this.selectOrderType = item?.type
      if (item.type == 0) {
        this.selectNav = this.navArrCharge[0]?.status
      } else if (item.type == 1) {
        this.selectNav = this.navCharge[0]?.status
      }
      this.refresh()
    },
    changeTab(item) {
      this.selectNav = item.status
      this.refresh()
    },
    async getList(page, done) {
      let data = {
          page,
          limit: 10,
          status: this.selectNav,
        },
        res
      let type = this.selectOrderType
      if (type === 0) {
        res = await getOrderList(data)
      } else if (type === 1) {
        res = await getUserRechargeOrderList(data)
      }
      res && done(res?.data)
    },
  },
  onLoad() {
    this.refresh()
  },
}
</script>

<style lang="scss">
page {
  background-color: $pageBgColor;
}
</style>

<style scoped lang="scss">
::v-deep .u-tabs__wrapper__nav__item__text {
  font-size: 30rpx !important;
}
::v-deep .u-tabs__wrapper__nav {
  width: 100%;
}
</style>
