<template>
  <view class="content" :class="info.is_recommend ? 'box_show' : ''" @click="buyHandle">
    <!-- 顶部区域 -->
    <view class="cargo-way">
      <view class="way">{{ info.goods_name }}</view>
      <view class="recomment" v-if="info.is_recommend">推荐</view>
    </view>

    <!-- 中间内容区域 -->
    <view class="flex btwn">
      <view class="btwn_left">
        <view class="flex ented">
          <view class="price">
            <view>￥</view>
            <view>{{ info.good_price
              }}{{ info.unit === 3 ? "元/" + info.game_time + "分钟" : "" }}</view>
          </view>
          <view class="vip_price" v-if="info.vip_price * 1">
            <text class="vip_text">
              会员￥{{ info.vip_price }}元
            </text>

          </view>
        </view>
        <view class="name">
          <view class="name_title" :style="{ width: info.count + '%' }">
            <view class="title"></view>
          </view>
        </view>
      </view>


      <view class="buy" hover-class="buy-active" :hover-start-time="0" :hover-stay-time="200">
        购买
      </view>
    </view>

    <!-- </view> -->
    <view class="hot" v-if="info.is_hot">
      火爆
    </view>
  </view>
</template>
<script>
export default {
  props: {
    info: { type: Object, default: {} },
  },
  data() {
    return {};
  },

  methods: {
    buyHandle() {
      this.$emit("click");
    },
  },
};
</script>

<style scoped lang='scss'>
.box_show {
  box-shadow: 5rpx 5rpx 20rpx rgba(0, 0, 0, 0.6), -5rpx -5rpx 20rpx rgba(98, 1, 1, 0.4);
  /* 双重阴影 */
}

.recomment {
  margin-left: 10rpx;
  position: relative;
  display: inline-block;
  padding: 5rpx 10rpx;
  background-color: #fbab17;
  /* 浅灰色背景 */
  // border: 1rpx solid #ccc; /* 灰色边框 */
  border-radius: 10rpx;
  /* 圆角 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  /* 阴影 */
  max-width: 200px;
  /* 最大宽度 */
  word-wrap: break-word;
  /* 长单词换行 */
  font-size: 20rpx;
}

/* 气泡箭头指示器（默认指向下方，需通过.left类调整） */
.recomment::after {
  content: '';
  position: absolute;
  top: 45%;
  /* 初始位置设置为气泡底部 */
  right: 100%;
  /* 水平居中 */
  margin-left: -10rpx;
  /* 箭头宽度的一半且方向相反，用于垂直居中 */
  border-width: 8rpx;
  /* 箭头大小 */
  border-style: solid;
  border-color: #fbab17 transparent transparent transparent;
  /* 箭头颜色设置 */
  transform: rotate(90deg);
  /* 箭头方向 */
}

.content {
  position: relative;
  // height: 150rpx;
  border-radius: 10rpx;
  background-color: rgb(63, 149, 232);
  overflow: hidden;
  // box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.15);
  border: 1px solid rgb(54, 89, 157);
  font-weight: 600;
  padding: 10rpx 20rpx;
}

.vip_price {
  // width: 200rpx;
  margin-left: 10rpx;
  height: 40rpx;
  background-image: url('../../static/btnbg.png');
  background-repeat: no-repeat;
  background-clip: content-box;
  background-position: center;
  background-size: 100%;
  color: #5c2700;
  display: flex;
  justify-content: center;
  align-items: center;

  .vip_text {
    font-size: 16rpx;
    font-weight: bold;
    padding: 5rpx 5rpx;
  }
}

.flex {
  display: flex;
}

.btwn {
  justify-content: space-between;
  align-items: flex-end;

  .btwn_left{
    width: 460rpx;
  }
}

.ented {
  align-items: flex-end;
  margin: 10rpx 0;
}

.top {
  position: relative;
  height: 94rpx;
}

.hot {
  position: absolute;
  right: -66rpx;
  top: -22rpx;
  background-color: red;
  font-size: 22rpx;
  color: #fee425;
  padding: 40rpx 60rpx 10rpx 60rpx;
  transform: rotate(45deg);
  box-shadow: 5rpx 5rpx 20rpx rgba(0, 0, 0, 0.5), -5rpx -5rpx 20rpx rgba(255, 0, 0, 0.5);
  /* 双重阴影 */
  border-radius: 10rpx;
  /* 圆角增加柔和感 */
  transition: box-shadow 0.3s, transform 0.3s;
  /* 添加过渡效果 */
  transform: rotate(45deg) scale(1.1);
  /* 旋转并稍微放大 */
}


.name {
  display: flex;
  box-sizing: border-box;
  // margin-top: 15rpx;
  // margin-left: 30rpx;
  width: 100%;
  height: 25rpx;
  border: 1px solid rgb(39, 105, 178);
  overflow: hidden;
  border-radius: 10rpx;
  background: rgb(255, 255, 255) url("../../static/sport/5.png") no-repeat top center;
  background-size: 100% 100%;
  z-index: 1;
  position: relative;

  .name_title {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    overflow: hidden;

    // background: url('../../static/sport/6.png') no-repeat top center;
    // background-size: 100% 100%;
    .title {
      width: 422rpx;
      height: 100%;
      background: url("../../static/sport/6.png") no-repeat;
      background-size: 121% 100%;
    }
  }
}

.price {
  display: flex;
  // align-items: flex-end;
  color: white;
  line-height: 60rpx;

  :first-child {
    font-size: 25rpx;
    margin: auto;
  }

  :last-child {
    font-size: 35rpx;
    font-weight: bold;
  }
}

.buy {
  width: 150rpx;
  height: 66rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  border: 2rpx solid rgb(83, 72, 146);
  background: rgb(253, 12, 53);
  border-radius: 10rpx;
  margin-right: 8rpx;
  // margin-top: 15rpx;
  box-sizing: border-box;
}

.buy-active {
  opacity: 0.7;
}

// }
.cargo-way {
  // position: absolute;
  // top: 12rpx;
  // left: 30rpx;
  display: flex;
  align-items: center;
  color: white;

  .way {
    padding: 5rpx 10rpx;
    max-width: 430rpx;
    background: rgb(22, 90, 195);
    border-radius: 10rpx;
    text-align: center;
    margin-right: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 25rpx;
  }
}
</style>