<template>
  <view>
    <BaseNavbar :title="title" />
    <ComList :loadingType="loadingType">
      <view class="list-top-card">
        <view class="title-box">
          <view class="title-box-line"></view>
          <view class="title-box-text">
            <view>附近</view>
            <view class="recommend">{{ title }}</view>
            <view>品牌</view>
          </view>
          <view class="title-box-line"></view>
        </view>
        <view class="sort-box">
          <view
            v-for="(item, index) in screenType"
            :key="item.name"
            :class="{ active: item.active }"
            @click="screenTypeHandle(index)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
      <NearbyMerchantsCard
        v-for="item in listData"
        :key="item.id"
        :info="item"
      />
    </ComList>
  </view>
</template>
<script>
import BaseNavbar from "../../components/base/BaseNavbar.vue";
import ComList from "../../components/list/ComList.vue";
import NearbyMerchantsCard from "./components/NearbyMerchantsCard.vue";
import myPull from "@/mixins/myPull.js";
import { getNearHotels } from "@/common/http/api";
export default {
  components: { BaseNavbar, ComList, NearbyMerchantsCard },
  mixins: [myPull()],
  data() {
    return {
      title: "商户分类",
      screenType: [
        {
          name: "距您最近",
          status: 1,
          active: false,
        },
        {
          name: "推荐排序",
          status: 2,
          active: true,
        },
        {
          name: "人气优先",
          status: 3,
          active: false,
        },
      ],
      sort_type: 2,
    };
  },

  methods: {
    screenTypeHandle(index) {
      this.screenType = this.screenType?.map((item, i) => {
        item.active = i === index;
        item.active && (this.sort_type = item.status);
        return item;
      });
      this.refresh();
    },
    getList(page, done) {
      let data = {
        page,
        limit: 10,
        lon: this.vCurLocation.longitude || 114.32168,
        lat: this.vCurLocation.latitude || 30.37559,
        radius: 1000,
        cateid: this.cateid,
        sort_type: this.sort_type,
      };
      getNearHotels(data).then((res) => {
        done(res);
      });
    },
  },
  onLoad(opt) {
    this.cateid = opt?.id;
    this.title = opt?.title || "商户分类";
    this.refresh();
  },
};
</script>
<style lang="scss">
page {
  background-color: #f4f4f4;
}
</style>

<style scoped  lang='scss'>
.list-top-card {
  padding: 50rpx 26rpx 40rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
  .title-box {
    display: flex;
    justify-items: center;
    align-items: center;
    &-line {
      flex: 1;
      height: 2rpx;
      background-color: #c6c6c6;
    }
    &-text {
      flex: 1 0 auto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 28rpx;
      color: #666666;
      .recommend {
        color: #e70e0d;
      }
    }
  }
  .sort-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 26rpx;
    .active {
      font-weight: 700;
    }
  }
}
</style>