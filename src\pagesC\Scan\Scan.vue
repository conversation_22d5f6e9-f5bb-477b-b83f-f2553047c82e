<template>
  <view class="sectionview">
    <view id="reader"></view>
  </view>
</template>

<script>
import { Html5Qrcode } from 'html5-qrcode'
// import { getUrlParams, getUrlDynamicData } from '@/common/tools/utils.js'
import utils from '@/utils/utils'
export default {
  destroyed() {
    if (this.stops) {
      this.stop()
    }
  },
  data() {
    return {
      title: '扫描',
      Html5Qrcode: null,
      isNavigated: false,
      mid: '',
      batch: false, //批量
      hotel_id: '',
      stops: false,
    }
  },

  methods: {
    stop() {
      // 停止扫描器

      this.html5QrCode
        .stop()
        .then((ignore) => {
          // QR Code scanning is stopped.
          this.stops = true
          console.log('QR Code scanning stopped.', ignore)
        })
        .catch((err) => {
          // Stop failed, handle it.
          console.log('Unable to stop scanning.', err)
        })
    },
    async start() {
      if (this.isNavigated) {
        return // 如果已经执行过跳转，不再执行
      }
      await this.html5QrCode
        .start(
          { facingMode: 'environment' },
          {
            fps: 20, // 设置每秒多少帧
            qrbox: { width: 250, height: 250 }, // 设置取景范围
            // scannable, rest shaded.
          },
          async (decodedText, decodedResult) => {
           
            let result = decodeURIComponent(decodedText)
            let dataVsCode = utils.getUrlParams(result, 'vscode')
            let dataMid = utils.getUrlDynamicData(result, 'mid')
            let dataDeviceSn = utils.getUrlDynamicData(result, 'device_sn')
            let freeType =
              utils.getUrlParams(result, 'ft') ||
              utils.getUrlDynamicData(result, 'ft') ||
              ''
            if (dataVsCode) {
              console.log('扫码进入获取设备虚拟码vscode', dataVsCode)
              uni.navigateTo({
                url: `/pagesB/product/index?vscode=${dataVsCode}&ft=${freeType}`,
              })
            } else if (dataMid) {
              console.log('扫码进入获取设备mid', dataMid)
              uni.navigateTo({
                url: `/pagesB/product/index?mid=${dataMid}&ft=${freeType}`,
              })
            } else if (dataDeviceSn) {
              console.log('扫码进入获取设备device', dataDeviceSn)
              uni.navigateTo({
                url: `/pagesB/product/index?device_sn=${dataDeviceSn}&ft=${freeType}`,
              })
              
            } else {
              uni.showToast({
                title: '请扫描正确二维码',
                icon: 'error',
              })
            }
            this.stop()
          },
          (errorMessage) => {
            // parse error, ideally ignore it. For example:
            // console.log(`QR Code no longer in front of camera.`);
            console.log('暂无额扫描结果', errorMessage)
          },
        )
        .catch((err) => {
          // Start failed, handle it. For example,
          console.log(`Unable to start scanning, error: ${err}`)
          this.isShowErr('错误信息' + err)
        })
    },
    getCameras() {
      Html5Qrcode.getCameras()
        .then((devices) => {
          /**
           * devices would be an array of objects of type:
           * { id: "id", label: "label" }
           */
          console.log(devices, 'cameraId')
          // this.start();
          if (devices && devices.length) {
            this.html5QrCode = new Html5Qrcode('reader')
            // start开始扫描
            this.start()
          }
        })
        .catch((err) => {
          console.log('err', err)
          this.isShowErr('错误信息' + err)
        })
    },
  },
  mounted() {
    this.current = this.$route.query.current || 0

    setTimeout(() => {
      this.getCameras()
    }, 1000)
  },
  onLoad(opt) {
    if (opt.from == 'batch') {
      this.batch = true
      console.log('opt', opt)
      this.hotel_id = opt.hotel_id
    }
  },
  onshow() {
    setTimeout(() => {
      this.getCameras()
    }, 100)
  },
}
</script>

<style lang="scss" scoped>
.sectionview {
  background-color: #303030;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999999;

  #reader {
    margin-top: 50%;
  }
}
.btn {
  color: red;
}
</style>
