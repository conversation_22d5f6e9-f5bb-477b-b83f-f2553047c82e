const http = uni.$u.http
const setConfig = (lodingTitle, isShowLoading) => {
    return {
        isShowLoading,
        lodingTitle,
    }
}

//根据设备类型获取附近设备
export const getNearbyMachine = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getNearbyMachine', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取产品列表
export const getProduct = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getProduct', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//根据产品ID获取附近设备
export const getNearbyMachineByProductId = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getNearbyMachineByProduct', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//通过虚拟码获取设备信息 vscode虚拟码 获取mid或device_sn
export const getDeviceAllInfoByVscode = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getDeviceAllInfoByVscode', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据设备mid获取设备信息
export const getDeviceInfoByMid = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getDeviceAllInfoById', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据设备编码获取设备信息
export const getDeviceAllInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getDeviceAllInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



// 下单（充电线是创建订单同时返回了支付信息）
export const createOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//创建充电订单/预支付信息

export const createOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//能否免费领取
export const isGetFree = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/isGetFree', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//用户登录
export const login = (data = {}, loadingText = "授权中", showLoading = true) => {
    return http.post('/waapi/public/login', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//更新用户信息
export const updateUserInfo = (data = {}, loadingText = "正在获取信息", showLoading = true) => {
    return http.post('/waapi/user/updateUserInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//创建免费订单并出货
export const createFreeOrderAndOut = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createRakeOrderAndOut', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//订单  获取订单列表
export const getOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserOrderList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//订单 获取订单详情
export const getOrderStatus = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/order/getOrderStatus', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取网站基础配置信息：
export const baseInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/index/baseInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取底部菜单
export const getMiniMenu = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getMiniMenu', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取广告信息
export const getShopAndAd = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getShopAndAd', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取用户手机号

export const getPhoneNumber = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getPhoneNumber', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//投资设备 获取 投资设备总价
export const getInvestPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getInvestPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取精选商城订单
export const getMySelectOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/select_mall/getMySelectOrderList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//广告订单 获取我投放的广告订单
export const getMemberApplyAdList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberApplyAdList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告订单 获取我投放的广告订单详情
export const getMemberApplyAdDetail = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberApplyAdDetail', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 获取设备的广告出价
export const getAdPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getAdPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 设备可投放时间段
export const decideAdTime = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/decideAdTime', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//广告 申请广告投放并支付
export const createAdOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createAdOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//投资设备 获取附近设备
export const getHotelInvestPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getHotelInvestPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//投放广告 获取附近设备
export const getMachinAdPrice = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getMachinAdPrice', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
///投资设备 获取投放订单
export const getMemberInvestList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberInvestList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//投资设备 提交订单
export const createInvestOrderAndPrepay = (data = {}, loadingText = "提交中~", showLoading = true) => {
    return http.post('/waapi/order/createInvestOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


// 预支付
export const prepay = (data = {}, loadingText = "支付中~", showLoading = true) => {
    return http.post('/waapi/pay/prepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

// 余额支付
export const payBalance = (data = {}, loadingText = "支付中~", showLoading = true) => {
    return http.post('/waapi/pay/payBalance', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取精选商城商品列表
export const getGoodsList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/Select_Mall/getGoodsList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//购买精选商城商品：
export const createGoodsOrderAndPay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/Select_Mall/createGoodsOrderAndPay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//统计广告
export const setAdLog = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/setAdLog', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//是否能看视频免费领取
export const getRemainCountMini = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getRemainCountMini', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//创建积分订单
export const createPointOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createPointOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//根据积分订单出货
export const pointOrderOut = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/PointOrderOut', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//获取积分任务
export const getTaskAll = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getTaskAll', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//添加积分
export const doneTask = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/doneTask', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取完成的积分任务
export const getTaskList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getTaskList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//积分记录
export const getPointList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/point/getPointList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//优惠券 获取优惠券列表
export const getCoupon = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/machine/getCoupon', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//优惠券 获取优惠券列表
export const getUserInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//更新订单状态
export const orderOutGoodsStatus = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/orderOutGoodsStatus', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//创建充电订单/预支付信息

export const createRechargeOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createRechargeOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//根据订单编号启动
export const openDoorByOrderSN = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/openDoorByOrderSN', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//获取用户单个订单信息
export const getUserRechargeOrderOne = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserRechargeOrderOne', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取用户单个订单信息
export const getUserOrderByNo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserOrderByNo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取用户单个订单信息 支持支付宝订单
export const getUserOrderByNoAll = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getOrderByNo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//结束充电并申请退款
export const endChargeOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/endChargeOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取用户在该设备的状态值
export const getUmDoing = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUmDoing', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取我的充电订单

// export const getUserRechargeOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
//     return http.post('/waapi/user/getUserRechargeOrderList', data, { custom: { ...setConfig(loadingText, showLoading) } })
// }
//获取我的新的订单

export const getUserRechargeOrderList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getUserDoingOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取附近商户 
export const getNearHotels = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/hotel/getNearHotels', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取.商户分类
export const getHotelCategoryOneLevel = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/hotel/getHotelCategoryOneLevel', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取.商户详情
export const getHotelDetail = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/hotel/getHotelDetail', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取.附近商户广告
export const getNearHotelAds = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/hotel/getNearHotelAds', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

//获取.用户领取优惠券
export const receiveHotelCoupon = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/receiveHotelCoupon', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取.用户优惠券列表
export const getUserCoupons = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getHotelCoupons', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取商家优惠券
export const getHotelCoupons = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/hotel/getHotelCoupons', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取我的会员信息
export const getMemberInfo = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}



//获取汉币充值记录
export const getMemberRechargeList = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberRechargeList', data, { custom: { ...setConfig(loadingText, showLoading) } })
}


//获取我的金额流水
export const getMemberCashLog = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/user/getMemberCashLog', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取充值套餐
export const getPackage = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getPackage', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//创建汉币充值订单
export const creatRechargeOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/creatRechargeOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//获取微信签名字符串
export const getJsSign = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/public/getJsSign', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//创建预支付订单
export const createPreOrderAndPrepay = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/createPreOrderAndPrepay', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//结束预支付订单
export const endPreOrder = (data = {}, loadingText = "加载中~", showLoading = true) => {
    return http.post('/waapi/order/endPreOrder', data, { custom: { ...setConfig(loadingText, showLoading) } })
}

// 检测是否在充电接口
export const searchBoardRemoteInfo = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/machine/searchBoardRemoteInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}
//是否在充电状态
export const getBoardRemoteInfo = (data = {}, loadingText = "加载中~", showLoading = false) => {
    return http.post('/waapi/machine/getBoardRemoteInfo', data, { custom: { ...setConfig(loadingText, showLoading) } })
}