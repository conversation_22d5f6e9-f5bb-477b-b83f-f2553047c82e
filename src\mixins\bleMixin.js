import ble from "@/utils/ble/newBle.js";
const app = getApp()
import { orderOutGoodsStatus } from "@/common/http/api.js";
export const bleMixin = {
    methods: {
        //初始化蓝牙
        initBt: function () {
            console.log('initBt,ble=', ble);
            ble.initBle();
        },
        //开始连接蓝牙设备
        startBleConnect() {
            this.initBt()
        },
        openBleLock(isAll, cancel, callback) {
            ble.openLock(isAll, cancel, callback || this.openLockCallback)
        },
        openLockCallback(flag) {
            let data = {
                order_sn: this.vCreateOrderInfo?.orderInfo?.order_sn,
                status: flag ? 1 : 0
            }
            orderOutGoodsStatus(data).then(res => {
                if (flag) {
                    this.isShowSuccess("出货成功", 0, () => {
                        uni.navigateTo({
                            url: `/pagesB/order/OrderDetails?order_sn=${this.vCreateOrderInfo?.orderInfo?.order_sn}`,
                        });
                    })
                } else {
                    this.isShowErr("出货失败~")
                }
            })
        },
        //充电
        onRecharge: async function (time, flag = false) {
            console.log('准备写入充电指令，time=', time)
            ble.rechargeDevice(time, flag ? () => { } : this.btRechargeCallback)
        },
        btRechargeCallback: function (flag) {
            console.log("🚀 ~ flag", flag)
            console.log("🚀 ~ btRechargeCallback  log  回调")
            if (flag) {
                uni.showToast({
                    title: "充电指令成功",
                    icon: "success",
                    duration: 1500,
                });
                // console.log("go   this.charge_sn", this.charge_sn);
                setTimeout(() => {
                    uni.redirectTo({
                        url: `/pagesB/chargeDetails/index?charge_sn=${this.charge_sn}`,
                    });
                }, 1500);
            } else {
                uni.showToast({
                    title: "充电指令执行失败~",
                    icon: "none",
                    duration: 2000,
                });
            }
        },

    },

}