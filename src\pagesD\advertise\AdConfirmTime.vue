<template>
    <view>
        <view class="container">
            <view class="list">
                <view class="list-item" v-for="(item, index) in deviceList" :key="index">
                    <view class="device-box">
                        <view>{{ item.device_sn }}</view>
                        <view>共投放{{ item.date.length || 0 }}天</view>
                        <view class="device-box-price">
                            {{
                                    ((item.price * 1000 * (item.date.length || 0)) / 1000).toFixed(
                                        2
                                    )
                            }}元</view>
                    </view>
                    <view class="device-time-box">
                        <view class="device-time" v-for="date in item.date" :key="date">
                            {{ date }}
                        </view>
                        <view class="device-time"></view>
                        <view class="device-time"></view>
                    </view>
                </view>
            </view>
        </view>

        <view class="fixed-btn flexRowBetween" :style="{ paddingBottom: 20 + iPhoneXBH + 'rpx' }">
            <view class="total">
                <view class="total-box">
                    <view>合计：</view>
                    <view class="total-box-mark">￥</view>
                    <view class="total-box-price">{{
                            (priceInfo.totalPrice || 0).toFixed(2)
                    }}</view>
                </view>
                <view class="total-num"> 共计{{ priceInfo.totalNum }}台设备 </view>
            </view>
            <view class="btn" @click="confirmOrder"> 确认提交 </view>
        </view>

        <!-- 占位块 -->
        <SafeBlock height="140" />
    </view>
</template>
<script>
import SafeBlock from "@/components/list/SafeBlock.vue";
import { createAdOrderAndPrepay } from "@/common/http/api";
import { payOrder } from "@/utils/pay"
export default {
    components: { SafeBlock },
    computed: {
        priceInfo() {
            let totalPrice = this.deviceList.reduce(
                (total, nowVal) =>
                    total + (nowVal.price * 1000 * (nowVal.date?.length ?? 0)) / 1000,
                0
            );
            let totalNum = this.deviceList?.length ?? 0;
            return {
                totalPrice,
                totalNum,
            };
        },
    },
    data() {
        return {
            formInfo: {},
            deviceList: [],
        };
    },
    methods: {
        confirmOrder() {
            let data = {
                ...this.formInfo,
            };
            let that = this
            createAdOrderAndPrepay(data).then((res) => {
                console.log("🚀 ~ res createAdOrderAndPrepay", res);
                let payInfo = res.data;
                console.log("🚀 ~ payInfo", payInfo);
                payOrder(this, payInfo, `/pagesB/advertiseOrder/index`);


            });
        },
    },
    onLoad(opt) {

        this.formInfo = JSON.parse(uni.getStorageSync("sub_use_form")) || {};


        this.deviceList = JSON.parse(uni.getStorageSync("sub_use_time")) || [];
    },

};
</script>

<style lang="scss">
page {
    background-color: #f2f2f2;
}
</style>

<style scoped lang="scss">
.container {
    padding: 20rpx;
}

.list {
    &-item {
        padding: 20rpx;
        border-radius: 20rpx;
        background-color: #fff;
        margin-bottom: 20rpx;

        .device-box {
            display: flex;
            justify-content: space-between;
            padding: 10rpx 0;
            border-bottom: 2rpx solid #999;

            &-price {
                color: red;
            }
        }

        .device-time-box {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .device-time {
                width: 30%;
                margin-top: 20rpx;
                font-size: $font-size-middle;
                color: #999;
            }
        }
    }
}

.fixed-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: $uni-bg-color;
    z-index: 99;

    .total {
        &-box {
            display: flex;
            align-items: flex-end;
            color: $textBlack;
            font-size: $font-size-base;

            &-mark {
                color: red;
                font-size: $font-size-xsmall;
            }

            &-price {
                color: red;
                font-size: $font-size-middle;
            }
        }

        &-num {
            color: $textDarkGray;
            font-size: $font-size-small;
        }
    }

    .btn {
        padding: 20rpx 50rpx;
        color: #fff;
        font-size: $font-size-xlarge;
        background: linear-gradient(268deg, #206bc5 0%, #0eade2 99%);
        border-radius: 20rpx;
    }
}
</style>
