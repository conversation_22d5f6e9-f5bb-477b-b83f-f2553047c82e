<template>
  <view class="balance">
    <view class="title">{{ title }}</view>
    <view class="valueList">
      <view
        class="valueItem flexColumnAllCenter"
        v-for="(item, index) in valueList"
        :key="index"
        :class="{ selectValueItem: selectValueIndex == index }"
        @click="onClickValueItem(index)"
      >
        <view class="price1">{{ item.money }}</view>
        <view class="price2">售价:{{ item.money }}元</view>
      </view>
      <view class="seize"></view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    valueList: Array,
    title: String,
  },
  data() {
    return {
      selectValueIndex: 0,
    };
  },
  methods: {
    onClickValueItem(i) {
      this.selectValueIndex = i;
      this.$emit("onClickValueItem", this.valueList[i]);
    },
  },
};
</script>

<style lang="scss" scoped>
.balance {
  padding: 60rpx 30rpx 0;

  .title {
    color: $textBlack;
    font-size: $font-size-xlarge;
    font-weight: 700;
    margin-bottom: 30rpx;
  }

  .valueList {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .valueItem {
      width: 30%;
      height: 120rpx;
      background-color: #fff;
      color: $themeColor;
      font-weight: bold;
      font-size: $font-size-xlarge;
      border-radius: 10rpx;
      border: 2rpx solid $themeColor;
      margin-bottom: 20rpx;

      .price2 {
        font-weight: 400;
        font-size: $font-size-xsmall;
      }
    }

    .seize {
      width: 30%;
    }

    .selectValueItem {
      background-color: $themeColor;
      color: white;
    }
  }
}
</style>