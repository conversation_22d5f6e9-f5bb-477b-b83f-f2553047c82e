<template>
  <u-icon :name="name" :size="size" :color="color" @click="onClick"></u-icon>
</template>

<script>
export default {
  name: "BaseIcon",
  props: {
    name: { type: String },
    size: { type: Number | String, default: 26 },
    color: {
      type: String,
      default: "#666",
    },
  },
  methods: {
    onClick() {
      this.$emit("onClick");
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
