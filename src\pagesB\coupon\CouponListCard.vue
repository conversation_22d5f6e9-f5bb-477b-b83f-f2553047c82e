<template>
    <view class='card' @click="handle(info)">
        <view class="content">
            <view class="content-top">
                <image class="content-top-img" :src="info.img" />
            </view>
            <view class="content-bottom">
                <view class="name">{{ info.name }}</view>
                <view class="type">
                    <u-tag :text="info.type" :plain="Math.random() > 0.5" :plainFill="Math.random() > 0.5" size="medium"
                        :type="Math.random() > 0.5 ? 'error' : 'primary'" shape="circle" @click="handle(info)" />
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    name: 'CouponListCard',
    props: {
        info: { type: Object, default: {} }
    },
    data() {
        return {

        };
    },

    methods: {
        handle(item) {
            // console.log("🚀 ~ item", item)
            if (item.appid && item.path) {
                uni.navigateToMiniProgram({
                    appId: item.appid,
                    path: item.path,
                    success: (result) => { console.log("🚀 ~ result", result) },

                    fail: (error) => { console.log("🚀 ~ error", error) }

                })
            } else {
                console.log("🚀 ~ item.url", item.url)
                uni.navigateTo({
                    url: `/pagesC/webView/WebView?url=${encodeURIComponent(item.url)}`,

                });
            }
        }
    },

}
</script>


<style scoped  lang='scss'>
.card {
    width: 100%;
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    background-color: #fff;
    box-sizing: border-box;
}

.content {
    &-top {
        height: 160rpx;

        &-img {
            width: 100%;
            height: 100%;
        }
    }

    &-bottom {
        .name {

            color: $textBlack;
            font-size: 28rpx;
            margin: 16rpx 0;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;

        }

        .type {
            display: inline-block;
        }
    }
}
</style>